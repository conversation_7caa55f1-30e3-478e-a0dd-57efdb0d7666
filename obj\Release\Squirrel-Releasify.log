﻿[20/05/25 21:20:03] info: Program: Starting Squirrel Updater: --releasify EasyWork.Honor.exe --releaseDir=E:\Wangxianqi\Release\Packing_2
[20/05/25 21:20:03] info: Program: Bootstrapper EXE found at:E:\<PERSON>xianqi\Project\Project_4\EasyWork.Honor\EasyWork\obj\Release\Setup.exe
[20/05/25 21:20:03] fatal: Finished with unhandled exception: System.IO.FileNotFoundException: 未能找到文件“EasyWork.Honor.exe”。
文件名:“EasyWork.Honor.exe”
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.File.InternalCopy(String sourceFileName, String destFileName, Boolean overwrite, Boolean checkHost)
   在 Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   在 Squirrel.Update.Program.executeCommandLine(String[] args)
   在 Squirrel.Update.Program.main(String[] args)

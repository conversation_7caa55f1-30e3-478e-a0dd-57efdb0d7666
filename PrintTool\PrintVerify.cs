﻿using DevComponents.DotNetBar;
using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.ServiceModel.Channels;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class PrintVerify : Form
    {
        public bool IsPasswordCorrect { get; private set; } //验证密码公共属性

        private string Password 
        { 
            get 
            {
                string Sql = "select * from BAI.dbo.version where rjname='CodePrintPass'";
                return SqlHelper.Query(Sql).Tables[0].Rows[0]["versionNo"]?.ToString();  //密码
            }
        }

        public PrintVerify(Form owner)
        {
            InitializeComponent();
            this.StartPosition = FormStartPosition.CenterParent;
            if (owner != null)
            {
                this.Owner = owner;

                this.MaximizeBox = false;

                this.MinimizeBox = false;
            }
            textBox1.PasswordChar = '*';
        }

        private void button1_Click()
        {
            IsPasswordCorrect = this.textBox1.Text.Trim() == Password;

            if (IsPasswordCorrect)
            {
                ShowMesssage("密码输入正确!", MessageType.Positive);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                ShowMesssage("密码输入错误!", MessageType.Negative);
                //MessageBox.Show("打印密码错误！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }

        /// <summary>
        /// 键盘键入回车
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void textBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button1_Click();
            }
        }

        private void ShowMesssage(string message, MessageType messageType)
        {
            switch (messageType)
            {
                case MessageType.Positive: 
                    label2.ForeColor = Color.Green; 
                    label2.Text = "成功: " + message;
                    label2.Font = new Font(label2.Font, FontStyle.Bold);
                    break;

                case MessageType.Negative: 
                    label2.ForeColor = Color.Red; 
                    label2.Text = "错误: " + message;
                    label2.Font = new Font(label2.Font, FontStyle.Bold | FontStyle.Underline);
                    break;

                case MessageType.Warning: 
                    label2.ForeColor = Color.Orange;
                    label2.Text = "警告: " + message;
                    label2.Font = new Font(label2.Font, FontStyle.Bold); 
                    break;

                case MessageType.Info:
                    label2.ForeColor = Color.Blue;
                    label2.Text = "信息: " + message;
                    label2.Font = new Font(label2.Font, FontStyle.Regular); 
                    break;
            }
        }

        private enum MessageType
        {
            Positive,
            Negative,
            Warning,
            Info
        }
    }
}

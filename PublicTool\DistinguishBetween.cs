﻿using EasyWork.Honor.Service.WXMES;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PublicTool
{
    public partial class DistinguishBetween : Form
    {
        string[] AText =new string[] { "", "", "", "", "", "", "", "", "" };
        string[] ALocation =new string[] { "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9" };
        int[] AQty =new int[] { 0,0,0,0,0,0,0,0,0};
        DataTable sntocode= new DataTable();
        public DistinguishBetween()
        {
            InitializeComponent();
            sntocode.Columns.Add("Code",typeof(string));
            sntocode.Columns.Add("TargetCode",typeof(string));
            sntocode.Columns.Add("Qty",typeof(string));
         

        }

        private void button1_Click(object sender, EventArgs e)
        {
            AText = new string[] { "", "", "", "", "", "", "", "", "" };
            AQty = new int[] { 0, 0, 0, 0, 0, 0, 0, 0, 0 };
            foreach (Control c in tabPage1.Controls)
            {
                if (c is GroupBox)
                {
                    foreach (Label label in c.Controls)
                    {
                            label.Text = "Normal" ;
                        
                    }
                }
            }
        }

        private void textBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {             

                for (int i = 0; i < AText.Length; i++)
                {
                    if (AText[i]=="")
                    {
                        AText[i] = textBox1.Text.Trim();
                        AQty[i]++;
                        foreach (Control c in tabPage1.Controls)
                        {
                            if (c is GroupBox)
                            {
                                foreach (Label label in c.Controls)
                                {
                                    if (label.Name == "la_A"+(i+1))
                                    {
                                        label.Text = textBox1.Text; ;
                                        la_Alocation.Text = $"{ALocation[i]}";
                                    }
                                    if (label.Name=="qty_A"+(i+1))
                                    {
                                        label.Text = AQty[i].ToString();
                                    }
                                }
                            }
                        }                      
                        return;

                    }
                    else if (textBox1.Text.Trim().ToUpper()==AText[i].ToUpper())
                    {
                        AQty[i]++;
                        foreach (Control c in tabPage1.Controls)
                        {
                            if (c is GroupBox)
                            {
                                foreach (Label label in c.Controls)
                                {
                                    if (label.Name == "qty_A" +( i+1))
                                    {
                                        la_Alocation.Text = $"{ALocation[i]}";
                                        label.Text = AQty[i].ToString();
                                    }
                                }
                            }
                        }
                        return;
                    }
                    else if(AText[8]!="")
                    {
                        la_Alocation.Text = $"超9个,放一边";
                        return;
                    }
                }
                textBox1.Text = "";
            }
        }

        private void tbSNToCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                la_SNToCode.Text = "";

                string data = tbSNToCode.Text;
                if (data.Trim().Length<8)
                {
                    tbSNToCode.Focus();
                    tbSNToCode.SelectAll();
                    return;
                }

                DataTable dt = WXMESQuery.QueryBasicData_New(tbSNToCode.Text.Trim());
                if (dt.Rows.Count==0)
                {
                    la_SNToCode.Text = "无MEID数据";
                    tbSNToCode.Focus();
                    tbSNToCode.SelectAll();
                    return;
                }
                data = dt.Rows[0]["targetcode"].ToString();
                if (sntocode.Rows.Count==0)
                {
                    DataRow dr1 = sntocode.NewRow();
                    dr1["code"] = "A1";
                    dr1["targetcode"] = data.Trim().ToUpper();
                    dr1["qty"] = "1";
                    la_SNToCode.Text = "A1";
                    sntocode.Rows.Add(dr1);                 
                }
                else
                {
                    foreach (DataRow row in sntocode.Rows)
                    {
                        if (data.Trim().ToUpper() == row["targetcode"].ToString().Trim().ToUpper())
                        {
                            row["qty"] = int.Parse(row["qty"].ToString()) + 1;
                            la_SNToCode.Text = row["code"].ToString();
                            return;
                        }
                    }
                    DataRow dr = sntocode.NewRow();
                    dr["code"] = "A" + sntocode.Rows.Count + 1;
                    la_SNToCode.Text = "A" + sntocode.Rows.Count + 1;
                    dr["targetcode"] = data.Trim().ToUpper();
                    dr["qty"] = "1";
                    sntocode.Rows.Add(dr);

                }  
                dgv_SNToCode.DataSource = sntocode;
                tbSNToCode.Focus();
                tbSNToCode.SelectAll();
                return;
            }
        }

        private void bt_sntocode_Click(object sender, EventArgs e)
        {
            sntocode = new DataTable();
            sntocode.Columns.Add("Code", typeof(string));
            sntocode.Columns.Add("TargetCode", typeof(string));
            sntocode.Columns.Add("Qty", typeof(string));
            dgv_SNToCode.DataSource = sntocode;
            tbSNToCode.Text = "";
            la_SNToCode.Text = "";
            tbSNToCode.Focus();
            tbSNToCode.SelectAll();
        }
    }
}

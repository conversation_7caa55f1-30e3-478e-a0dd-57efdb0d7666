﻿using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Management;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace EasyWork.bll.Login
{
    public class LoginService
    {
        public static string username;
        public static string idcard = "";
        /// <summary>
        /// 验证登陆信息
        /// </summary>
        /// <param name="user">登陆名</param>
        /// <returns>返回数据数量</returns>
        public static DataTable login(string user)
        {
            //string sql = "select a.usercode,b.username,a.state,a.pwd,b.IdentityCard from honor_mes.dbo.sys_users a left join Bai.dbo.PersonnelFile b on a.usercode=b.usercode where a.UserCode='" + user + "'";

            string sql = "select * from MES_Honor.dbo.sys_users where UserCode='" + user + "'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询人事档案
        /// </summary>
        /// <param name="usercode"></param>
        /// <returns></returns>
        public static DataTable QueryPersonnel(string usercode)
        {
            string sql = "select * from MES_Honor.dbo.Sys_UserPermission where usercode='" + usercode + "'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 本地IP地址
        /// </summary>
        /// <returns>返回本地IP地址</returns>
        [Obsolete]
        public static string ip()
        {
           IPAddress[] addressList = Dns.GetHostByName(Dns.GetHostName()).AddressList;
            return addressList[0].ToString();
        }

        /// <summary>  
        /// 获取本机MAC地址  
        /// </summary>  
        /// <returns>本机MAC地址</returns>  
        public static string GetMacAddress()
        {
            try
            {
                string strMac = string.Empty;
                ManagementClass mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                {
                    if ((bool)mo["IPEnabled"] == true)
                    {
                        strMac = mo["MacAddress"].ToString();
                    }
                }
                moc = null;
                mc = null;
                return strMac;
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// 获取Windows系统版本
        /// </summary>
        /// <returns></returns>
        public static string GetWindowsVer()
        {
            string _operatingSystem = "";
            string _osArchitecture = "";
            using (ManagementObjectSearcher win32OperatingSystem = new ManagementObjectSearcher("select * from Win32_OperatingSystem"))
            {
                foreach (ManagementObject obj in win32OperatingSystem.Get())
                {
                    _operatingSystem = obj["Caption"].ToString();
                    _osArchitecture = obj["OSArchitecture"].ToString();
                    break;
                }
            }
            return _operatingSystem + " " + _osArchitecture;
        }

        /// <summary>
        /// 添加获电脑CPU信息的方法
        /// </summary>
        /// <returns></returns>
        public static string GetProcessor()
        {
            List<string> lst = new List<string>();
            ManagementClass mc = new ManagementClass("Win32_Processor");
            ManagementObjectCollection moc = mc.GetInstances();

            foreach (ManagementObject mo in moc)
            {
                return mo["name"].ToString();                
            }
            return "";
        }

        ///   <summary> 
        ///   获取cpu序列号     
        ///   </summary> 
        ///   <returns> string </returns> 
        public static string GetCpuInfo()
        {
            string cpuInfo = " ";
            using (ManagementClass cimobject = new ManagementClass("Win32_Processor"))
            {
                ManagementObjectCollection moc = cimobject.GetInstances();

                foreach (ManagementObject mo in moc)
                {
                    cpuInfo = mo.Properties["ProcessorId"].Value.ToString();
                    mo.Dispose();
                }
            }
            return cpuInfo.ToString();
        }

        /// <summary>
        /// 获取内存大小
        /// </summary>
        /// <returns></returns>
        public static string GetMemory()
        {
            ManagementClass mc = new ManagementClass("Win32_PhysicalMemory");
            ManagementObjectCollection moc = mc.GetInstances();
            double capacity = 0;
            foreach (ManagementObject mo in moc)
            {
                capacity += ((Math.Round(Int64.Parse(mo.Properties["Capacity"].Value.ToString()) / 1024 / 1024 / 1024.0, 0)));
            }
            return capacity.ToString() + "G";
        }

        /// <summary>
        /// 获取硬盘信息
        /// </summary>
        /// <returns></returns>
        public static string GetDisk()
        {
            ManagementClass mc = new ManagementClass("win32_DiskDrive");
            ManagementObjectCollection moc = mc.GetInstances();
            string hdd = "";
            foreach (ManagementObject mo in moc)
            {
                if (hdd == "")
                {
                    hdd += mo["Model"].ToString().Split(' ')[0] + "-" + ((Math.Round(Int64.Parse(mo.Properties["size"].Value.ToString()) / 1000 / 1000 / 1000.0, 0))) + "G";
                }
                else
                {
                    hdd += " " + mo["Model"].ToString().Split(' ')[0] + "-" + ((Math.Round(Int64.Parse(mo.Properties["size"].Value.ToString()) / 1000 / 1000 / 1000.0, 0))) + "G";
                }              
            }
            return hdd;
        }

        /// <summary>  
        /// 获取本机所有MAC地址  
        /// </summary>  
        /// <returns>本机MAC地址</returns>  
        public static string GetAllMacAddress()
        {
            try
            {
                string strMac = "";
                ManagementClass mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                {
                    if ((bool)mo["IPEnabled"] == true)
                    {
                        if (strMac=="")
                        {
                            strMac = mo["MacAddress"].ToString();
                        }
                        else
                        {
                            strMac +=","+ mo["MacAddress"].ToString();
                        }                       
                    }
                }
                moc = null;
                mc = null;
                return strMac;
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>  
        /// 获取操作系统名称  
        /// </summary>  
        /// <returns>操作系统名称</returns>  
        public static string GetSystemName()
        {
            try
            {
                return Dns.GetHostEntry("localhost").HostName;
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// 用户是否有权限
        /// </summary>
        /// <param name="usercode">登陆名</param>
        /// <param name="per">需要使用的窗口或按钮权限名</param>
        /// <returns>返回数据量</returns>
        public static DataTable Permission(string usercode)
        {
            string sql = "select * from userquanxian where usercode='" + usercode + "' and type='OA'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            return dt;
        }

        /// <summary>
        /// 好友列表
        /// </summary>
        /// <returns>返回好友列表</returns>
        public static int IM_Permission(string usercode)
        {
            string sql = "select usercode from userquanxian where qxname='im' and usercode='" + usercode + "'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            return dt.Rows.Count;
        }

        /// <summary>
        /// 查询权限列表
        /// </summary>
        /// <returns></returns>
        public static DataTable quanxianList()
        {
            string sql = "select * from quanxianList";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 插入新用户信息
        /// </summary>
        /// <param name="usercode"></param>
        /// <param name="pwd"></param>
        /// <returns></returns>
        public static int InsertUserLogin(string usercode, string pwd)
        {
            string sql = "insert into MES_Honor.dbo.Sys_Users(usercode,PassWord,UserState) values('" + usercode + "','" + pwd + "','正常')";
            return SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 更新用户的登陆密码
        /// </summary>
        /// <param name="usercode"></param>
        /// <param name="pwd"></param>
        /// <returns></returns>
        public static int UpdateUserPwd(string usercode, string pwd)
        {
            string sql = "update MES_Honor.dbo.Sys_Users set PassWord='" + pwd + "' where usercode='" + usercode + "'";
            return SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 查询修改过的旧密码
        /// </summary>
        /// <param name="usercode"></param>
        /// <returns></returns>
        public static DataTable QueryOldPwd(string usercode)
        {
            string sql = "select * from MES_Honor.dbo.Sys_UserPwdLog where usercode='" + usercode + "' order by id desc";
            return SqlHelper.Query(sql).Tables[0];
        }

        public static DataTable QueryFLCode(string mac)
        {
            string sql = "select * from ERP.dbo.fl_cangku where mac='"+mac+"'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 修改电脑名称
        /// </summary>
        /// <param name="pcname"></param>
        public static void ChangePCName(string pcname)
        {
            string[] mac = GetAllMacAddress().Split(',');
            foreach (object item in mac)
            {
                string mc = item.ToString().Replace(":","").Replace(":", "").Replace(":", "").Replace(":", "").Replace(":", "");
                DataTable dt = QueryFLCode(mc);
                if (dt.Rows.Count > 0)
                {
                    if (pcname.Split('.')[0] != dt.Rows[0]["code"].ToString())
                    {
                        while (true)
                        {
                            int i= WinAPI.apiSetComputerNameEx(5, dt.Rows[0]["code"].ToString());
                            if (i==0)
                            {
                                MessageBox.Show("修改电脑名失败,请用管理员权限运行");
                            }
                            else
                            {
                                MessageBox.Show("修改电脑名成功,请重新启动电脑!");
                                break;
                            }
                        }
                    }
                    
                }
            }
        }
    }
}

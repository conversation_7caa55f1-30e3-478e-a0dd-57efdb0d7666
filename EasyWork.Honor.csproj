﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\squirrel.windows.2.0.1\build\squirrel.windows.props" Condition="Exists('..\packages\squirrel.windows.2.0.1\build\squirrel.windows.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EB09791A-FB3E-40BA-8360-8C15C1AF71A1}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>EasyWork.Honor</RootNamespace>
    <AssemblyName>EasyWork.Honor</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>E:\Wangxianqi\Release\Packing_2\EasyWork.Honor\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>8</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>dog_128px_1189450_easyicon.net.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>7497BE48223C465565BE7B0AB48F6B4B60B4429D</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>EasyWork.Honor_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="AForge, Version=2.2.5.0, Culture=neutral, PublicKeyToken=c1db6ff4eaa06aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Controls, Version=2.2.5.0, Culture=neutral, PublicKeyToken=a8ac264d1dc6b9d9, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.Controls.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Imaging, Version=2.2.5.0, Culture=neutral, PublicKeyToken=ba8ddea9676ca48b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Math, Version=2.2.5.0, Culture=neutral, PublicKeyToken=abba2e25397ee8c9, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.Math.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video, Version=2.2.5.0, Culture=neutral, PublicKeyToken=cbfb6e07d173c401, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow, Version=2.2.5.0, Culture=neutral, PublicKeyToken=61ea4348d43881b7, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Core, Version=1.44.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.44.1\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="DeltaCompressionDotNet, Version=1.1.0.0, Culture=neutral, PublicKeyToken=1d14d6e5194e7f4a, processorArchitecture=MSIL">
      <HintPath>..\packages\DeltaCompressionDotNet.1.1.0\lib\net20\DeltaCompressionDotNet.dll</HintPath>
    </Reference>
    <Reference Include="DeltaCompressionDotNet.MsDelta, Version=1.1.0.0, Culture=neutral, PublicKeyToken=46b2138a390abf55, processorArchitecture=MSIL">
      <HintPath>..\packages\DeltaCompressionDotNet.1.1.0\lib\net20\DeltaCompressionDotNet.MsDelta.dll</HintPath>
    </Reference>
    <Reference Include="DeltaCompressionDotNet.PatchApi, Version=1.1.0.0, Culture=neutral, PublicKeyToken=3e8888ee913ed789, processorArchitecture=MSIL">
      <HintPath>..\packages\DeltaCompressionDotNet.1.1.0\lib\net20\DeltaCompressionDotNet.PatchApi.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2">
      <HintPath>bin\Debug\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="Interop.Microsoft.Office.Core">
      <HintPath>bin\Debug\Interop.Microsoft.Office.Core.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.Microsoft.Office.Interop.Excel">
      <HintPath>bin\Debug\Interop.Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Memory, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Memory.9.0.0\lib\net462\Microsoft.Bcl.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.TimeProvider.8.0.1\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Build.Engine" />
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=2.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.2.1.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Graph, Version=5.81.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Graph.5.81.0\lib\netstandard2.0\Microsoft.Graph.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Graph.Core, Version=3.2.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Graph.Core.3.2.4\lib\net462\Microsoft.Graph.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.8.6.1\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.8.6.1\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.8.6.1\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.8.6.1\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.8.6.1\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.8.6.1\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Validators, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Validators.8.6.1\lib\net472\Microsoft.IdentityModel.Validators.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Abstractions, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Abstractions.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Authentication.Azure, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Authentication.Azure.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Authentication.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Http.HttpClientLibrary, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Http.HttpClientLibrary.1.17.1\lib\net462\Microsoft.Kiota.Http.HttpClientLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Form, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Form.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Form.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Json, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Json.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Multipart, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Multipart.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Multipart.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Kiota.Serialization.Text, Version=********, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Kiota.Serialization.Text.1.17.1\lib\netstandard2.0\Microsoft.Kiota.Serialization.Text.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e, processorArchitecture=MSIL">
      <HintPath>..\packages\Mono.Cecil.0.11.2\lib\net40\Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil.Mdb, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e, processorArchitecture=MSIL">
      <HintPath>..\packages\Mono.Cecil.0.11.2\lib\net40\Mono.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil.Pdb, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e, processorArchitecture=MSIL">
      <HintPath>..\packages\Mono.Cecil.0.11.2\lib\net40\Mono.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil.Rocks, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e, processorArchitecture=MSIL">
      <HintPath>..\packages\Mono.Cecil.0.11.2\lib\net40\Mono.Cecil.Rocks.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NuGet.Squirrel, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\squirrel.windows.2.0.1\lib\Net45\NuGet.Squirrel.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="Selenium.WebDriverBackedSelenium">
      <HintPath>E:\workspace\Bravo\trunk\Bravo\bin\Debug\Selenium.WebDriverBackedSelenium.dll</HintPath>
    </Reference>
    <Reference Include="SharpCompress, Version=0.17.1.0, Culture=neutral, PublicKeyToken=afb0a02973931d96, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpCompress.0.17.1\lib\net45\SharpCompress.dll</HintPath>
    </Reference>
    <Reference Include="SideBar">
      <HintPath>E:\workspace\Bravo\trunk\Bravo\bin\Debug\SideBar.dll</HintPath>
    </Reference>
    <Reference Include="Squirrel, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\squirrel.windows.2.0.1\lib\Net45\Squirrel.dll</HintPath>
    </Reference>
    <Reference Include="Std.UriTemplate, Version=2.0.1.0, Culture=neutral, PublicKeyToken=c118b0afb4598f9a, processorArchitecture=MSIL">
      <HintPath>..\packages\Std.UriTemplate.2.0.1\lib\netstandard2.0\Std.UriTemplate.dll</HintPath>
    </Reference>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>..\..\..\vs2017_Projects\TwoReturn\TwoReturn\bin\Debug\stdole.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.1.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.1.0\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.6.0.2\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=8.6.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.8.6.1\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.6.0.0\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WinHttpHandler, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.WinHttpHandler.6.0.0\lib\net461\System.Net.Http.WinHttpHandler.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.4.5.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WebDriver">
      <HintPath>bin\Debug\WebDriver.dll</HintPath>
    </Reference>
    <Reference Include="WebDriver.Support">
      <HintPath>E:\workspace\Bravo\trunk\Bravo\bin\Debug\WebDriver.Support.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ClientPlugins\HHTPlugins.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ClientPlugins\HHTPlugins.Designer.cs">
      <DependentUpon>HHTPlugins.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\GZChuHuoPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\GZChuHuoPrint.Designer.cs">
      <DependentUpon>GZChuHuoPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\CMESWorkPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\CMESWorkPrint.Designer.cs">
      <DependentUpon>CMESWorkPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\PrintVerify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\PrintVerify.Designer.cs">
      <DependentUpon>PrintVerify.cs</DependentUpon>
    </Compile>
    <Compile Include="ProgressForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProgressForm.Designer.cs">
      <DependentUpon>ProgressForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PublicTool\BOMTool.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PublicTool\BOMTool.Designer.cs">
      <DependentUpon>BOMTool.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\Public\AppUpdate.cs" />
    <Compile Include="Service\SqlHelperMES.cs" />
    <Compile Include="WXMES\AutoReadILookLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\AutoReadILookLog.Designer.cs">
      <DependentUpon>AutoReadILookLog.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\AutoReadWXReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\AutoReadWXReport.Designer.cs">
      <DependentUpon>AutoReadWXReport.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\PCDataValJob.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\PCDataValJob.designer.cs">
      <DependentUpon>PCDataValJob.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\WXFenPei.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\WXFenPei.Designer.cs">
      <DependentUpon>WXFenPei.cs</DependentUpon>
    </Compile>
    <Compile Include="Tool\AddZPLSeripts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Tool\AddZPLSeripts.Designer.cs">
      <DependentUpon>AddZPLSeripts.cs</DependentUpon>
    </Compile>
    <Compile Include="Tool\MergeBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Tool\MergeBox.Designer.cs">
      <DependentUpon>MergeBox.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\ColorBoxPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\ColorBoxPrint.Designer.cs">
      <DependentUpon>ColorBoxPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\BoxNoPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\BoxNoPrint.Designer.cs">
      <DependentUpon>BoxNoPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\ChuHuoPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\ChuHuoPrint.Designer.cs">
      <DependentUpon>ChuHuoPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\NetCodeMeidPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\NetCodeMeidPrint.Designer.cs">
      <DependentUpon>NetCodeMeidPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\CheckRportPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\CheckRportPrint.Designer.cs">
      <DependentUpon>CheckRportPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\WXPsidWorkPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\WXPsidWorkPrint.Designer.cs">
      <DependentUpon>WXPsidWorkPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintTool\ZPLPublicPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\ZPLPublicPrint.Designer.cs">
      <DependentUpon>ZPLPublicPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="PublicTool\DistinguishBetween.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PublicTool\DistinguishBetween.Designer.cs">
      <DependentUpon>DistinguishBetween.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\ClientPlugins\HHTService.cs" />
    <Compile Include="Service\domain.cs" />
    <Compile Include="Service\Login\LoginService.cs" />
    <Compile Include="Service\Print\PrintService.cs" />
    <Compile Include="Service\Print\RawPrinterHelper.cs" />
    <Compile Include="Service\Print\ZebraGesigner.cs" />
    <Compile Include="Service\Print\ZPLPrint.cs" />
    <Compile Include="Service\Public\PublicService.cs" />
    <Compile Include="Service\Public\BarCode.cs" />
    <Compile Include="Service\Public\ExportToExcel.cs" />
    <Compile Include="Service\Public\GetOSystem.cs" />
    <Compile Include="Service\Public\QqHelper.cs" />
    <Compile Include="Service\Public\Win32.cs" />
    <Compile Include="Service\SqlHelperBAI.cs" />
    <Compile Include="Service\SqlHelper.cs" />
    <Compile Include="Service\Public\WinAPI.cs" />
    <Compile Include="Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Login.Designer.cs">
      <DependentUpon>Login.cs</DependentUpon>
    </Compile>
    <Compile Include="Manage\ChangePWD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Manage\ChangePWD.Designer.cs">
      <DependentUpon>ChangePWD.cs</DependentUpon>
    </Compile>
    <Compile Include="MDI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MDI.Designer.cs">
      <DependentUpon>MDI.cs</DependentUpon>
    </Compile>
    <Compile Include="Model\WXProcedure.cs" />
    <Compile Include="Model\ControlProcessList.cs" />
    <Compile Include="Model\Results.cs" />
    <Compile Include="Model\TopicList.cs" />
    <Compile Include="Model\PagingList.cs" />
    <Compile Include="PrintTool\MingPaiPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintTool\MingPaiPrint.Designer.cs">
      <DependentUpon>MingPaiPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service\WXMES\WXMESPublic.cs" />
    <Compile Include="Service\WXMES\WXMESQuery.cs" />
    <Compile Include="ShowMsg\HHT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShowMsg\HHT.Designer.cs">
      <DependentUpon>HHT.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\DataValJob.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\DataValJob.designer.cs">
      <DependentUpon>DataValJob.cs</DependentUpon>
    </Compile>
    <Compile Include="WXMES\WXMESDataMerge.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WXMES\WXMESDataMerge.Designer.cs">
      <DependentUpon>WXMESDataMerge.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="ClientPlugins\HHTPlugins.resx">
      <DependentUpon>HHTPlugins.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Login.resx">
      <DependentUpon>Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Manage\ChangePWD.resx">
      <DependentUpon>ChangePWD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MDI.resx">
      <DependentUpon>MDI.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\GZChuHuoPrint.resx">
      <DependentUpon>GZChuHuoPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\CMESWorkPrint.resx">
      <DependentUpon>CMESWorkPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\PrintVerify.resx">
      <DependentUpon>PrintVerify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PublicTool\BOMTool.resx">
      <DependentUpon>BOMTool.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\AutoReadILookLog.resx">
      <DependentUpon>AutoReadILookLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\AutoReadWXReport.resx">
      <DependentUpon>AutoReadWXReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\PCDataValJob.resx">
      <DependentUpon>PCDataValJob.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\WXFenPei.resx">
      <DependentUpon>WXFenPei.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Tool\AddZPLSeripts.resx">
      <DependentUpon>AddZPLSeripts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Tool\MergeBox.resx">
      <DependentUpon>MergeBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\ColorBoxPrint.resx">
      <DependentUpon>ColorBoxPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\BoxNoPrint.resx">
      <DependentUpon>BoxNoPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\ChuHuoPrint.resx">
      <DependentUpon>ChuHuoPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\MingPaiPrint.resx">
      <DependentUpon>MingPaiPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\NetCodeMeidPrint.resx">
      <DependentUpon>NetCodeMeidPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\CheckRportPrint.resx">
      <DependentUpon>CheckRportPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\WXPsidWorkPrint.resx">
      <DependentUpon>WXPsidWorkPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintTool\ZPLPublicPrint.resx">
      <DependentUpon>ZPLPublicPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="PublicTool\DistinguishBetween.resx">
      <DependentUpon>DistinguishBetween.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShowMsg\HHT.resx">
      <DependentUpon>HHT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\DataValJob.resx">
      <DependentUpon>DataValJob.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WXMES\WXMESDataMerge.resx">
      <DependentUpon>WXMESDataMerge.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="EasyWork.Honor.nuspec" />
    <None Include="EasyWork.Honor_TemporaryKey.pfx" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="dog_128px_1189450_easyicon.net.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\squirrel.windows.2.0.1\build\squirrel.windows.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\squirrel.windows.2.0.1\build\squirrel.windows.props'))" />
  </Target>
</Project>
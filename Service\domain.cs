﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.bll.Public
{
    public static class domain
    {
        public static string AddDomain()
        {
            string userName = "read";
            //MessageBox.Show(userName);
            string domainName = "bravotong.com";
            string pwd = "123456";
            string dns = "************";
            string message;
            //修改DNS
            SetDNS(new string[] { dns });
            int message1 = SetDomainMembership2(domainName, userName, pwd, out message);
            SetDNS(new string[] { "**************", "*************", "************" });
            return message;
        }

        public static string DomainName()
        {
            SelectQuery query = new SelectQuery("Win32_ComputerSystem");
            using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(query))
            {
                foreach (ManagementObject mo in searcher.Get())
                {
                    if ((bool)mo["partofdomain"])
                        return mo["domain"].ToString();
                }
            }
            return "";
        }

        public static int SetDomainMembership2(string DomainName, string UserName, string Password, out string err)
        {
            err = string.Empty;
            try
            {
                string DomainNameHost = DomainName;
                uint value1 = NetJoinDomain(null, DomainNameHost, null, UserName + "@" + DomainName, Password, (JoinOptions.NETSETUP_JOIN_DOMAIN | JoinOptions.NETSETUP_DOMAIN_JOIN_IF_JOINED | JoinOptions.NETSETUP_ACCT_CREATE));
                err = value1.ToString();
                return Convert.ToInt32(value1);
            }
            catch (Exception e)
            {
                err = e.ToString();
                return -1;
            }
        }

        [DllImport("netapi32.dll", CharSet = CharSet.Unicode)]
        static extern uint NetJoinDomain(
          string lpServer,
          string lpDomain,
          string lpAccountOU,
          string lpAccount,
          string lpPassword,
          JoinOptions NameType);

        [Flags]
        enum JoinOptions
        {
            NETSETUP_JOIN_DOMAIN = 0x00000001,
            NETSETUP_ACCT_CREATE = 0x00000002,
            NETSETUP_ACCT_DELETE = 0x00000004,
            NETSETUP_WIN9X_UPGRADE = 0x00000010,
            NETSETUP_DOMAIN_JOIN_IF_JOINED = 0x00000020,
            NETSETUP_JOIN_UNSECURE = 0x00000040,
            NETSETUP_MACHINE_PWD_PASSED = 0x00000080,
            NETSETUP_DEFER_SPN_SET = 0x10000000
        }

        public static void SetDNS(string[] domainDNS)
        {

            //获取本机的IP地址和网关
            ManagementObjectCollection moc = GetLocalIPAndGateway();
            ManagementBaseObject inPar = null;
            ManagementBaseObject outPar = null;

            foreach (ManagementObject mo in moc)
            {
                //如果没有启用IP设置的网络设备则跳过
                if (!(bool)mo["IPEnabled"])
                {
                    continue;
                }
                if (domainDNS != null)
                {
                    //设置DNS  
                    inPar = mo.GetMethodParameters("SetDNSServerSearchOrder");
                    // 1.DNS 2.备用DNS 
                    inPar["DNSServerSearchOrder"] = domainDNS;

                    outPar = mo.InvokeMethod("SetDNSServerSearchOrder", inPar, null);
                }
            }
        }



        public static ManagementObjectCollection GetLocalIPAndGateway()
        {
            ManagementClass wmi = new ManagementClass("Win32_NetworkAdapterConfiguration");
            ManagementObjectCollection moc = wmi.GetInstances();
            return moc;
        }
    }
}

﻿using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PublicTool
{
    public partial class BOMTool : Form
    {
        DataTable dt = null;
        public BOMTool()
        {
            InitializeComponent();
            this.TopMost = true;
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            if (textBox1.Text.Trim().Length==8)
            {
                QueryBOM();
            }
        }

        void QueryBOM()
        {

            string sql = "select * from MES_Honor.dbo.fx_cangku where material='"+textBox1.Text+"'";
            if (dt == null)
            {
                dt = SqlHelper.Query(sql).Tables[0];
                if (dt.Rows.Count==0)
                {
                    MessageBox.Show("翻新仓库没有该物料编码");
                    return;
                }
                dataGridView1.DataSource = dt;
                textBox1.Text = "";
                textBox1.Focus();                
            }
            else
            {
                DataTable dtt = SqlHelper.Query(sql).Tables[0];
                if (dtt.Rows.Count == 0)
                {
                    MessageBox.Show("翻新仓库没有该物料编码");
                    return;
                }
                if (dt.Select("material='"+dtt.Rows[0]["material"].ToString()+"'").Length>0)
                {
                    MessageBox.Show("表格已有该物料编码");
                    return;
                }
                dt.Merge(dtt, false, MissingSchemaAction.AddWithKey);
                dataGridView1.DataSource = dt;
                textBox1.Text = "";
                textBox1.Focus();
            }    
        }

        private void button2_Click(object sender, EventArgs e)
        {
            dt = null;
            dataGridView1.DataSource = null;
            textBox1.Text = "";
            textBox1.Focus();
        }
    }
}

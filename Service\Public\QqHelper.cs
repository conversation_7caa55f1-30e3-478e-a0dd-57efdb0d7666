﻿using System;
using Accessibility;
using MessengerHelper;

namespace QqHelper
{
    /// <summary>
    /// 对Messenger窗口进行操作
    /// </summary>
    public class QqWindowHelper
    {
        IntPtr _QqWindowHandle;
        string _winTitle;
        IAccessible _inputBox;

        public QqWindowHelper(IntPtr windowHandle, String winTitle)
        {
            _QqWindowHandle = windowHandle;
            _winTitle = winTitle;

            GetAccessibleObjects(_QqWindowHandle, out _inputBox);
        }

        /// <summary>
        /// 返回消息框内容
        /// </summary>
        /// <returns></returns>
        public string GetContent()
        {
            string value = (string)_inputBox.get_accValue(Win32.CHILDID_SELF);
            return value;
        }

        private IAccessible[] GetAccessibleChildren(IAccessible paccContainer)
        {
            IAccessible[] rgvarChildren = new IAccessible[paccContainer.accChildCount];
            int pcObtained;
            Win32.AccessibleChildren(paccContainer, 0, paccContainer.accChildCount, rgvar<PERSON>hildren, out pcObtained);
            return rgvarChildren;
        }
        //按层级找到对象
        public IAccessible GetAccessibleChild(IAccessible paccContainer, int[] array)
        {
            if (array.Length > 0)
            {
                IAccessible result = GetAccessibleChildren(paccContainer)[array[0]];

                int[] array_1 = new int[array.Length - 1];
                for (int i = 0; i < array.Length - 1; i++)
                {
                    array_1[i] = array[i + 1];
                }
                return GetAccessibleChild(result, array_1);
            }
            else
            {
                return paccContainer;
            }
        }

        private void GetAccessibleObjects(System.IntPtr imWindowHwnd, out IAccessible inputBox)
        {
            Guid guidCOM = new Guid(0x618736E0, 0x3C3D, 0x11CF, 0x81, 0xC, 0x0, 0xAA, 0x0, 0x38, 0x9B, 0x71);
            Accessibility.IAccessible IACurrent = null;

            Win32.AccessibleObjectFromWindow(imWindowHwnd, (int)Win32.OBJID_CLIENT, ref guidCOM, ref IACurrent);
            IACurrent = (IAccessible)IACurrent.accParent;
            inputBox = null;
            inputBox = GetAccessibleChild(IACurrent, new int[] { 3, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 3, 0 });

            //int childCount = IACurrent.accChildCount;
            //object[] windowChildren = new object[childCount];
            //int pcObtained;
            //Win32.AccessibleChildren(IACurrent, 0, childCount, windowChildren, out pcObtained);

            //foreach (IAccessible child in windowChildren)
            //{
            // if (child.get_accName(Win32.CHILDID_SELF) == _winTitle)
            // {
            // inputBox = GetAccessibleChild(child, new int[] { 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 3, 0 });

            // break;
            // }
            //}
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Honor.Service.ClientPlugins
{
    public static class HHTService
    {
        /// <summary>
        /// 查询是否有相同的PN
        /// </summary>
        /// <param name="PN"></param>
        /// <returns></returns>
        public static bool Exists_PN(string PN)
        {
            string sql = "select count(1) from MES_Honor.dbo.HHT where PN='" + PN+"'";
            return int.Parse(SqlHelper.Query(sql).Tables[0].Rows[0][0].ToString()) > 0 ? true : false;
        }

        /// <summary>
        /// 更新火火兔的数据
        /// </summary>
        /// <param name="PN"></param>
        /// <param name="QRCode"></param>
        /// <param name="Model"></param>
        /// <param name="Mac"></param>
        /// <param name="Location"></param>
        /// <returns></returns>
        public static int UpdateHHT_PN(string PN,string QRCode,string Code,string Model,string Mac,string Location)
        {
            string sql = "update MES_Honor.dbo.HHT set QRcode='" + QRCode+ "',Model='"+Model+ "',Mac='"+Mac+ "',Location='"+Location+"',Code='"+Code+"' where PN='"+PN+"'";
            return SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 插入火火兔的数据
        /// </summary>
        /// <param name="PN"></param>
        /// <param name="QRCode"></param>
        /// <param name="Model"></param>
        /// <param name="Mac"></param>
        /// <param name="Location"></param>
        /// <returns></returns>
        public static int Insert(string PN,string QRCode,string Code, string Model, string Mac, string Location)
        {
            string sql = "insert into MES_Honor.dbo.HHT(PN,QRCode,Code,Model,Mac,Location,CreateById,CreateTime) values('" + PN+ "','"+QRCode+ "','"+Code+"','"+Model+ "','"+Mac+ "','"+Location+ "','"+Login.usercode+ "',getdate())";
            return SqlHelper.ExecuteSql(sql);
        }
    }
}

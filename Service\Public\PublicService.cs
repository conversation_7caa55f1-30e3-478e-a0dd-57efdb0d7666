﻿ using EasyWork.bll.Public;
using EasyWork.Model;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using System.Xml;

namespace EasyWork.Honor.Service
{
    public class PublicService
    {
        /// <summary>
        /// 存储过程,分页过程
        /// </summary>
        /// <param name="paging">PagingList集合类</param>
        /// <returns>返回分页结果集</returns>
        public static DataSet paging(PagingList paging)
        {
            SqlParameter[] pre = new SqlParameter[]{
               new SqlParameter("@TableList",paging.tablelist),//搜索表的字段,比如：’id，datatime，job‘，用逗号隔开
               new SqlParameter("@TableName",paging.tablename),//搜索的表名
               new SqlParameter("@where",paging.selectwhere),//搜索条件，这里不用写where，比如：job=’teacher‘and class='2'
               new SqlParameter("@order",paging.orderid),//排序，可以使用多字段排序但主键字段必需在最前面.也可以不写，比如：order by class asc
               new SqlParameter("@intPageNo",paging.pageno),//页号,第几页
               new SqlParameter("@intPageSize",paging.pagesize)
           };
            DataSet ds = SqlHelper.RunProcedure("pro_Paging", pre, "table");
            return ds;
        }

        public static float version(string ver)
        {

            string[] str = ver.Split('.');
            ver = str[0] + "." + str[1] + str[2] + str[3];
            return float.Parse(ver);
        }
      

        /// <summary>
        /// 查询软件版本号是否和服务一致
        /// </summary>
        /// <returns>返回结果</returns>
        public static float QueryVersion()
        {
            string sql = "select version from version where item='EasyWork'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                string str = dt.Rows[0]["version"].ToString();
                return float.Parse(str);
            }
            return 0;
        }


        public static int SaveLog(string usercode, string username, string ver, string operation, string quanxian, string pagename, string mac, string pcname, string ip, string batch,string winver,string cpu,string hdd,string memory)
        {
            string domname = domain.DomainName();
            string sql = "insert into login_log(usercode,username,ver,intime,type,operation,quanxian,pagename,mac,pcname,ip,batch,remarks,winver,cpu,hdd,memory) values('" + usercode + "','" + username + "','" + ver + "',getdate(),'EasyWork','" + operation + "','" + quanxian + "','" + pagename + "','" + mac + "','" + pcname + "','" + ip + "','" + batch + "','"+domname+ "','"+winver+"','"+cpu+"','"+hdd+"','"+memory+"')";
            return SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 查询服务器的时间
        /// </summary>
        /// <returns>返回时间</returns>
        public static DateTime serverTime()
        {
            string sql = "select getdate() as time";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            return Convert.ToDateTime(dt.Rows[0][0].ToString());
        }

        /// <summary>
        /// 查询目标时间是星期几
        /// </summary>
        /// <param name="date">目标时间</param>
        /// <returns>返回星期几</returns>
        public static string dayofweek(DateTime date)
        {
            string weekstr = date.DayOfWeek.ToString();
            switch (weekstr)
            {
                case "Monday": weekstr = "星期一"; break;
                case "Tuesday": weekstr = "星期二"; break;
                case "Wednesday": weekstr = "星期三"; break;
                case "Thursday": weekstr = "星期四"; break;
                case "Friday": weekstr = "星期五"; break;
                case "Saturday": weekstr = "星期六"; break;
                case "Sunday": weekstr = "星期日"; break;
            }
            return weekstr;
        }

        /// <summary>
        /// 根据目标时间计算得出目标时间的周一
        /// </summary>
        /// <param name="date">目标时间</param>
        /// <returns>返回周一时间</returns>
        public static DateTime startWeek(DateTime date)
        {
            DateTime startWeek = date.AddDays(1 - Convert.ToInt32(date.DayOfWeek.ToString("d")));  //目标时间的周一日期
            return startWeek;
        }

        /// <summary>
        /// 根据计算得出周一的时间,再计算得出周日时间
        /// </summary>
        /// <param name="startWeek">周一的时间</param>
        /// <returns>返回周日时间</returns>
        public static DateTime endWeek(DateTime startWeek)
        {
            DateTime endWeek = startWeek.AddDays(6);  //计算后的周一再计算得出的周日日期
            return endWeek;
        }

        /// <summary>
        /// 目标时间的上个月1号
        /// </summary>
        /// <param name="datetime">目标时间</param>
        /// <returns>返回上个月1号</returns>
        public static DateTime LastMonthHead(DateTime datetime)
        {
            return DateTime.Parse(datetime.ToString("yyyy-MM-01")).AddMonths(-1);
        }

        /// <summary>
        /// 目标时间的上个月最后一天
        /// </summary>
        /// <param name="datetime">目标时间</param>
        /// <returns>返回上个月最后一天</returns>
        public static DateTime LastMonthTail(DateTime datetime)
        {
            return DateTime.Parse(datetime.ToString("yyyy-MM-01")).AddDays(-1);
        }

        /// <summary>
        /// 目标时间当月的第一天
        /// </summary>
        /// <param name="datetime">目标时间</param>
        /// <returns>返回目标时间当月第一天</returns>
        public static DateTime ThisMonthHead(DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day); //本月月初  
        }

        /// <summary>
        /// 权限目标时间计算出来当月的第一天再得出当月的最后一天
        /// </summary>
        /// <param name="datetime">目标时间计算出来的当月第一天</param>
        /// <returns>返回当月最后一天</returns>
        public static DateTime ThisMonthTail(DateTime datetime)
        {
            return datetime.AddMonths(1).AddDays(-1); //本月月末 
        }

        /// <summary>
        ///检测32or64位操作系统
        /// </summary>
        /// <returns>返回结果</returns>
        public static string Detect3264() //检测32or64位操作系统
        {
            ConnectionOptions oConn = new ConnectionOptions();
            System.Management.ManagementScope oMs = new System.Management.ManagementScope("\\\\localhost", oConn);
            System.Management.ObjectQuery oQuery = new System.Management.ObjectQuery("select AddressWidth from Win32_Processor");
            ManagementObjectSearcher oSearcher = new ManagementObjectSearcher(oMs, oQuery);
            ManagementObjectCollection oReturnCollection = oSearcher.Get();
            string addressWidth = null;
            foreach (ManagementObject oReturn in oReturnCollection)
            {
                addressWidth = oReturn["AddressWidth"].ToString();
            }
            return addressWidth;
        }

        /// <summary>
        /// 导入EXCEL数据到DataGridView
        /// </summary>
        /// <param name="filePath">EXCEL数据的路径</param>
        /// <returns>返回DT数据</returns>
        public static DataTable LoadDataFromExcel(string filePath)
        {
            try
            {
                string strConn = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source = " + filePath + ";Extended Properties ='Excel 12.0 Xml;HDR=Yes;IMEX=0'";
                OleDbConnection OleConn = new OleDbConnection(strConn);
                OleConn.Open();

                DataTable schemaTable = OleConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                string sheetName = schemaTable.Rows[0]["TABLE_NAME"].ToString().Trim();
                string sql = "SELECT * FROM  [" + sheetName + "]";
                OleDbDataAdapter OleDaExcel = new OleDbDataAdapter(sql, OleConn);
                DataSet OleDsExcle = new DataSet();
                OleDaExcel.Fill(OleDsExcle, "Sheet1");
                OleConn.Close();
                return OleDsExcle.Tables[0];
            }
            catch
            {
                //MessageBox.Show("数据绑定Excel失败!失败原因：" + err.Message, "提示信息",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return null;

            }
        }

        /// <summary>
        /// 导入EXCEL数据到DataGridView
        /// </summary>
        /// <param name="filePath">EXCEL数据的路径</param>
        /// <returns>返回DT数据</returns>
        public static DataTable LoadDataFromexcel(string filePath)
        {
            try
            {
                string strConn = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source = " + filePath + ";Extended Properties ='Excel 12.0 Xml;HDR=yes;IMEX=1'";
                OleDbConnection OleConn = new OleDbConnection(strConn);
                OleConn.Open();

                DataTable schemaTable = OleConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                string sheetName = schemaTable.Rows[0]["TABLE_NAME"].ToString().Trim();

                string sql = "SELECT * FROM  [" + sheetName + "]";
                OleDbDataAdapter OleDaExcel = new OleDbDataAdapter(sql, OleConn);
                DataSet OleDsExcle = new DataSet();
                OleDaExcel.Fill(OleDsExcle, "Sheet1");
                OleConn.Close();
                return OleDsExcle.Tables[0];
            }
            catch
            {
                //MessageBox.Show("数据绑定Excel失败!失败原因：" + err.Message, "提示信息",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return null;

            }
        }

        /// <summary>
        /// 将GridView表转换为DataTable
        /// </summary>
        /// <param name="dgv">GridView表</param>
        /// <returns>返回转换后结果</returns>
        public static DataTable GetDgvToTable(DataGridView dgv)
        {
            DataTable dt = new DataTable();

            // 列强制转换
            for (int count = 0; count < dgv.Columns.Count; count++)
            {
                DataColumn dc = new DataColumn(dgv.Columns[count].HeaderText.ToString().Trim());
                dt.Columns.Add(dc);
            }

            // 循环行
            for (int count = 0; count < dgv.Rows.Count; count++)
            {
                DataRow dr = dt.NewRow();
                for (int countsub = 0; countsub < dgv.Columns.Count; countsub++)
                {
                    string text = dgv.Rows[count].Cells[countsub].Value.ToString().Trim();
                    //if (text == "&nbsp;")
                    //{
                    //    text = "";
                    //}
                    dr[countsub] = Convert.ToString(text);
                }
                dt.Rows.Add(dr);
            }
            return dt;
        }

        /// <summary>
        /// 将DataTable转换为List集合
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static List<T> ConvertToList<T>(DataTable dt) where T : new()
        {
            List<T> ts = new List<T>();
            foreach (DataRow dr in dt.Rows)
            {
                T t = new T();
                PropertyInfo[] propertys = t.GetType().GetProperties();
                foreach (PropertyInfo pi in propertys)
                {
                    string tempName = pi.Name;
                    if (dt.Columns.Contains(tempName))
                    {
                        if (!pi.CanWrite)
                        {
                            continue;
                        }
                        object value = dr[tempName];
                        if (value != DBNull.Value)
                        {
                            pi.SetValue(t, value, null);
                        }
                    }
                }
                ts.Add(t);
            }
            return ts;
        }


        /// <summary>
        /// 把泛型List转换成DataTable
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataTable DataDataTable<T>(List<T> list)
        {
            DataTable dt = new DataTable();     
            PropertyInfo[] propertys = typeof(T).GetProperties();
            foreach (PropertyInfo pi in propertys)
            {     
                if (!pi.CanRead) continue;
                dt.Columns.Add(pi.Name, pi.PropertyType);
            }
            foreach (T item in list)
            {
                propertys = item.GetType().GetProperties();
                DataRow newRow = dt.NewRow();
                foreach (PropertyInfo pi in propertys)
                {
                    if (!pi.CanRead) continue;
                    newRow[pi.Name] = pi.GetValue(item);
                }
                dt.Rows.Add(newRow);
            }
            return dt;
        }

        /// <summary>
        /// 查询管理起来的帐户与密码信息
        /// </summary>
        /// <param name="code">类型</param>
        /// <returns></returns>
        public static DataTable QueryAccount(string code)
        {
            string sql = "select * from MES_Honor.dbo.accountmanage where code='" + code + "'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// //字符转ASCII码
        /// </summary>
        /// <param name="character">需要转换的字符</param>
        /// <returns></returns>
        public static int StringToASCII(string character)
        {
            if (character.Length == 1)
            {
                ASCIIEncoding asciiEncoding = new ASCIIEncoding();
                int intAsciiCode = (int)asciiEncoding.GetBytes(character)[0];
                return (intAsciiCode);
            }
            else
            {
                throw new Exception("Character is not valid.");
            }

        }

        /// <summary>
        /// 两个日期的时间差
        /// </summary>
        /// <param name="d1">开始时间</param>
        /// <param name="d2">结束时间</param>
        /// <returns>时间差</returns>
        public static TimeSpan timeDistance(DateTime d1, DateTime d2)
        {
            TimeSpan ts;
            if (d1 > d2)
            {
                ts = d1 - d2;
            }
            else
            {
                ts = d2 - d1;
            }
            return ts;
        }

        /// <summary>
        /// 查询系统的设定数据
        /// </summary>
        /// <param name="item"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static DataTable QueryUserSet(string item)
        {
            string sql = "select * from version where item='" + item + "'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询系统的设定数据
        /// </summary>
        /// <param name="Class"></param>
        /// <param name="type"></param>
        /// <param name="item"></param>
        /// <returns></returns>
        public static DataTable SystemSetting(string sysname, string type,string item)
        {
            string sql = "select * from systemsetting where sysname='" + sysname + "' and type='" + type + "' and item='"+item+"'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 
        /// 查询打印偏离左右上下
        /// </summary>
        /// <param name="code">软件名</param>
        /// <param name="type">用户</param>
        /// <returns></returns>
        public static DataTable QueryLRUD(string code, string type)
        {
            string sql = "select * from MES_Honor.dbo.Coordinates where code='" + code + "' and type='" + type + "' ";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 更新用户坐标的数据
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="code">软件名</param>
        /// <param name="type">用户</param>
        public static void updateLRUD(string x, string y, string code, string type)
        {
            string sql = "update MES_Honor.dbo.Coordinates set x='" + x + "',y='" + y + "' where code='" + code + "' and type='" + type + "'";
            SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 插入新的用户坐标信息
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="code">软件名</param>
        /// <param name="type">用户</param>
        public static void InsertLRUD(string x, string y, string code, string type)
        {
            string sql = "insert into MES_Honor.dbo.Coordinates(type,code,x,y) values('" + type + "','" + code + "','" + x + "','" + y + "')";
            SqlHelper.ExecuteSql(sql);
        }

        /// <summary>
        /// 随机数
        /// </summary>
        /// <returns></returns>
        public static int random(int start, int end)
        {
            Random ran = new Random();
            return ran.Next(start, end);
        }

        public static DataTable QueryUserSetting(string usercode, string type)
        {
            string sql = "select * from MES_Honor.dbo.UserSetting where usercode='" + usercode + "' and type='" + type + "' and syscode='EWork' ";
            return SqlHelper.Query(sql).Tables[0];
        }

        public static void UpdateUserSetting(string usercode, string type, string item)
        {
            string sql = "update MES_Honor.dbo.UserSetting set item='" + item + "' where syscode='OA' and usercode='" + usercode + "'";
            SqlHelper.ExecuteSql(sql);
        }

        public static void InsertUserSetting(string usercode, string type, string item)
        {
            string sql = "insert into MES_Honor.dbo.UserSetting(usercode,type,item,syscode) values('" + usercode + "','" + type + "','" + item + "','OA')";
            SqlHelper.ExecuteSql(sql);
        }

        public static DataTable Version(string item)
        {
            string sql = "select * from Version where item='" + item + "'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 写入故事
        /// </summary>
        /// <param name="Content"></param>
        /// <returns></returns>
        public static string WritetJokerStory(string Content)
        {
            string sql = $"INSERT INTO Sys_Story (Content) VALUES ('{Content}')";
            return SqlHelper.ExecuteSql(sql).ToString();
        }

        /// <summary>
        /// 查询当天故事
        /// </summary>
        /// <returns></returns>
        public static string ReadStory()
        {
            string sql = $"SELECT * FROM Sys_Story WHERE inTime >= CAST(GETDATE() AS DATE) ORDER BY inTime DESC";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            return dt.Rows.Count > 0 ? dt.Rows[0]["Content"].ToString() : null;
        }

        /// <summary>
        /// 查询用户权限
        /// </summary>
        /// <param name="usercode"></param>
        /// <returns></returns>
        public static DataTable Permission(string usercode)
        {
            //string sql = "select * from userquanxian where usercode='" + usercode + "' and type='EWork'";   
            string sql = "select * from quanxianList as a left join UserQuanXian as b on a.QXname = b.QXName where userCode = '"+usercode+"' and ParentName<>'0' and type='EWork'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            return dt;
        }




        public static DataTable ParentPer(DataTable dt)
        {
            if (dt.Rows.Count==0)
            {
                return dt;
            }
            string str = "";
            foreach (DataRow dr in dt.Rows)
            {
                if (str == "")
                {
                    str ="'"+ dr["ParentName"].ToString()+"'";
                }
                else 
                { 
                    str +=",'" +dr["ParentName"].ToString()+"'";
                }
            }
            string sql = "select * from quanxianList where QXname in(" + str+ ") and ParentName='0'";
            return SqlHelper.Query(sql).Tables[0];
        }

        public static void GetRegistryWinUpdate()
        {
            RegistryKey rk = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
            RegistryKey windows = rk.OpenSubKey(@"SOFTWARE\Policies\Microsoft\Windows");
            RegistryKey winupdate = windows.OpenSubKey("WindowsUpdate");
            while (true)
            {             
                if (winupdate == null)
                {
                    try
                    {
                        RegistryKey key = rk.CreateSubKey(@"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate");
                    }
                    catch (Exception e)
                    {
                        if (e.Message.ToString().IndexOf("访问被拒绝") > -1)
                        {
                            MessageBox.Show("请使用右键--以管理员身份运行");
                            Thread.Sleep(30000);
                            continue;
                        }
                        else
                        {
                            MessageBox.Show(e.Message.ToString());
                            break;
                        }
                    }
                }
                try
                {
                    winupdate = windows.OpenSubKey("WindowsUpdate", true);
                    winupdate.SetValue("UpdateServiceUrlAlternate", "", RegistryValueKind.String);
                    winupdate.SetValue("WUServer", "http://192.168.0.19:8530", RegistryValueKind.String);
                    winupdate.SetValue("WUStatusServer", "http://192.168.0.19:8530", RegistryValueKind.String);
                }
                catch
                {

                }
                break;
            }

            while (true)
            {

                RegistryKey au = winupdate.OpenSubKey("AU");
                if (au == null)
                {
                    try
                    {
                        RegistryKey key = rk.CreateSubKey(@"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU");
                    }
                    catch (Exception e)
                    {
                        if (e.Message.ToString().IndexOf("访问被拒绝") > -1)
                        {
                            MessageBox.Show("请使用右键--以管理员身份运行");
                            Thread.Sleep(30000);
                            continue;
                        }
                        else
                        {
                            MessageBox.Show(e.Message.ToString());
                            break;
                        }
                    }
                }
                try
                {
                    au = winupdate.OpenSubKey("AU", true);
                    au.SetValue("NoAutoUpdate", "00000000", RegistryValueKind.DWord);
                    au.SetValue("AUOptions", "00000004", RegistryValueKind.DWord);
                    au.SetValue("ScheduledInstallDay", "00000000", RegistryValueKind.DWord);
                    au.SetValue("ScheduledInstallTime", "00000009", RegistryValueKind.DWord);
                    au.SetValue("UseWUServer", "00000001", RegistryValueKind.DWord);
                    au.SetValue("AutoInstallMinorUpdates", "00000001", RegistryValueKind.DWord);
                    au.SetValue("RebootRelaunchTimeoutEnabled", "00000001", RegistryValueKind.DWord);
                    au.SetValue("RebootRelaunchTimeout", "00000014", RegistryValueKind.DWord);
                    au.SetValue("DetectionFrequencyEnabled", "00000001", RegistryValueKind.DWord);
                    au.SetValue("DetectionFrequency", "00000005", RegistryValueKind.DWord);
                    au.SetValue("NoAutoRebootWithLoggedOnUser", "00000001", RegistryValueKind.DWord);
                    au.SetValue("RebootWarningTimeout", "00000010", RegistryValueKind.DWord);
                    au.SetValue("RebootWarningTimeoutEnabled", "00000001", RegistryValueKind.DWord);
                    au.SetValue("RescheduleWaitTime", "00000005", RegistryValueKind.DWord);
                    au.SetValue("RescheduleWaitTimeEnabled", "00000001", RegistryValueKind.DWord);
                }
                catch
                {

                }
                break;
            }

            try
            {
                RegistryKey cad = rk.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System", true);
                cad.SetValue("DisableCAD", "00000001", RegistryValueKind.DWord);
            }
            catch
            {

            }
        }

        /// <summary>
        /// 32位MD5加密
        /// </summary>
        /// <param name="password"></param>
        /// <returns></returns>
        public static string MD5Encrypt32(string password)
        {
            string cl = password;
            string pwd = "";
            MD5 md5 = MD5.Create(); //实例化一个md5对像
                                    // 加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择　
            byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(cl));
            // 通过使用循环，将字节类型的数组转换为字符串，此字符串是常规字符格式化所得
            for (int i = 0; i < s.Length; i++)
            {
                // 将得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符 
                pwd = pwd + s[i].ToString("X");
            }
            return pwd;
        }

      

        public static string DateSwitch(string text)
        {
            if (int.TryParse(text,out int tt))
            {
                return text;
            }
            switch (text)
            {
                case "A": text="10";break;
                case "B": text="11";break;
                case "C": text="12";break;
                case "D": text="13";break;
                case "E": text="14";break;
                case "F": text="15";break;
                case "G": text="16";break;
                case "H": text="17";break;
                case "J": text="18";break;
                case "K": text="19";break;
                case "L": text="20";break;
                case "M": text="21";break;
                case "N": text="22";break;
                case "P": text="23";break;
                case "Q": text="24";break;
                case "R": text="25";break;
                case "S": text="26";break;
                case "T": text="27";break;
                case "U": text="28";break;
                case "V": text="29";break;
                case "W": text="30";break;
                case "X": text="31";break;
                case "Y": text="32";break;
                case "Z": text="33";break;
            }
            return text;
        }











        #region"基本操作函数"
        /// <summary>
        /// 得到程序工作目录
        /// </summary>
        /// <returns></returns>
        private static string GetWorkDirectory()
        {
            try
            {
                return Path.GetDirectoryName(typeof(PublicService).Assembly.Location);
            }
            catch
            {
                return System.Windows.Forms.Application.StartupPath;
            }
        }
        /// <summary>
        /// 判断字符串是否为空串
        /// </summary>
        /// <param name="szString">目标字符串</param>
        /// <returns>true:为空串;false:非空串</returns>
        private static bool IsEmptyString(string szString)
        {
            if (szString == null)
                return true;
            if (szString.Trim() == string.Empty)
                return true;
            return false;
        }
        /// <summary>
        /// 创建一个制定根节点名的XML文件
        /// </summary>
        /// <param name="szFileName">XML文件</param>
        /// <param name="szRootName">根节点名</param>
        /// <returns>bool</returns>
        private static bool CreateXmlFile(string szFileName, string szRootName)
        {
            if (szFileName == null || szFileName.Trim() == "")
                return false;
            if (szRootName == null || szRootName.Trim() == "")
                return false;

            XmlDocument clsXmlDoc = new XmlDocument();
            clsXmlDoc.AppendChild(clsXmlDoc.CreateXmlDeclaration("1.0", "GBK", null));
            clsXmlDoc.AppendChild(clsXmlDoc.CreateNode(XmlNodeType.Element, szRootName, ""));
            try
            {
                clsXmlDoc.Save(szFileName);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从XML文件获取对应的XML文档对象
        /// </summary>
        /// <param name="szXmlFile">XML文件</param>
        /// <returns>XML文档对象</returns>
        public static XmlDocument GetXmlDocument(string szXmlFile)
        {
            if (IsEmptyString(szXmlFile))
                return null;
            if (!File.Exists(szXmlFile))
                return null;
            XmlDocument clsXmlDoc = new XmlDocument();
            try
            {
                clsXmlDoc.Load(szXmlFile);
            }
            catch
            {
                return null;
            }
            return clsXmlDoc;
        }

        /// <summary>
        /// 将XML文档对象保存为XML文件
        /// </summary>
        /// <param name="clsXmlDoc">XML文档对象</param>
        /// <param name="szXmlFile">XML文件</param>
        /// <returns>bool:保存结果</returns>
        private static bool SaveXmlDocument(XmlDocument clsXmlDoc, string szXmlFile)
        {
            if (clsXmlDoc == null)
                return false;
            if (IsEmptyString(szXmlFile))
                return false;
            try
            {
                if (File.Exists(szXmlFile))
                    File.Delete(szXmlFile);
            }
            catch
            {
                return false;
            }
            try
            {
                clsXmlDoc.Save(szXmlFile);
            }
            catch
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 获取XPath指向的单一XML节点
        /// </summary>
        /// <param name="clsRootNode">XPath所在的根节点</param>
        /// <param name="szXPath">XPath表达式</param>
        /// <returns>XmlNode</returns>
        public static XmlNode SelectXmlNode(XmlNode clsRootNode, string szXPath)
        {
            if (clsRootNode == null || IsEmptyString(szXPath))
                return null;
            try
            {
                return clsRootNode.SelectSingleNode(szXPath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取XPath指向的XML节点集
        /// </summary>
        /// <param name="clsRootNode">XPath所在的根节点</param>
        /// <param name="szXPath">XPath表达式</param>
        /// <returns>XmlNodeList</returns>
        public static XmlNodeList SelectXmlNodes(XmlNode clsRootNode, string szXPath)
        {
            if (clsRootNode == null || IsEmptyString(szXPath))
                return null;
            try
            {
                return clsRootNode.SelectNodes(szXPath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建一个XmlNode并添加到文档
        /// </summary>
        /// <param name="clsParentNode">父节点</param>
        /// <param name="szNodeName">结点名称</param>
        /// <returns>XmlNode</returns>
        private static XmlNode CreateXmlNode(XmlNode clsParentNode, string szNodeName)
        {
            try
            {
                XmlDocument clsXmlDoc = null;
                if (clsParentNode.GetType() != typeof(XmlDocument))
                    clsXmlDoc = clsParentNode.OwnerDocument;
                else
                    clsXmlDoc = clsParentNode as XmlDocument;
                XmlNode clsXmlNode = clsXmlDoc.CreateNode(XmlNodeType.Element, szNodeName, string.Empty);
                if (clsParentNode.GetType() == typeof(XmlDocument))
                {
                    clsXmlDoc.LastChild.AppendChild(clsXmlNode);
                }
                else
                {
                    clsParentNode.AppendChild(clsXmlNode);
                }
                return clsXmlNode;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 设置指定节点中指定属性的值
        /// </summary>
        /// <param name="parentNode">XML节点</param>
        /// <param name="szAttrName">属性名</param>
        /// <param name="szAttrValue">属性值</param>
        /// <returns>bool</returns>
        private static bool SetXmlAttr(XmlNode clsXmlNode, string szAttrName, string szAttrValue)
        {
            if (clsXmlNode == null)
                return false;
            if (IsEmptyString(szAttrName))
                return false;
            if (IsEmptyString(szAttrValue))
                szAttrValue = string.Empty;
            XmlAttribute clsAttrNode = clsXmlNode.Attributes.GetNamedItem(szAttrName) as XmlAttribute;
            if (clsAttrNode == null)
            {
                XmlDocument clsXmlDoc = clsXmlNode.OwnerDocument;
                if (clsXmlDoc == null)
                    return false;
                clsAttrNode = clsXmlDoc.CreateAttribute(szAttrName);
                clsXmlNode.Attributes.Append(clsAttrNode);
            }
            clsAttrNode.Value = szAttrValue;
            return true;
        }
        #endregion

        #region"配置文件的读取和写入"
        private static string CONFIG_FILE = "Config.xml";
        /// <summary>
        ///  读取指定的配置文件中指定Key的值
        /// </summary>
        /// <param name="szKeyName">读取的Key名称</param>
        /// <param name="szDefaultValue">指定的Key不存在时,返回的值</param>
        /// <returns>Key值</returns>
        public static int GetConfigData(string szKeyName, int nDefaultValue)
        {
            string szValue = GetConfigData(szKeyName, nDefaultValue.ToString());
            try
            {
                return int.Parse(szValue);
            }
            catch
            {
                return nDefaultValue;
            }
        }

        /// <summary>
        ///  读取指定的配置文件中指定Key的值
        /// </summary>
        /// <param name="szKeyName">读取的Key名称</param>
        /// <param name="szDefaultValue">指定的Key不存在时,返回的值</param>
        /// <returns>Key值</returns>
        public static float GetConfigData(string szKeyName, float fDefaultValue)
        {
            string szValue = GetConfigData(szKeyName, fDefaultValue.ToString());
            try
            {
                return float.Parse(szValue);
            }
            catch
            {
                return fDefaultValue;
            }
        }

        /// <summary>
        ///  读取指定的配置文件中指定Key的值
        /// </summary>
        /// <param name="szKeyName">读取的Key名称</param>
        /// <param name="szDefaultValue">指定的Key不存在时,返回的值</param>
        /// <returns>Key值</returns>
        public static bool GetConfigData(string szKeyName, bool bDefaultValue)
        {
            string szValue = GetConfigData(szKeyName, bDefaultValue.ToString());
            try
            {
                return bool.Parse(szValue);
            }
            catch
            {
                return bDefaultValue;
            }
        }

        /// <summary>
        ///  读取指定的配置文件中指定Key的值
        /// </summary>
        /// <param name="szKeyName">读取的Key名称</param>
        /// <param name="szDefaultValue">指定的Key不存在时,返回的值</param>
        /// <returns>Key值</returns>
        public static string GetConfigData(string szKeyName, string szDefaultValue)
        {
            string szConfigFile = string.Format("{0}\\{1}", GetWorkDirectory(), CONFIG_FILE);
            if (!File.Exists(szConfigFile))
            {
                return szDefaultValue;
            }

            XmlDocument clsXmlDoc = GetXmlDocument(szConfigFile);
            if (clsXmlDoc == null)
                return szDefaultValue;

            string szXPath = string.Format(".//key[@name='{0}']", szKeyName);
            XmlNode clsXmlNode = SelectXmlNode(clsXmlDoc, szXPath);
            if (clsXmlNode == null)
            {
                return szDefaultValue;
            }

            XmlNode clsValueAttr = clsXmlNode.Attributes.GetNamedItem("value");
            if (clsValueAttr == null)
                return szDefaultValue;
            return clsValueAttr.Value;
        }

        public static string IseeGetConfigData(string szKeyName, string url,string key,string value)
        {
            string szConfigFile = url;
            if (!File.Exists(szConfigFile))
            {
                return "";
            }

            XmlDocument clsXmlDoc = GetXmlDocument(szConfigFile);
            if (clsXmlDoc == null)
                return "";

            string szXPath = string.Format(".//"+key+"[@itemKeyName='{0}']", szKeyName);
            XmlNode clsXmlNode = SelectXmlNode(clsXmlDoc, szXPath);
            if (clsXmlNode == null)
            {
                return "";
            }

            XmlNode clsValueAttr = clsXmlNode.Attributes.GetNamedItem(value);
            if (clsValueAttr == null)
                return "";
            return clsValueAttr.Value;
        }

        /// <summary>
        ///  保存指定Key的值到指定的配置文件中
        /// </summary>
        /// <param name="szKeyName">要被修改值的Key名称</param>
        /// <param name="szValue">新修改的值</param>
        public static bool WriteConfigData(string szKeyName, string szValue)
        {
            string szConfigFile = string.Format("{0}\\{1}", GetWorkDirectory(), CONFIG_FILE);
            if (!File.Exists(szConfigFile))
            {
                if (!CreateXmlFile(szConfigFile, "Config"))
                    return false;
            }
            XmlDocument clsXmlDoc = GetXmlDocument(szConfigFile);

            string szXPath = string.Format(".//key[@name='{0}']", szKeyName);
            XmlNode clsXmlNode = SelectXmlNode(clsXmlDoc, szXPath);
            if (clsXmlNode == null)
            {
                clsXmlNode = CreateXmlNode(clsXmlDoc, "key");
            }
            if (!SetXmlAttr(clsXmlNode, "name", szKeyName))
                return false;
            if (!SetXmlAttr(clsXmlNode, "value", szValue))
                return false;
            //
            return SaveXmlDocument(clsXmlDoc, szConfigFile);
        }
        #endregion




    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Model
{
    public class ControlProcessList
    {
        public string id { get; set; } //id
        public string errnumber { get; set; }//故障代码
        public string approve { get; set; }//良品与不良品
        public string username { get; set; }//操作人
        public string job { get; set; }//操作工站点
        public string location { get; set; }//下一站位置
        public string newimei { get; set; }//新的IMEI
        public string wxname { get; set; }//维修人员
        public string remarks { get; set; }//标记操作类型   
        public string other { get; set; }//备注
        public string battery { get; set; }//电池
        public string psid { get; set; }//任务令号
        public string phonetype { get; set; }//产品型号
        public string phonemodel { get; set; }//产品型号
        public string targetcode { get; set; }//目标编码
        public string type { get; set; }//产品类型
        public string old_imei { get; set; }//旧IMEI号
        public string sn { get; set; }//SN号
        public string veneercode { get; set; }//单板号
        public string initialcode { get; set; }//原始编码
        public string wxtype { get; set; }//维修类型
        public string inname { get; set; }//操作人
        public string intime { get; set; }
        public string cspmtime { get; set; }
        public string endtime { get; set; }
        public string t_time { get; set; }
        public string starttime { get; set; }
        public string dep { get; set; }
    }
}

@echo off
setlocal enabledelayedexpansion

echo [INFO] Quick package builder for EasyWork.Honor v1.2.2

echo [STEP1] Create Release directory from Debug build...
if not exist "bin\Debug\EasyWork.Honor.exe" (
    echo [ERROR] Debug build not found!
    goto :end
)

if exist "bin\Release" rmdir /s /q "bin\Release"
mkdir "bin\Release"

echo [INFO] Copying files from Debug to Release...
xcopy "bin\Debug\*.*" "bin\Release\" /E /Y >nul

echo [STEP2] Download nuget.exe if needed...
if not exist "nuget.exe" (
    echo [INFO] Downloading nuget.exe...
    powershell -Command "Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile 'nuget.exe'"
)

if not exist "nuget.exe" (
    echo [ERROR] nuget.exe not available
    goto :end
)

echo [STEP3] Create NuGet package...
nuget.exe pack EasyWork.Honor.nuspec -Version 1.2.2 -OutputDirectory bin\Release
if errorlevel 1 (
    echo [ERROR] NuGet pack failed
    goto :end
)

echo [STEP4] Generate Squirrel installer...
pushd bin\Release
if exist "Setup.exe" del "Setup.exe"
if exist "RELEASES" del "RELEASES"

echo [INFO] Running Squirrel...
..\..\packages\squirrel.windows.2.0.1\tools\Squirrel.exe --releasify EasyWork.Honor.1.2.2.nupkg --releaseDir=.
if errorlevel 1 (
    echo [ERROR] Squirrel failed
    popd
    goto :end
)
popd

echo [SUCCESS] Package creation completed!
echo [INFO] Generated files:
dir "bin\Release\*.nupkg" /b 2>nul
dir "bin\Release\Setup.exe" /b 2>nul
dir "bin\Release\RELEASES" /b 2>nul

echo.
echo [NEXT] You can now run: publish.bat

:end
pause

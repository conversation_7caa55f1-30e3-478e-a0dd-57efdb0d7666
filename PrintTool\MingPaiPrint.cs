﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class MingPaiPrint : Form
    {
        DataTable dt;
        public MingPaiPrint()
        {
            InitializeComponent();
            tb_MEID.Focus();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            tb_path.Text = "";
            //打开一个文件选择框
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Title = "Excel文件";
            ofd.FileName = "";
            ofd.Filter = "Excel文件(*.xlsx)|*.xlsx|Excel低版本文件(*.xls)|*.xls";
            ofd.ValidateNames = true;     //文件有效性验证ValidateNames，验证用户输入是否是一个有效的Windows文件名
            ofd.CheckFileExists = true;  //验证路径有效性
            ofd.CheckPathExists = true; //验证文件有效性

            string strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择Excel文件！无法进行数据导入");
                return;
            }

            dt = PublicService.LoadDataFromExcel(strName);
            if (dt.Rows.Count == 0)
            {
                MessageBox.Show("无数据可导入");
                return;
            }
            tb_path.Text = strName;
        }

        private void bt_AllPrint_Click(object sender, EventArgs e)
        {
            if (dt.Rows.Count==0||tb_path.Text.Trim()=="")
            {
                MessageBox.Show("无数据可打印");
                return;
            }
            int i = 0;
            string phonemodel = "";
            string ScriptText = "";
            foreach (DataRow dr in dt.Rows)
            {
                i++;
                if (dr["型号"].ToString().Trim()==""||dr["MEID"].ToString()==""||dr["IMEI1"].ToString().Trim()==""||dr["SN"].ToString().Trim()=="")
                {
                    MessageBox.Show("第"+i+"行,有空数据!");
                    return;
                }
                if (dr["MEID"].ToString().Trim().Length !=14 || dr["IMEI1"].ToString().Trim().Length!=15 || dr["SN"].ToString().Trim().Length!=16)
                {
                    MessageBox.Show("第" + i + "行,MEID长度需14个字符,IMEI1需15个字符,SN需要16个字符!");
                    return;
                }
                if (phonemodel == "")
                {
                    ScriptText = PrintService.Get_ZPLScript("铭牌", dr["型号"].ToString().Trim());
                    if (ScriptText == "0")
                    {
                        MessageBox.Show("系统没有该型号[" + dr["型号"].ToString().Trim() + "]的铭牌脚本数据");
                        return;
                    }                    
                }
                else if (phonemodel!=dr["型号"].ToString().Trim())
                {
                    phonemodel = dr["型号"].ToString().Trim();
                    ScriptText = PrintService.Get_ZPLScript("铭牌", dr["型号"].ToString().Trim());
                    if (ScriptText == "0")
                    {
                        MessageBox.Show("系统没有该型号["+ dr["型号"].ToString().Trim() + "]的铭牌脚本数据");
                        return;
                    }
                }
            }
            phonemodel = "";
            ScriptText = "";
            foreach (DataRow dr in dt.Rows)
            {
                if (phonemodel == "")
                {
                    ScriptText = PrintService.Get_ZPLScript("铭牌", dr["型号"].ToString().Trim());
                    if (ScriptText == "")
                    {
                        MessageBox.Show("系统没有该型号[" + dr["型号"].ToString().Trim() + "]的铭牌脚本数据");
                        return;
                    }
                }
                else if (phonemodel.ToUpper().Trim() != dr["型号"].ToString().ToUpper().Trim())
                {
                    phonemodel = dr["型号"].ToString().Trim();
                    ScriptText = PrintService.Get_ZPLScript("铭牌", dr["型号"].ToString().Trim());
                    if (ScriptText == "")
                    {
                        MessageBox.Show("系统没有该型号[" + dr["型号"].ToString().Trim() + "]的铭牌脚本数据");
                        return;
                    }
                }
                string text = ScriptText;
                for (int j = 0; j < 5; j++)
                {
                    text = text.Replace("$MEID$", dr["MEID"].ToString().ToUpper().Trim());
                    text = text.Replace("$IMEI1$", dr["IMEI1"].ToString().ToUpper().Trim());
                    text = text.Replace("$SN$", dr["SN"].ToString().ToUpper().Trim());
                }
                ZPLPrint zb = new ZPLPrint();
                zb.ZPL_Print(text);
                Thread.Sleep(500);
            }
            dt = null;
            tb_path.Text = "";

        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            if (tb_PhoneModel.Text.Trim()=="")
            {
                tb_PhoneModel.Focus();
                return;
            }
            if (tb_MEID.Text.Trim().Length!=14)
            {
                tb_MEID.Focus();
                return;
            }
            if (tb_IMEI1.Text.Trim().Length!=15)
            {
                tb_IMEI1.Focus();
                return;
            }
            if (tb_SN.Text.Trim().Length!=16)
            {
                tb_SN.Focus();
                return;
            }
            string ScriptText = PrintService.Get_ZPLScript("铭牌",tb_PhoneModel.Text.Trim());
            if (ScriptText=="")
            {
                MessageBox.Show("系统没有该型号的铭牌脚本数据");
                return;
            }
            for (int i = 0; i < 3; i++)
            {
                ScriptText = ScriptText.Replace("$MEID$", tb_MEID.Text.ToUpper().Trim());
                ScriptText = ScriptText.Replace("$IMEI1$", tb_IMEI1.Text.ToUpper().Trim());
                ScriptText = ScriptText.Replace("$SN$", tb_SN.Text.ToUpper().Trim());
            }
            ZPLPrint zb = new ZPLPrint();            
            zb.ZPL_Print(ScriptText);
            if (!cb_PhoneModel.Checked)
            {
                tb_PhoneModel.Text = "";
            }
            tb_MEID.Text = tb_IMEI1.Text = tb_SN.Text = "";
        }

        private void tb_PhoneModel_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter||e.KeyCode==Keys.Tab)
            {
                bt_Print_Click(null,null);
            }
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            string FilePath = Application.StartupPath + "\\Template\\铭牌批量打印模板 V1.0.xlsx";//文件路径
            string FileName = Path.GetFileName(FilePath);
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Title = "下载文件";
            saveFileDialog.Filter = "xlsx文件(*.xlsx)|*.xlsx";

            saveFileDialog.FileName = FileName;
            DialogResult dialogResult = saveFileDialog.ShowDialog(this);
            if (dialogResult == DialogResult.OK)
            {
                System.Net.WebClient client = new System.Net.WebClient();
                byte[] data = client.DownloadData(FilePath);
                FileStream fs = new FileStream(saveFileDialog.FileName, FileMode.Create);
                fs.Write(data, 0, data.Length);
                fs.Close();
                MessageBox.Show("下载成功！");
            }
        }
    }
}

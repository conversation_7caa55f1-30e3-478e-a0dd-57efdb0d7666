﻿using EasyWork.bll;
using EasyWork.bll.Login;
using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.Manage
{
    public partial class ChangePWD : Form
    {
        string type;
        public ChangePWD(string stype)
        {
            InitializeComponent();
            type = stype;
            if (type == "注册" || type == "强制")
            {
                tbold_pwd.Text = Login.pwd;
                tbold_pwd.Enabled = false;
            }
        }

        private void Button1_Click(object sender, EventArgs e)
        {
            if (tbold_pwd.Text.Trim() == "" || tbnew_pwd.Text.Trim() == "" || tbconfirm_pwd.Text.Trim() == "")
            {
                MessageBox.Show("新旧密码不能为空!", "提示");
                return;
            }
            if (tbold_pwd.Text.Trim() != Login.pwd)
            {
                MessageBox.Show("你输入的旧密码有误!", "提示");
                return;
            }

            if (tbnew_pwd.Text.Trim() != tbconfirm_pwd.Text.Trim())
            {
                MessageBox.Show("新密码两次输入不一致!", "提示");
                return;
            }
            if (tbold_pwd.Text.Trim() == tbconfirm_pwd.Text.Trim())
            {
                MessageBox.Show("新密码不能与旧密码相同!", "提示");
                return;
            }
            bool regex = Regex.IsMatch(tbconfirm_pwd.Text.Trim(), @"^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,16}$");
            if (!regex)
            {
                MessageBox.Show("密码必须为8-16位,且包含大写字母+小写字母+数字+特殊字符!", "提示");
                return;
            }

            if (type != "注册")
            {
                DataTable oldpwddt = LoginService.QueryOldPwd(Login.usercode);
                if (oldpwddt.Rows.Count > 0)
                {
                    if (oldpwddt.Rows.Count >= 5)
                    {
                        for (int i = 0; i < 5; i++)
                        {
                            if (tbconfirm_pwd.Text.Trim() == oldpwddt.Rows[i]["oldpwd"].ToString())
                            {
                                MessageBox.Show("最近5次修改密码里,你已经使用过此密码!", "提示");
                                return;
                            }
                        }
                    }
                    else
                    {
                        for (int i = 0; i < oldpwddt.Rows.Count; i++)
                        {
                            if (tbconfirm_pwd.Text.Trim() == oldpwddt.Rows[i]["oldpwd"].ToString())
                            {
                                MessageBox.Show("最近5次修改密码里,你已经使用过此密码!", "提示");
                                return;
                            }
                        }
                    }
                }
            }
            string newpwd = PublicService.MD5Encrypt32(tbconfirm_pwd.Text.Trim());

            if (type == "注册")
            {
                if (LoginService.InsertUserLogin(Login.usercode, newpwd) > 0)
                {
                    MessageBox.Show("新用户注册成功,请关闭此窗口重新登陆!", "提示");
                    type = "";
                    return;
                }
            }
            else if (type == "强制")
            {
                if (LoginService.UpdateUserPwd(Login.usercode, newpwd) > 0)
                {
                    MessageBox.Show("密码已修改,请关闭此窗口重新登陆!", "提示");
                    type = "";
                    return;
                }
            }
            else
            {
                if (LoginService.UpdateUserPwd(Login.usercode, newpwd) > 0)
                {
                    Login.pwd = tbnew_pwd.Text.Trim();
                    MessageBox.Show("修改成功!");
                    tbold_pwd.Text = tbnew_pwd.Text = tbconfirm_pwd.Text = "";
                }
            }
        }

        private void ChangePWD_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (type == "注册" || type == "强制")
            {
                MessageBox.Show("请修改密码!", "提示");
                e.Cancel = true;
            }
        }
    }
}

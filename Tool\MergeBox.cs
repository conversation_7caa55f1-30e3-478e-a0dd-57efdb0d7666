﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.Tool
{
    public partial class MergeBox : Form
    {
        string ScriptText;
        string date;
        public MergeBox()
        {
            InitializeComponent();
            ScriptText = PrintService.Get_ZPLScript("中箱标", "ALL");
            date = DateTime.Now.ToString("yyyy-MM-dd");
        }

        private void button1_Click(object sender, EventArgs e)
        {
           

        }

        private void bt_Print_Click(object sender, EventArgs e)
        {

            if (rbBoxNo.Text.Trim() == "")
            {
                lb_show.Text = "请输入中箱号";
                rbBoxNo.Focus();
                return;
            }

            string st = rbBoxNo.Text.Trim().Replace("\n", "','");

            string sql = "select id,psid,targetcode,cartonno,meid as sn,mergeboxno from MES_Honor.dbo.PackingNo where cartonno in('"+st+"') order by targetcode desc";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count==0)
            {
                MessageBox.Show("系统没有该批次的中箱号数据");
                return;
            }

            foreach (DataRow dr in dt.Rows)
            {
                if (dt.Select("cartonno='" + dr["cartonno"].ToString() +"'").Length==0)
                {
                    MessageBox.Show($"系统没有{ dr["cartonno"].ToString()}的中箱号数据");
                    return;
                }
            }

            if (dt.Select("targetcode='"+ dt.Rows[0]["targetcode"].ToString() + "'").Length!=dt.Rows.Count)
            {
                MessageBox.Show($"每次合箱只能是一个编码");
                return;
            }
           ArrayList code = new ArrayList();
            string str = "";
            int i = 0;
            int boxno =0 ;
            string date = DateTime.Now.ToString("yyMMddHHmmss");
            foreach (DataRow dr in dt.Rows)
            {
                if (str!= dr["targetcode"].ToString())
                {
                    boxno = 1;
                    i = 1;
                    //code.Add(dr["targetcode"].ToString());
                    str = dr["targetcode"].ToString();
                    dr["mergeboxno"] = "GL" + dr["targetcode"].ToString()+date+"000"+ boxno;
                }
                else
                {
                    if (i==14)
                    {
                        boxno ++;
                    }
                    i++;
                    dr["mergeboxno"] = "GL" + dr["targetcode"].ToString()+date+"000" + boxno;
                }
            }
            dataGridView1.DataSource = dt;
          


            //string text = ScriptText;
            //for (int j = 0; j < 3; j++)
            //{
            //    text = text.Replace("$PSID$", tb_PSID.Text.Trim().ToUpper());
            //    text = text.Replace("$ITEM$", tb_ITEM.Text.Trim().ToUpper());
            //    text = text.Replace("$QTY$", tb_Qty.Text.Trim().ToUpper());
            //    text = text.Replace("$CARTON$", tb_CARTON.Text.Trim().ToUpper());
            //    text = text.Replace("$DATE$", tb_Date.Text.Trim().ToUpper().Replace("-", "").Replace("-", ""));
            //}

            //ZPLPrint zb = new ZPLPrint();
            //zb.ZPL_Print(text);
        }

        private void btSave_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow item in dataGridView1.Rows)
            {
              //  string sql= "update MES_Honor.dbo.PackingNo set mergeboxno='"++"'";
            }
        }
    }
}

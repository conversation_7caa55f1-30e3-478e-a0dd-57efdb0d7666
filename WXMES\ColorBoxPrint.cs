﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using EasyWork.Honor.Service.WXMES;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class ColorBoxPrint : Form
    {
        string ScriptText;
        string ColorBoxText;
        string BackText;
        string XCText;
        string EANText;
        bool ApplyPrint=false;
        string ColorPrintName;
        string BackPrintName;
        string XCPrintName;
        string psid;
        string netcode;
        DataTable celt = null;
        public ColorBoxPrint()
        {
            InitializeComponent();
            ColorPrintName = PublicService.QueryUserSet("ColorPrintName").Rows[0]["version"].ToString();
            BackPrintName = PublicService.QueryUserSet("BackPrintName").Rows[0]["version"].ToString();
            XCPrintName = PublicService.QueryUserSet("XCPrintName").Rows[0]["version"].ToString();
            XCText = PrintService.Get_ZPLScript("内购标", "ALL");
        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            laChkShow.Text = "";
            laPrintShow.Text = "";
            if (tbNetCode.Text.Trim().Length != 15)
            {
                tbNetCode.Focus();
                tbNetCode.SelectAll();
                return;
            }
            ApplyPrint = false;
            DataRow[] drr = celt.Select("netcode='" + tbNetCode.Text.Trim() + "'");
            if (drr.Length == 0)
            {
                MessageBox.Show("该网标号不存在");
                return;
            }
            netcode = tbNetCode.Text.Trim();
            if (drr[0]["printqty"].ToString() != "0" && drr[0]["printqty"].ToString() != "")
            {
                DataTable dt = WXMESQuery.GetOrderApply(tbNetCode.Text.Trim(),"网标打印");
                if (dt.Rows.Count==0)
                {
                    MessageBox.Show("该网标号已打印,不能重复打印");
                    return;
                }
                ApplyPrint = true;
            }
            string back = BackText;
            string colorbox = ColorBoxText;
            string yy = DateTime.Now.ToString("yyyy");
            string mm = DateTime.Now.ToString("MM");
            for (int j = 0; j < 5; j++)
            {
                back = back.Replace("$SN$", drr[0]["Product_Barcode"].ToString().Trim().ToUpper());
                back = back.Replace("$MEID$", drr[0]["PHYSICSNO"].ToString().Trim().ToUpper());
                back = back.Replace("$MEID_HEX_14$", drr[0]["PHYSICSNO"].ToString().Trim().ToUpper());
                back = back.Replace("$IMEI_1$", drr[0]["IMEI1"].ToString().Trim().ToUpper());

                colorbox = colorbox.Replace("$IMEI_1$", drr[0]["IMEI1"].ToString().Trim().ToUpper());
                colorbox = colorbox.Replace("$SN$", drr[0]["Product_Barcode"].ToString().Trim().ToUpper());
                colorbox = colorbox.Replace("$MEID_HEX_14$", drr[0]["PHYSICSNO"].ToString().Trim().ToUpper());
                colorbox = colorbox.Replace("$IMEI_2$", drr[0]["IMEI2"].ToString().Trim().ToUpper());
                colorbox = colorbox.Replace("$EAN$", EANText);
                colorbox = colorbox.Replace("$YYYY$", yy);
                colorbox = colorbox.Replace("$MM$", mm);
            }
            ZPLPrint zp = new ZPLPrint();
            if (chkPrintColorBox.Checked)
            {
                zp.ZPL_Print(colorbox, ColorPrintName);
            }
            if (chkPrintBack.Checked)
            {
                zp.ZPL_Print(back, BackPrintName);
            }
            if (chkPrintXC.Checked)
            {
                zp.ZPL_Print(XCText, XCPrintName);
            }

            UpdatePrintData();
            tbNetCode.Text = "";
            laPrintShow.Text = "打印成功";
            if (chkCode.Checked)
            {
                tbChkColorMeid.Focus();
                tbChkColorMeid.SelectAll();
            }
            else
            {
                tbNetCode.Focus();
                tbNetCode.SelectAll();
            }
         
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {

            tbNetCode.Text =netcode= "";
            tbPsid.Text =psid ="";
            ColorBoxText = BackText = XCText =EANText= "";
            tbPsid.Enabled = true;
            btGetPsid.Enabled = true;
            ApplyPrint = false;
            tbNetCode.Focus();
            tbNetCode.SelectAll();
        }

        private void cb_MD_print_SelectedIndexChanged(object sender, EventArgs e)
        {
            ScriptText = ScriptText.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim());
        }


        private void bt_GetPsid_Click(object sender, EventArgs e)
        {
            laChkShow.Text = "";
            laPrintShow.Text = "";
            celt = WXMESQuery.GetCELT(tbPsid.Text.Trim());
            if (celt.Rows.Count==0)
            {
                MessageBox.Show("系统没有该任务令的CELT数据!");
                return;
            }
            psid = tbPsid.Text.Trim();
            if (celt.Select("model='" + celt.Rows[0]["model"].ToString() +"'").Length!=celt.Rows.Count)
            {
                MessageBox.Show("该任务令的CELT数据中,有机型不一样的数据!");
                return;
            }
            if (celt.Select("MAT_ID='" + celt.Rows[0]["MAT_ID"].ToString() + "'").Length != celt.Rows.Count)
            {
                MessageBox.Show("该任务令的CELT数据中,有编码不一样的数据!");
                return;
            }            

           ColorBoxText= PrintService.Get_ZPLScript("彩盒标",celt.Rows[0]["MAT_ID"].ToString());
            if (ColorBoxText=="")
            {
                MessageBox.Show($"编码{ celt.Rows[0]["MAT_ID"].ToString()}的彩盒标数据不存在!");
                return;
            }
            ColorBoxText = ColorBoxText.Replace("?","").Replace("?", "").Replace("?", "").Replace("?", "");
            string phonemodel = celt.Rows[0]["model"].ToString().Substring(0, celt.Rows[0]["model"].ToString().Length - 1);

            BackText = PrintService.Get_ZPLScript("背贴", phonemodel);
            if (ColorBoxText == "")
            {
                MessageBox.Show($"机型{ phonemodel}的背贴数据不存在!");
                return;
            }
            DataTable dt= WXMESQuery.QueryCodeData(celt.Rows[0]["MAT_ID"].ToString(), "整机");
            if (dt.Rows.Count==0)
            {
                MessageBox.Show($"编码{ celt.Rows[0]["MAT_ID"].ToString()}数据系统不存在!");
                return;
            }
            if (dt.Rows[0]["ean"].ToString().Trim().Length!=13)
            {
                MessageBox.Show($"编码{ celt.Rows[0]["MAT_ID"].ToString()}的XC69码不规范!");
                return;
            }
            EANText ="XC"+ dt.Rows[0]["ean"].ToString().Trim().ToUpper();

            XCText = PrintService.Get_ZPLScript("内购标", "ALL");
            for (int j = 0; j < 3; j++)
            {
                XCText = XCText.Replace("$EAN$", EANText);
            }
         
            tbPsid.Enabled = false;
            btGetPsid.Enabled = false;
            tbNetCode.Focus();
            tbNetCode.SelectAll();

        }

        private void UpdatePrintData()
        {
            string date = PublicService.serverTime().ToString();
            if (celt.Rows[0]["printqty"].ToString()=="")
            {
                celt.Rows[0]["printqty"] = "0";
            }
            int qty = int.Parse(celt.Rows[0]["printqty"].ToString()) + 1;
            string sql = "update celt_data set printqty='" + qty + "'  where workorder='" + psid + "' and netcode='" + netcode + "'";
            SqlHelperMES.ExecuteSql(sql);


            if (ApplyPrint)
            {
                sql = "update OrderApply set approve='2',lasttime='" + date + "',lastbyid='" + Login.usercode + "' where type='网标打印' and code='" + netcode + "'";
                SqlHelperMES.ExecuteSql(sql);
            }         
        }

        private void tbNetCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {

            ZPLPrint zp = new ZPLPrint();
            for (int j = 0; j < 3; j++)
            {
                XCText = XCText.Replace("$EAN$", "6900000000000");
            }
            zp.ZPL_PrintShowName(XCText);
            //string tt= "KonanZ-AL00C";
            //MessageBox.Show(tt.Substring(0,tt.Length-1));
        }

        private void tbPsid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_GetPsid_Click(null,null);
            }
        }

        private void btCheckCode_Click(object sender, EventArgs e)
        {
            if (tbChkColorMeid.Text.Trim() == "")
            {
                tbChkColorMeid.Focus();
                tbChkColorMeid.SelectAll();
                return;
            }
            if (tbChkNetCode.Text.Trim() == "")
            {
                tbChkNetCode.Focus();
                tbChkNetCode.SelectAll();
                return;
            }
            if (tbBackMeid.Text.Trim() == "")
            {
                tbBackMeid.Focus();
                tbBackMeid.SelectAll();
                return;
            }
            //if (tbChkColorMeid.Text.Trim().ToUpper()!=tbBackMeid.Text.Trim().ToUpper())
            //{
            //    MessageBox.Show("彩盒标的MEID与背贴的MEID不一致");
            //    return;
            //}

            DataTable dt = WXMESQuery.GetCELT_Meid(tbChkColorMeid.Text.Trim());
            if (dt.Rows.Count==0)
            {
                MessageBox.Show("CELT系统没有该MEID的数据");
                return;
            }
            if (tbChkNetCode.Text.Trim()!=dt.Rows[0]["netcode"].ToString().Trim())
            {
                MessageBox.Show("网标号与CELT系统的网标号不一标");
                return;
            }
            if (tbBackMeid.Text.Trim() != dt.Rows[0]["Product_Barcode"].ToString().Trim())
            {
                MessageBox.Show("网标SNCELT系统的SN号不一标");
                return;
            }
            laChkShow.Text = "保存成功";
            button2_Click(null,null);
        }

        private void tbChkColorMeid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                laChkShow.Text = "";
                laPrintShow.Text = "";
                if (tbChkColorMeid.Text.Trim() == "")
                {
                    tbChkColorMeid.Focus();
                    tbChkColorMeid.SelectAll();
                    return;
                }
                tbChkNetCode.Focus();
                tbChkNetCode.SelectAll();
            }
        }

        private void tbChkNetCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                laChkShow.Text = "";
                laPrintShow.Text = "";
                if (tbChkNetCode.Text.Trim() == "")
                {
                    tbChkNetCode.Focus();
                    tbChkNetCode.SelectAll();
                    return;
                }
                tbBackMeid.Focus();
                tbBackMeid.SelectAll();
            }
        }

        private void tbBackMeid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                laChkShow.Text = "";
                laPrintShow.Text = "";
                if (tbBackMeid.Text.Trim() == "")
                {
                    tbBackMeid.Focus();
                    tbBackMeid.SelectAll();
                    return;
                }
                btCheckCode_Click(null,null);
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            tbChkNetCode.Text = tbChkColorMeid.Text = tbBackMeid.Text = "";
            if (chkCode.Checked)
            {
                tbNetCode.Focus();
                tbNetCode.SelectAll();
            }
            else {
                tbChkColorMeid.Focus();
                tbChkColorMeid.SelectAll();
            }

        }

        private void tbNetCode_ImeModeChanged(object sender, EventArgs e)
        {

        }
    }
}

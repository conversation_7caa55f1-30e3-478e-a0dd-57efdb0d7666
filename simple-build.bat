@echo off
setlocal enabledelayedexpansion

echo [INFO] Simple NuGet package builder for EasyWork.Honor v1.2.2

REM Check if Release build exists
if not exist "bin\Release\EasyWork.Honor.exe" (
    echo [ERROR] Release build not found!
    echo [INFO] Please build the project first:
    echo [INFO] 1. Open Visual Studio
    echo [INFO] 2. Set configuration to Release
    echo [INFO] 3. Build the project
    echo [INFO] 4. Then run this script again
    goto :end
)

echo [INFO] Found Release build, proceeding...

REM Download nuget.exe if not available
if not exist "nuget.exe" (
    echo [INFO] Downloading nuget.exe...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile 'nuget.exe' } catch { Write-Host 'Download failed' }"
)

if not exist "nuget.exe" (
    echo [ERROR] nuget.exe not available
    echo [INFO] Please download nuget.exe manually from https://www.nuget.org/downloads
    goto :end
)

echo [INFO] Creating NuGet package...
nuget.exe pack EasyWork.Honor.nuspec -Version 1.2.2 -OutputDirectory bin\Release
if errorlevel 1 (
    echo [ERROR] NuGet pack failed
    goto :end
)

echo [INFO] Running Squirrel to generate installer...
pushd bin\Release
if exist "Setup.exe" del "Setup.exe"
if exist "RELEASES" del "RELEASES"

..\..\packages\squirrel.windows.2.0.1\tools\Squirrel.exe --releasify EasyWork.Honor.1.2.2.nupkg --releaseDir=.
if errorlevel 1 (
    echo [ERROR] Squirrel failed
    popd
    goto :end
)

popd

echo [SUCCESS] Package creation completed!
echo [INFO] Generated files:
if exist "bin\Release\EasyWork.Honor.1.2.2.nupkg" echo   - EasyWork.Honor.1.2.2.nupkg
if exist "bin\Release\Setup.exe" echo   - Setup.exe  
if exist "bin\Release\RELEASES" echo   - RELEASES

echo.
echo [NEXT] You can now run: publish.bat

:end
pause

﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.44.1" targetFramework="net48" />
  <package id="DeltaCompressionDotNet" version="1.1.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.Memory" version="9.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="8.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.1.0" targetFramework="net48" />
  <package id="Microsoft.Graph" version="5.81.0" targetFramework="net48" />
  <package id="Microsoft.Graph.Core" version="3.2.4" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Validators" version="8.6.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Abstractions" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Authentication.Azure" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Http.HttpClientLibrary" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Serialization.Form" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Serialization.Json" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Serialization.Multipart" version="1.17.1" targetFramework="net48" />
  <package id="Microsoft.Kiota.Serialization.Text" version="1.17.1" targetFramework="net48" />
  <package id="Mono.Cecil" version="0.11.2" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net472" />
  <package id="SharpCompress" version="0.17.1" targetFramework="net48" />
  <package id="squirrel.windows" version="2.0.1" targetFramework="net48" />
  <package id="Std.UriTemplate" version="2.0.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.ClientModel" version="1.1.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.2" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="8.6.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Memory.Data" version="6.0.0" targetFramework="net48" />
  <package id="System.Net.Http.WinHttpHandler" version="6.0.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding.CodePages" version="4.5.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>
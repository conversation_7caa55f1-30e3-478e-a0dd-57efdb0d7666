﻿using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using System;
using System.Data;
using System.Windows.Forms;
using System.Threading;
using EasyWork.Honor.Service.WXMES;
using EasyWork.Honor.Service;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Tab;
using static System.Windows.Forms.Design.AxImporter;

namespace EasyWork.Honor.WXMES
{
    public partial class AutoReadWXReport : Form
    {
        private delegate void delInfoList(string text);//申明委托，防止不同线程设置richtextbox时出现错误
        IWebDriver selenium;
        DataTable dt=new DataTable();
        bool shop = true;
        int qty = 5;
        Thread th;
        public AutoReadWXReport()
        {
            InitializeComponent();
            dt.Columns.Add("batch");
            dt.Columns.Add("Psid");
            dt.Columns.Add("InitialCode");
            dt.Columns.Add("InitialCodeType");
            dt.Columns.Add("Type");
            dt.Columns.Add("Difficulty");
            dt.Columns.Add("PhoneModel");
            dt.Columns.Add("PhoneType");
            dt.Columns.Add("PhoneLine");
            dt.Columns.Add("TargetCode");
            dt.Columns.Add("TargetCodeType");
            dt.Columns.Add("TargetCodePhoneCode");
            dt.Columns.Add("Countries");
            dt.Columns.Add("VeneerCode");
            dt.Columns.Add("SN");
            dt.Columns.Add("OriginalFault");
            dt.Columns.Add("ConfirmFault");
            dt.Columns.Add("WXMethods");
            dt.Columns.Add("Results");
            dt.Columns.Add("WXDes");
            dt.Columns.Add("FailurePartsCode");
            dt.Columns.Add("FailurePartsQty");
            dt.Columns.Add("NewImei");
            dt.Columns.Add("NewSN");
            dt.Columns.Add("NewVeneerCode");
            dt.Columns.Add("WXQty");
            dt.Columns.Add("Remarks");
            dt.Columns.Add("CompleteTime");
            dt.Columns.Add("State");
            dt.Columns.Add("CreateById");
            dt.Columns.Add("CreateTime");
            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;
            TopMost = true;
            th = new Thread(StartAuto);
        }

        void StartAuto()
        {
            ChromeOptions options = new ChromeOptions();
            //options.AddArgument("--headless");

            SetrichTextBox(DateTime.Now + "：系统登陆中......\n");
            selenium = new ChromeDriver(System.AppDomain.CurrentDomain.BaseDirectory.ToString(),options);


            DataTable dt = PublicService.QueryAccount("TGMES01");
            if (dt.Rows.Count < 1)
            {
                return;
            }
            string uid = dt.Rows[0]["uid"].ToString();
            string pwd = dt.Rows[0]["pwd"].ToString();

            selenium.Navigate().GoToUrl("https://authex.hihonor.com/uniportal1/?redirect=http%3A%2F%2Fhmes.hihonor.com%2Fmmsweb%2F%23%2F"); 
            selenium.Manage().Window.Maximize();
            Thread.Sleep(500);
            try
            {
                while (shop)
                {
                    selenium.FindElement(By.Id("username")).SendKeys(uid);
                    selenium.FindElement(By.XPath("//*[@type='password']")).SendKeys(pwd);
                    selenium.FindElement(By.XPath("//*[@type='submit']")).Click();
                    SetrichTextBox(DateTime.Now + "：系统登陆完成,正在转到指定页面......\n");
                    Thread.Sleep(1000);
                    selenium.Navigate().GoToUrl("http://hmes.hihonor.com/mmsweb/#/qm/gw-repair-report/GwRepairReport"); 
                    Thread.Sleep(4000);
                    SetrichTextBox(DateTime.Now + "：已转到指定页面,正在读取系统最后保存时间并设置查询时间段......\n");
                    int k = 0;
                    while (shop)
                    {

                        dt.Rows.Clear();
                        DateTime startdate = WXMESQuery.GetWXReportLastTime();
                        Thread.Sleep(1000);
                        try
                        {
                            //  selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[1]/input")).Clear();
                              selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[1]/span/span/i[1]")).Click();

                            Thread.Sleep(1000);
                            selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[1]/input")).SendKeys(startdate.ToString("yyyy-MM-dd HH:mm:ss"));
                            Thread.Sleep(500);


                            selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[2]/input")).Clear();
                            Thread.Sleep(500);
                            selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[2]/input")).SendKeys(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            Thread.Sleep(500);
                            selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[3]/button[1]")).Click();
                            string date = selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[1]/input")).GetAttribute("value");
                            SetrichTextBox(DateTime.Now + $"：开始时间{date},结束时间{selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/form/div[2]/div[3]/div/div/div[2]/input")).GetAttribute("value")}查询数据中......\n");
                            Thread.Sleep(3000);
                           
                            if (startdate.ToString("yyyy-MM-dd HH:mm:ss") != date)
                            {
                                SetrichTextBox(DateTime.Now + $"：时间段设置有误,重新设置......\n");
                                continue;
                            }
                        }
                        catch
                        {
                            k++;
                            if (k>5)
                            {
                                break;
                            }
                            continue;
                        }


                        string total = selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/div[2]/span[1]")).Text.Split('共')[1].Split('条')[0];
                        if (int.Parse(total) > 0)
                        {
                            SetrichTextBox(DateTime.Now + $"：已查询到{total}条数据,准备读取中......\n");
                        }
                        else
                        {
                            SetrichTextBox(DateTime.Now + $"：本次查询不到有新数据,等待{qty}分钟后再重新查询......\n");
                            Thread.Sleep(60000 * qty);
                            continue;
                        }

                        Thread.Sleep(500);
                        int j = 0;

                        dt = new DataTable();
                        dt.Columns.AddRange(new DataColumn[] {
                            new DataColumn("Psid"),
                            new DataColumn("InitialCode"),
                            new DataColumn("InitialCodeType"),
                            new DataColumn("Type"),
                            new DataColumn("Difficulty"),
                            new DataColumn("PhoneModel"),
                            new DataColumn("PhoneType"),
                            new DataColumn("PhoneLine"),
                            new DataColumn("TargetCode"),
                            new DataColumn("TargetCodeType"),
                            new DataColumn("TargetCodePhoneCode"),
                            new DataColumn("Countries"),
                            new DataColumn("VeneerCode"),
                            new DataColumn("SN"),
                            new DataColumn("OriginalFault"),
                            new DataColumn("ConfirmFault"),
                            new DataColumn("WXMethods"),
                            new DataColumn("Results"),
                            new DataColumn("WXDes"),
                            new DataColumn("FailurePartsCode"),
                            new DataColumn("FailurePartsQty"),
                            new DataColumn("NewImei"),
                            new DataColumn("NewSN"),
                            new DataColumn("NewVeneerCode"),
                            new DataColumn("WXQty"),
                            new DataColumn("Remarks"),
                            new DataColumn("CompleteTime"),
                            new DataColumn("State"),
                            new DataColumn("LastTime"),
                            new DataColumn("CreateById"),
                            new DataColumn("CreateTime")
                    });

                        while (true)
                        {
                            for (int i = 1; i < 102; i++)
                            {
                                try
                                {
                                    string veneercode = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[25]")).Text;
                                    string psid = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[9]/div[2]/table/tbody/tr[" + i + "]/td[4]")).Text;
                                    if (psid == "")
                                    {
                                        var a = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[9]/div[2]/table/tbody/tr[" + i + "]/td[4]"));
                                        ((IJavaScriptExecutor)selenium).ExecuteScript("arguments[0].scrollIntoView(true);", a);
                                        Thread.Sleep(10);
                                        psid = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[9]/div[2]/table/tbody/tr[" + i + "]/td[4]")).Text;
                                        if (psid == "")
                                        {
                                            i--;
                                            continue;
                                        }
                                    }
                                }
                                catch
                                {
                                    break;
                                }
                                DataRow dr = dt.NewRow();
                                dr["Psid"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[9]/div[2]/table/tbody/tr[" + i + "]/td[4]/div")).Text;
                                dr["InitialCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[7]")).Text;
                                dr["InitialCodeType"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[8]")).Text;
                                dr["Type"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[12]")).Text;
                                dr["Difficulty"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[14]")).Text;
                                dr["PhoneModel"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[15]")).Text;
                                dr["PhoneType"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[16]")).Text;
                                dr["PhoneLine"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[17]")).Text;
                                dr["TargetCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[19]")).Text;
                                dr["TargetCodeType"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[20]")).Text;
                                dr["TargetCodePhoneCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[21]")).Text;
                                dr["Countries"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[22]")).Text;
                                dr["VeneerCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[25]")).Text;
                                dr["SN"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[26]")).Text;
                                dr["OriginalFault"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[28]")).Text;
                                dr["ConfirmFault"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[29]")).Text;
                                dr["WXMethods"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[30]")).Text;
                                dr["Results"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[31]")).Text;
                                dr["WXDes"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[32]")).Text;
                                dr["FailurePartsCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[35]")).Text;
                                dr["FailurePartsQty"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[36]")).Text;
                                dr["NewImei"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[37]")).Text;
                                dr["NewSN"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[38]")).Text;
                                dr["NewVeneerCode"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[39]")).Text;
                                dr["WXQty"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[40]")).Text;
                                dr["Remarks"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[42]")).Text;
                                dr["CompleteTime"] = selenium.FindElement(By.XPath("//*[@id=\"xui-table_1\"]/div[8]/div[1]/div/div[1]/table/tbody/tr[" + i + "]/td[43]")).Text;
                                dr["CreateById"] = "Sys-" + Login.usercode;
                                dr["State"] = "Normal";
                                dr["CreateTime"] = DateTime.Now;
                                dt.Rows.Add(dr);
                                j++;
                                Thread.Sleep(10);
                            }

                            SetrichTextBox(DateTime.Now + $"：已读取{j}条数据......\n");
                            Thread.Sleep(100);
                            string disabled = selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/div[2]/div/button[2]")).GetAttribute("disabled");
                            if (disabled == "true")
                            {
                                SetrichTextBox(DateTime.Now + $"：读取完毕,共读取{j}条数据......\n");
                                break;
                            }
                            selenium.FindElement(By.XPath("//*[@id=\"app\"]/div/div[2]/section/div/div[2]/div/button[2]")).Click();
                            SetrichTextBox(DateTime.Now + $"：准备读取下一面页......\n");
                            Thread.Sleep(3000);
                        }
                        int dttotal = dt.Rows.Count;
                        if (dttotal != int.Parse(total))
                        {
                            SetrichTextBox(DateTime.Now + $"：本次读取数据{dt.Rows.Count}条,实际数据{total},读取有误,重新读取......\n");
                            continue;
                        }
                        //dataGridView2.DataSource = dt;
                        //ExportToExcel p=new ExportToExcel();
                        //p.OutputAsExcelFile(dataGridView2);

                        SetrichTextBox(DateTime.Now + $"：正在与系统匹配数据,去除重复值......\n");
                        dt.DefaultView.Sort = "Completetime asc";
                        j = 0;
                        for (int i = dt.Rows.Count - 1; i > -1; i--)
                        {
                            if (WXMESQuery.GetPsidVeneerCode(dt.Rows[i]["psid"].ToString(), dt.Rows[i]["veneercode"].ToString()).Rows.Count > 0)
                            {
                                j++;
                                dt.Rows.RemoveAt(i);
                            }
                        }
                        SetrichTextBox(DateTime.Now + $"：匹配完毕,共匹配重复值{j}条数,......\n");
                        Thread.Sleep(100);
                        if (dt.Rows.Count == 0)
                        {
                            SetrichTextBox(DateTime.Now + $"：无数据保存,等待{qty}分钟后再重新查询......\n");
                        }
                        else
                        {
                            SetrichTextBox(DateTime.Now + $"：准备保存数据......\n");
                            Thread.Sleep(100);
                            SqlHelperMES.BulkInsert(dt, "WXReport");
                            SetrichTextBox(DateTime.Now + $"：保存完毕,共保存{dt.Rows.Count}条数据......\n");
                            Thread.Sleep(100);
                            SetrichTextBox(DateTime.Now + $"：等待{qty}分钟后再重新查询......\n");
                        }
                        Thread.Sleep(60000 * qty);
                    }
                }
            }
            catch 
            {

            }
        }    

        void SetrichTextBox(string value)
        {
            if (rt1.InvokeRequired)
            {
                delInfoList d = new
                delInfoList(SetrichTextBox);
                rt1.Invoke(d, value);
            }
            else
            {
                rt1.Focus();
                rt1.Select(rt1.TextLength, 0);
                rt1.ScrollToCaret();
                rt1.AppendText(value);
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            button2.Enabled = false;
          th.Start();
        }

      

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {         
        }

        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {
            shop = false;
            try
            {
                th.Interrupt();
            }
            catch
            {

            }
         
        }
    }
}

﻿using EasyWork.bll.Public;
using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class CheckReportPrint : Form
    {
        string sn = "";
        string psid = "";
        string model = "";
        string code = "";
        string type = "";
        string error = "";
        string checkname = "";
        string checkdate = "";
        DataTable gzdt;
        DataTable gzpsiddt;

        public CheckReportPrint()
        {
            InitializeComponent();
            printPreviewDialog1.Width = 100;
            printPreviewDialog1.Height = 300;
            //printDocument1.DefaultPageSettings.Landscape = true;
            this.printDocument1.OriginAtMargins = true;//启用页边距
            this.pageSetupDialog1.EnableMetric = true; //以毫米为单位    


            tblr.Text = PublicService.GetConfigData("WXPsidWorkLR", string.Empty);
            tbud.Text = PublicService.GetConfigData("WXPsidWorkUD", string.Empty);
            if (tblr.Text == "")
            {
                tblr.Text = "0";
            }
            if (tbud.Text == "")
            {
                tbud.Text = "0";
            }
            tb_psid.Focus();
        }

         private void printDocument1_PrintPage(object sender, System.Drawing.Printing.PrintPageEventArgs e)
         {
            int x = int.Parse(tblr.Text.Trim())-3;
            int y = int.Parse(tbud.Text.Trim());
            BarCode.Code128 _Code = new BarCode.Code128();
            //string sn = "AJVVUT1222002688";
            //string psid = "RBBKK15H1B3";
            //string model = "York-AN10C";
            //string code = "03031234-001";
            //string checkname = "李永寿";
            //string checkdate = "2021-08-07";
            //string error = "不开机，就是不开机啊,屏幕有非常严重的划伤";



            //大横线
            e.Graphics.DrawLine(Pens.Black, 200+x, -100+y, -85+x, -100+y);//1
            e.Graphics.DrawLine(Pens.Black, 200+x, 85+y, -85+x, 85+y);//1


            e.Graphics.DrawLine(Pens.Black, 200 + x, -60 + y, -85 + x, -60 + y);//1
            e.Graphics.DrawLine(Pens.Black, 200 + x, -20 + y, -85 + x, -20 + y);//1
            e.Graphics.DrawLine(Pens.Black, 200 + x, 20 + y, -85 + x, 20 + y);//1

            ////大竖线
            e.Graphics.DrawLine(Pens.Black, -85 + x, -100 + y, -85 + x, 85 + y);
            e.Graphics.DrawLine(Pens.Black, 200 + x, -100 + y, 200 + x, 85 + y);
            
            e.Graphics.DrawLine(Pens.Black, -35 + x, -100 + y, -35 + x, 85 + y);
            e.Graphics.DrawLine(Pens.Black, 55 + x, -100 + y, 55 + x, -60 + y);
            e.Graphics.DrawLine(Pens.Black, 95 + x, -100 + y, 95 + x, 20 + y);
            e.Graphics.DrawLine(Pens.Black, 130 + x, -60 + y, 130 + x, 20 + y);




            //任务令
            e.Graphics.DrawString("任务令:", new Font("宋体", (float)10), Brushes.Black,-86+x, -85 + y);
            //psid
            e.Graphics.DrawString(psid, new Font("宋体", (float)10), Brushes.Black, -34 + x, -85 + y);
            //机型
            e.Graphics.DrawString("机型:", new Font("宋体", (float)10), Brushes.Black, 56 + x, -85 + y);
            e.Graphics.DrawString(model, new Font("宋体", (float)10), Brushes.Black, 96 + x, -85 + y);


            //SN
            e.Graphics.DrawString("SN:", new Font("宋体", (float)10), Brushes.Black, -57 + x, -45 + y);
            //SN
            e.Graphics.DrawString(sn, new Font("宋体", (float)10), Brushes.Black, -34 + x, -45 + y);
            //检测员
            e.Graphics.DrawString("检测", new Font("宋体", (float)10), Brushes.Black, 96 + x, -55 + y);
            //检测员
            e.Graphics.DrawString("员:", new Font("宋体", (float)10), Brushes.Black, 107 + x, -35 + y);
            //人名
            e.Graphics.DrawString(checkname, new Font("宋体", (float)10), Brushes.Black, 128 + x, -45 + y);




            //编码
            e.Graphics.DrawString("编码:", new Font("宋体", (float)10), Brushes.Black, -71 + x, -3 + y);
            //code
            e.Graphics.DrawString(code, new Font("宋体", (float)10), Brushes.Black, -34 + x, -3 + y);
            //检测日期
            e.Graphics.DrawString("检测", new Font("宋体", (float)10), Brushes.Black, 96 + x, -13 + y);
            //检测日期
            e.Graphics.DrawString("日期:", new Font("宋体", (float)10), Brushes.Black, 93 + x, 5 + y);
            //日期
            e.Graphics.DrawString(checkdate, new Font("宋体", (float)9.3), Brushes.Black, 129 + x, -3 + y);







            //检测
            e.Graphics.DrawString("检测", new Font("宋体", (float)10), Brushes.Black, -71 + x, 38 + y);
            //故障
            e.Graphics.DrawString("故障:", new Font("宋体", (float)10), Brushes.Black, -71 + x, 53 + y);
            //error

            if (error.ToString().Trim()=="")
            {
                error = "良品";
            }
            if (error.Length<16)
            {
                e.Graphics.DrawString(error, new Font("宋体", (float)10), Brushes.Black, -34 + x, 28 + y);
            }
            else
            {
                e.Graphics.DrawString(error.Substring(0,16), new Font("宋体", (float)10), Brushes.Black, -34 + x, 28 + y);
                if (error.Length<32)
                {
                    e.Graphics.DrawString(error.Substring(16, error.Length - 16), new Font("宋体", (float)10), Brushes.Black, -34 + x, 45 + y);
                }
                else
                {
                    e.Graphics.DrawString(error.Substring(16, 16), new Font("宋体", (float)10), Brushes.Black, -34 + x, 45 + y);
                    e.Graphics.DrawString(error.Substring(32, error.Length - 32), new Font("宋体", (float)10), Brushes.Black, -34 + x, 62 + y);
                }

              
            }





        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            if (tb_psid.Text.Trim()=="")
            {
                tb_psid.Focus();
                tb_psid.SelectAll();
                return;
            }
            if (tb_sn.Text.Trim()=="")
            {
                tb_sn.Focus();
                tb_sn.SelectAll();
                return;
            }


            string sql = "select *,(select top 1 username from mes_honor.dbo.Sys_Users where usercode=a.createbyid) as username,(select top 1 phonemodel from mes_honor.dbo.gzmes_psid where psid=a.psid) as phonemodel from MES_Honor.dbo.GZMES as a where sn='" + tb_sn.Text.Trim()+"' and psid='"+tb_psid.Text.Trim()+"'";
            gzdt = SqlHelper.Query(sql).Tables[0];
            if (gzdt.Rows.Count==0)
            {
                MessageBox.Show("系统没有该任务令的SN数据");
                tb_sn.Focus();
                tb_sn.SelectAll();
                return;
            }

            if (gzdt.Rows[0]["Phonemodel"].ToString()=="")
            {
                MessageBox.Show("系统没有该任务令数据");
                tb_sn.Focus();
                tb_sn.SelectAll();
                return;
            }

            sn = gzdt.Rows[0]["sn"].ToString();
            code = gzdt.Rows[0]["targetcode"].ToString();
            error = gzdt.Rows[0]["OriginalFault"].ToString();
            checkname = gzdt.Rows[0]["username"].ToString();
            checkdate = gzdt.Rows[0]["createtime"].ToString().Substring(0,10);
            psid = gzdt.Rows[0]["psid"].ToString();
            model = gzdt.Rows[0]["phonemodel"].ToString();


            //printPreviewDialog1.Document = printDocument1;
           // printPreviewDialog1.ShowDialog();
            printDocument1.Print();
            UpdatePrintGongDan();
            //InsertLog(psid,model,incode,code, dt.Rows[0]["outtype"].ToString(),imei,sn,bsn,error,type);
            tb_sn.Text = "";
            tb_sn.Focus();
        }

        public void UpdatePrintGongDan()
        {
            string sql= "update mes_honor.dbo.gzmes set remarks='已打检测报告' where sn='" + tb_sn.Text.Trim() + "' and psid='" + tb_psid.Text.Trim() + "'";
            SqlHelper.ExecuteSql(sql);

        }

        public void InsertLog(string psid,string phonemodel,string incode,string outcode,string outtype,string old_imei,string sn,string veneercode,string fault,string type)
        {
            string sql = "insert into mes_honor.dbo.wxmes_log(psid,phonemodel,initialcode,targetcode,outtype,old_imei,sn,veneercode,originalfault,location,approve,type,operatetype,createbyid,createtime) values('"+psid+ "','"+phonemodel+ "','"+incode+ "','"+outcode+ "','"+outtype+ "','"+old_imei+ "','"+sn+ "','"+veneercode+ "','"+fault+ "','工单打印','1','"+type+ "','PsidPlan','"+Login.usercode+"',getdate())";
            SqlHelper.ExecuteSql(sql);
        }

        private void tblr_Validated(object sender, EventArgs e)
        {
            if (!int.TryParse(tblr.Text.Trim(),out int _dec))
            {
                tblr.Text = "0";
            }
            PublicService.WriteConfigData("WXPsidWorkLR",tblr.Text.Trim());
        }

        private void tbud_Validated(object sender, EventArgs e)
        {
            if (!int.TryParse(tbud.Text.Trim(), out int _dec))
            {
                tbud.Text = "0";
            }
            PublicService.WriteConfigData("WXPsidWorkUD", tbud.Text.Trim());
        }    

        private void tb_sn_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }

        public static int GetLength(string str)
        {
            if (str.Length == 0)
                return 0;
            ASCIIEncoding ascii = new ASCIIEncoding();
            int tempLen = 0;
            byte[] s = ascii.GetBytes(str);
            for (int i = 0; i < s.Length; i++)
            {
                if ((int)s[i] == 63)
                {
                    tempLen += 2;
                }
                else
                {
                    tempLen += 1;
                }
            }
            return tempLen;
        }

        private void tb_psid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bt_Print_Click(null, null);
            }
        }
    }
}

﻿[20/05/25 21:12:44] info: Program: Starting Squirrel Updater: --releasify EasyWork.Honor.nupkg --releaseDir=E:\Wangxianqi\Release\Packing_2
[20/05/25 21:12:44] info: Program: Bootstrapper EXE found at:E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Debug\Setup.exe
[20/05/25 21:12:44] fatal: Finished with unhandled exception: System.IO.FileNotFoundException: 未能找到文件“EasyWork.Honor.nupkg”。
文件名:“EasyWork.Honor.nupkg”
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.File.InternalCopy(String sourceFileName, String destFileName, Boolean overwrite, Boolean checkHost)
   在 Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   在 Squirrel.Update.Program.executeCommandLine(String[] args)
   在 Squirrel.Update.Program.main(String[] args)
[20/05/25 21:13:23] info: Program: Starting Squirrel Updater: --releasify MyApp-1.0.0.nupkg --releaseDir=C:\MyApp\Releases
[20/05/25 21:13:23] info: Program: Bootstrapper EXE found at:E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Debug\Setup.exe
[20/05/25 21:13:23] fatal: Finished with unhandled exception: System.IO.FileNotFoundException: 未能找到文件“MyApp-1.0.0.nupkg”。
文件名:“MyApp-1.0.0.nupkg”
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.File.InternalCopy(String sourceFileName, String destFileName, Boolean overwrite, Boolean checkHost)
   在 Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   在 Squirrel.Update.Program.executeCommandLine(String[] args)
   在 Squirrel.Update.Program.main(String[] args)
[20/05/25 21:14:27] info: Program: Starting Squirrel Updater: --releasify EasyWork.Honor.exe --releaseDir=E:\Wangxianqi\Release\Packing_2
[20/05/25 21:14:27] info: Program: Bootstrapper EXE found at:E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Debug\Setup.exe
[20/05/25 21:14:27] fatal: Finished with unhandled exception: System.InvalidOperationException: Source sequence doesn't contain any elements.
   在 Squirrel.EnumerableExtensions.ExtremaBy[TSource,TKey](IEnumerable`1 source, Func`2 keySelector, Func`3 compare)
   在 Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   在 Squirrel.Update.Program.executeCommandLine(String[] args)
   在 Squirrel.Update.Program.main(String[] args)
[20/05/25 21:14:35] info: Program: Starting Squirrel Updater: --releasify EasyWork.Honor.exe --releaseDir=E:\Wangxianqi\Release\Packing_2
[20/05/25 21:14:35] info: Program: Bootstrapper EXE found at:E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Debug\Setup.exe
[20/05/25 21:14:35] fatal: Finished with unhandled exception: System.InvalidOperationException: Source sequence doesn't contain any elements.
   在 Squirrel.EnumerableExtensions.ExtremaBy[TSource,TKey](IEnumerable`1 source, Func`2 keySelector, Func`3 compare)
   在 Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   在 Squirrel.Update.Program.executeCommandLine(String[] args)
   在 Squirrel.Update.Program.main(String[] args)

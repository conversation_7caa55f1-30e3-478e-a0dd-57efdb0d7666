﻿using EasyWork.bll.Login;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text.RegularExpressions;
using EasyWork.Honor.Service;
using System.IO;
using System.Net;
using Squirrel;

namespace EasyWork.Honor
{
    public partial class Login : Form
    {
        public static string usercode = "";
        public static string username;
        public static string ip;
        public static string ver, ser;
        public static string pwd;
        public static string mac;
        public static string pcname;
        public static string batch;
        public static string winver;
        public static string hdd;
        public static string memory;
        public static string cpu, cpuid;
        public Login()
        {
            InitializeComponent();
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
            ver = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
            ip = LoginService.ip();
            mac = LoginService.GetMacAddress();
            pcname = LoginService.GetSystemName();
            winver = LoginService.GetWindowsVer();
            cpu = LoginService.GetProcessor();
            memory = LoginService.GetMemory();
            // hdd = LoginService.GetDisk();
            cpuid = LoginService.GetCpuInfo();
            Version.Text = "软件版本：V_" + PublicService.version(ver);
            lbip.Text = "本机IP：" + ip;
            lbmac.Text = "MAC:" + mac;
            lbpcname.Text = "PCName:" + pcname;
            lbwinver.Text = "系统版本:" + winver;
            lbcpu.Text = "CPU:" + cpu;
            lbmemory.Text = "内存:" + memory;

            tbuser.Text = PublicService.GetConfigData("usercode", string.Empty);
            ActiveControl = tbpwd;
            if (tbuser.Text.Trim() == "")
            {
                tbuser.Focus();
            }
            else
            {
                tbpwd.Focus();
            }
        }

        private void Btlogin_Click(object sender, EventArgs e)
        {
            //float sqlver = PublicService.QueryVersion();
            //if (sqlver > PublicService.version(ver))
            //{
            //    MessageBox.Show("您的版本过低,请升级后再登陆,新版本位置在 用浏览器登陆http://***********里面的 \\Install Files\\百沃系统\\EasyWork.Honor,无帐户者可使用 read----123456 登陆!", "提示");

            //    //System.Diagnostics.Process.Start(".\\UpLoad.exe");
            //    this.Close();
            //    Application.Exit();
            //    return;
            //}
            if (tbuser.Text.Trim() == "" || tbpwd.Text.Trim() == "")
            {
                MessageBox.Show("请输入帐号密码!", "提示");
                return;
            }
            DataTable dt = LoginService.login(tbuser.Text.Trim());
            string newpwd = PublicService.MD5Encrypt32(tbpwd.Text.Trim());
            if (dt.Rows[0]["password"].ToString() != tbpwd.Text.Trim())
            {
                if (dt.Rows[0]["password"].ToString() != newpwd)
                {
                    MessageBox.Show("密码错误!", "提示");
                    return;
                }
            }
            if (dt.Rows[0]["userstate"].ToString() != "正常")
            {
                MessageBox.Show("帐号已停用!", "提示");
                return;
            }
            bool regex = Regex.IsMatch(tbpwd.Text.Trim(), @"^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,16}$");
            if (!regex && tbuser.Text.Trim() != "admin")
            {
                MessageBox.Show("你的密码复杂度不符合系统要求,请更改密码!", "提示");
                usercode = tbuser.Text.Trim();
                username = LoginService.username;
                pwd = tbpwd.Text.Trim();
                Manage.ChangePWD form = new Manage.ChangePWD("强制");
                form.ShowDialog();
                return;
            }

            username = LoginService.username = dt.Rows[0]["username"].ToString();
            usercode = tbuser.Text.Trim();
            pwd = tbpwd.Text.Trim();
            PublicService.WriteConfigData("usercode", usercode);
            batch = PublicService.serverTime().ToString("yyyyMMddHHmmss");
            PublicService.SaveLog(usercode, "", ver, "登陆", "Login", "Login", mac, pcname, ip, batch, winver, cpu, hdd, memory);

            Hide();
            MDI mdi = new MDI();
            mdi.Show();
        }

        private void Login_FormClosing(object sender, FormClosingEventArgs e)
        {
            Application.ExitThread();
            Environment.Exit(0);
        }

        private void Tbexit_Click(object sender, EventArgs e)
        {
            this.Close();
            Application.ExitThread();
            Environment.Exit(0);
        }
    }
}

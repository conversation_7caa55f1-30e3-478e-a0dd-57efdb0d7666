﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class ColorBoxPrint : Form
    {
        string ScriptText;
        public ColorBoxPrint()
        {
            InitializeComponent();
           
        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            if (tb_code.Text.Trim() == "")
            {
                lb_show.Text = "请输入编码";
                tb_code.Focus();
                return;
            }
            if (tb_imei1.Text.Trim() == "")
            {
                lb_show.Text = "请输入IMEI1";
                tb_imei1.Focus();
                return;
            }
            if (tb_meid.Text.Trim() == "")
            {
                lb_show.Text = "请输入MEID";
                tb_meid.Focus();
                return;
            }
            if (tb_imei2.Text.Trim() == "")
            {
                lb_show.Text = "请输入IMEI2";
               tb_imei2.Focus();
                return;
            }
            if (tb_sn.Text.Trim() == "")
            {
                lb_show.Text = "请输入SN";
                tb_sn.Focus();
                return;
            }
            string sql = "select top 1 * from mes_honor.dbo.gz_checkpsid where meid='"+tb_meid.Text.Trim()+"' and sn='"+tb_sn.Text.Trim()+"' and approve='1' order by id desc";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count==0)
            {
                MessageBox.Show("系统无该MEID号的验标信息");
                return;
            }
            if (tb_code.Text.Trim().ToUpper()!= dt.Rows[0]["InitialCode"].ToString().Trim())
            {
                MessageBox.Show("系统验标编码与输入编码不一致");
                return;
            }
            ScriptText = PrintService.Get_ZPLScript("彩盒标",dt.Rows[0]["InitialCode"].ToString().Trim()) ;
         
            if (ScriptText=="")
            {
                MessageBox.Show("系统无该编码的盒标脚本");
                return;
            }


            string text = ScriptText.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim()); ;
            for (int j = 0; j < 3; j++)
            {
                text = text.Replace("$MEID$", dt.Rows[0]["MEID"].ToString().Trim().ToUpper());
                text = text.Replace("$IMEI1$", tb_imei1.Text.Trim().ToUpper());
                text = text.Replace("$IMEI2$", tb_imei2.Text.Trim().ToUpper());
                text = text.Replace("$SN$", tb_sn.Text.Trim().ToUpper());
            }

            ZPLPrint zb = new ZPLPrint();
            zb.ZPL_Print(text);
            bt_clear_Click(null,null);
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {
            tb_meid.Text = "";
            tb_sn.Text = "";
            tb_imei2.Text = "";
            tb_imei1.Text = "";            
            tb_imei1.Focus();
        }

        private void cb_MD_print_SelectedIndexChanged(object sender, EventArgs e)
        {
            ScriptText = ScriptText.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim());
        }

        private void tb_CARTON_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }
    }
}

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<meta name="msapplication-TileImage" content="resources/images/icon_tile.png?v=4398" />
<meta name="application-name" content="百沃科技--一起荣耀" />
<meta name="msapplication-TileColor" content="#246BB3"/>
<meta name="description" content="DiskStation provides a full-featured network attached storage (NAS) solution to help you manage, backup and share data among Windows, Mac and Linux easily." />
<meta name="keywords" content="Multitasking,Web Application,Personal Cloud" />

<link rel="apple-touch-icon" href="webman/resources/images/icon_dsm_96.png?v=4398" />
<link rel="mask-icon" href="webman/safari_pin_icon.svg" color="#0086E5" />
<link rel="shortcut icon" href="webman/favicon.ico?v=4399" />
<link rel="shortcut icon" href="webman/resources/images/icon_dsm_96.png?v=4398" sizes="96x96"/>
<link rel="shortcut icon" href="webman/resources/images/icon_dsm_64.png?v=4398" sizes="64x64"/>
<link rel="shortcut icon" href="webman/resources/images/icon_dsm_48.png?v=4398" sizes="48x48"/>
<link rel="shortcut icon" href="webman/resources/images/icon_dsm_32.png?v=4398" sizes="32x32"/>
<link rel="shortcut icon" href="webman/resources/images/icon_dsm_16.png?v=4398" sizes="16x16"/>
<title>百沃科技--一起荣耀</title>
<link rel="stylesheet" type="text/css" href="scripts/ext-3/resources/css/ext-all.css?v=1589234004" />
<link rel="stylesheet" type="text/css" href="scripts/ext-3/resources/css/xtheme-gray.css?v=1589234004" />
<link rel="stylesheet" type="text/css" href="scripts/ext-3/ux/ux-all.css?v=1740209246" />
<link rel="stylesheet" type="text/css" href="synoSDSjslib/sds.css?v=1740209246" />
<link rel="stylesheet" type="text/css" href="webman/resources/css/desktop.css?v=1740209246" />
<style type="text/css">
@import url("webman/modules/ClipBoardJS/style.css?v=1589234253");
@import url("webman/modules/C3/style.css?v=1589234254");
@import url("webman/modules/StorageManager/style.css?v=1589234254");
@import url("webman/modules/EzInternet/style.css?v=1589234254");
@import url("webman/modules/SystemInfoApp/style.css?v=1589234254");
@import url("webman/modules/MyDSCenter/style.css?v=1589234254");
@import url("webman/modules/TaskSchedulerUtils/style.css?v=1589234254");
@import url("webman/modules/PollingTask/style.css?v=1589234254");
@import url("webman/modules/ConfigBackup/style.css?v=1589234249");
@import url("webman/modules/VideoPlayer2/style.css?v=1589234247");
@import url("webman/modules/Widgets/style.css?v=1589234254");
@import url("webman/modules/SecurityScan/style.css?v=1589234282");
@import url("webman/modules/DiskMessageHandler/style.css?v=1589234254");
@import url("webman/modules/Utils/style.css?v=1589234253");
@import url("webman/modules/iSCSI/style.css?v=1589234015");
@import url("webman/modules/AudioPlayer/style.css?v=1589234253");
@import url("webman/modules/Share/style.css?v=1589234254");
@import url("webman/modules/BandwidthControl/style.css?v=1589234015");
@import url("webman/modules/LogCenter/style.css?v=1589234234");
@import url("webman/modules/DataDrivenDocuments/style.css?v=1589234254");
@import url("webman/modules/ThumbConvertProgress/style.css?v=1589234282");
@import url("webman/modules/HelpBrowser/style.css?v=1589234254");
@import url("webman/modules/PkgManApp/style.css?v=1589234254");
@import url("webman/modules/TaskSchedulerWidget/style.css?v=1589234254");
@import url("webman/modules/ExternalDevices/style.css?v=1589234254");
@import url("webman/modules/FileBrowser/style.css?v=1607915602");
@import url("webman/modules/PersonalSettings/style.css?v=1589234253");
@import url("webman/modules/WelcomeApp/style.css?v=1589234254");
@import url("webman/modules/DSMNotify/style.css?v=1589234254");
@import url("webman/modules/SupportForm/style.css?v=1589234254");
</style>
<style type="text/css">
@import url("webman/modules/WelcomeTip/style.css?v=1589234255");
@import url("webman/modules/FileTaskMonitor/style.css?v=1607915594");
@import url("webman/modules/PhotoViewer/style.css?v=1589234254");
@import url("webman/modules/HotkeyManager/style.css?v=1589234253");
@import url("webman/modules/ResourceMonitor/style.css?v=1589234254");
@import url("webman/modules/AdminCenter/style.css?v=1589234294");
</style>
<link rel="stylesheet" type="text/css" href="webman/3rdparty/DNSServer/style.css?v=1605613369" />
<link rel="stylesheet" type="text/css" href="webman/3rdparty/HyperBackup/style.css?v=1602039586" />
<link rel="stylesheet" type="text/css" href="webman/3rdparty/SynoFinder/style.css?v=1582080661" />
<link rel="stylesheet" type="text/css" href="webman/3rdparty/OAuthService/style.css?v=1550043740" />

</head>
<body role="application">
<div id="sds-wallpaper"></div>
<!-- Don't contain any text node to avoid IE insertBefore bug -->
<div id="sds-login-dialog-form" style="position: absolute; top: -10000px; left: -10000px;"><form id="login-form" class="x-plain-body" method="POST" action="webman/login.cgi" target="login_iframe"><input type="text" class="x-form-text" id="login_username" name="username" maxlength="256" /><input type="password" class="x-form-text" id="login_passwd" name="passwd" maxlength="256" autocomplete="off" /><input class="x-form-text" type="text" id="login_otp" name="OTPcode" maxlength="8" autocomplete="off" /><input type="submit" id="login_submit" style="position: absolute; top: -10000px; left: -10000px;" tabindex="-1" /></form><iframe id="login_iframe" name="login_iframe" width="0" height="0" frameborder="0" style="display: none;"></iframe></div>
<div id="sds-apply-preview-form" style="position: absolute; top: -10000px; left: -10000px;"><form id="preview-form" class="x-plain-body" method="POST" action="webman/modules/ControlPanel/modules/dsm.cgi" target="preview_iframe"><input type="submit" id="preview_submit" style="position: absolute; top: -10000px; left: -10000px;" tabindex="-1" /></form><iframe id="preview_iframe" name="preview_iframe" width="0" height="0" frameborder="0" style="display: none;"></iframe></div>
<script type="text/javascript" src="webapi/entry.cgi?api=SYNO.Core.Desktop.Defs&version=1&method=getjs&v=1611051831"></script>
<script type="text/javascript" src="webapi/entry.cgi?api=SYNO.Core.Desktop.JSUIString&version=1&method=getjs&lang=enu&v=1611051768"></script>
<script type="text/javascript" src="webapi/entry.cgi?api=SYNO.Core.Desktop.UIString&version=1&method=getjs&lang=enu&v=1611051770"></script>
<script type="text/javascript" src="scripts/prototype-1.7.2/prototype.js?v=1589234004"></script>
<script type="text/javascript" src="scripts/ext-3/adapter/ext/ext-base.js?v=1589234004"></script>
<script type="text/javascript" src="scripts/ext-3/ext-all.js?v=1589234004"></script>
<script type="text/javascript" src="scripts/ext-3/ux/ux-all.js?v=1589234004"></script>
<script type="text/javascript" src="scripts/scrollbar/flexcroll.js?v=1589234004"></script>
<script type="text/javascript" src="synoSDSjslib/sds.js?v=1589234012"></script>
<script type="text/javascript" src="webman/desktop.js?v=1589234253"></script>
<script type="text/javascript" src="webapi/entry.cgi?api=SYNO.Core.Desktop.SessionData&version=1&method=getjs&SynoToken=&v=1589234245"></script>

<script type="text/javascript" src="webman/security.cgi"></script>
<div class="pre-load-x-window-br"></div>
</body>
<noscript><div class='syno-no-script'><div class='title align-center'>This page can't be displayed</div><div class='desc align-center'>Please allow your browser to run JavaScript.</div><div class='icon align-center'></div></div></noscript>
</html>

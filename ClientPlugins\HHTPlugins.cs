﻿using EasyWork.bll;
using EasyWork.Honor.Service.ClientPlugins;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.ClientPlugins
{
    public partial class HHTPlugins : Form
    {
        bool autoupdate = false;
        public delegate void entrust();//声明一个委托 

        const int buffer_size = 1024;
        StringBuilder buffer = new StringBuilder(buffer_size);
        Thread th;
        public HHTPlugins()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            GetModel();
            cb_UpdateUrl.SelectedIndex = 1;
            cb_Model.SelectedIndex = 0;
            cb_WMacModel.SelectedIndex = 0;
            cb_sysversion.SelectedIndex = 0;
            tb_PN.Focus();
            th = new Thread(Auto_Update);
            th.Start();

        }

        void GetModel()
        {
            string path = Application.StartupPath + "\\HHT\\AutoUpdate";
            DirectoryInfo TheFolder = new DirectoryInfo(path);
            foreach (DirectoryInfo NextFolder in TheFolder.GetDirectories())
            {
                cb_Model.Items.Add(NextFolder.Name);
                cb_WMacModel.Items.Add(NextFolder.Name);
            }

        }

        private void bt_AutoUpdate_Click(object sender, EventArgs e)
        {
            if (!autoupdate)
            {
                autoupdate = true;
                bt_AutoUpdate.Text = "停止自动复制";
            }
            else if (autoupdate)
            {
                autoupdate = false;
                bt_AutoUpdate.Text = "开始自动复制";
            }
        }


        void Auto_Update()
        {
            while (true)
            {
                if (autoupdate)
                {
                    if (!Directory.Exists(cb_UpdateUrl.Text))
                    {
                        Thread.Sleep(2000);
                        continue;
                    }
                    string localpath = Application.StartupPath + "\\HHT\\AutoUpdate\\"+cb_Model.Text.Trim();
                    if (chk_path.Checked)
                    {
                        localpath = "d:\\EasyWork\\HHT\\AutoUpdate\\"+cb_Model.Text.Trim();
                    }
                    if (!File.Exists(localpath))
                    {
                        Directory.CreateDirectory(localpath);
                    }

                    DirectoryInfo TheFolder = new DirectoryInfo(localpath);
                    FileInfo[] fis = TheFolder.GetFiles("*.bin", SearchOption.AllDirectories);
                    bool shop = false;
                    foreach (FileInfo NextFile in fis)
                    {
                        string aa = cb_UpdateUrl.Text + "\\" + NextFile.Name;
                        if (!File.Exists(cb_UpdateUrl.Text + "\\" + NextFile.Name))
                        {
                            shop = true;
                            File.Copy(NextFile.FullName, cb_UpdateUrl.Text + "\\" + NextFile.Name, true);
                        }
                    }

                    if (shop)
                    {
                        ShowForm();
                    }



                    //    string sourceurl1 = Application.StartupPath + "\\HHT\\AutoUpdate\\sound.bin";
                    //string sourceurl2 = Application.StartupPath + "\\HHT\\AutoUpdate\\upgrade.bin";

                    //string desturl1 = cb_UpdateUrl.Text + "\\sound.bin";
                    //string desturl2 = cb_UpdateUrl.Text + "\\upgrade.bin";

                    //if (!File.Exists(sourceurl1)|| !File.Exists(sourceurl2))
                    //{
                    //    autoupdate = false;
                    //    bt_AutoUpdate.Text = "开始自动复制";
                    //    MessageBox.Show("火火兔的更新文件不正确!");
                    //    break;
                    //}

                    //if (Directory.Exists(cb_UpdateUrl.Text))
                    //{
                    //    bool shop1 = false;
                    //    bool shop2 = false;
                    //    if (!File.Exists(desturl1))
                    //    {
                    //        File.Copy(sourceurl1,desturl1, true);
                    //        shop1 = true;
                    //    }
                    //    if (!File.Exists(desturl2))
                    //    {
                    //        File.Copy(sourceurl2, desturl2, true);
                    //        shop2 = true;
                    //    }
                    //    if (shop2&&shop1)
                    //    {
                    //        ShowForm();
                    //    }
                    //}
                }

                Thread.Sleep(1000);
            }
        }

        public void ShowForm()
        {
            new Thread(new ThreadStart(thread)).Start();//创建一个新的线程并启动
        }

        public void thread()
        {

            Thread.Sleep(10);//线程休眠2秒 
            BeginInvoke(new entrust(Show_Msg));
        }

        public void Show_Msg()
        {           
            ShowMsg.HHT msg = new ShowMsg.HHT();//将窗口Messages 实例化
            Point p = new Point(Screen.PrimaryScreen.WorkingArea.Width - msg.Width + 5, Screen.PrimaryScreen.WorkingArea.Height + 7);
            msg.PointToClient(p);
            msg.Location = p;
            msg.Show();
            for (int i = 0; i < msg.Height; i++)
            {
                msg.Location = new Point(p.X, p.Y - i);
                Thread.Sleep(1);//消息框弹出速度，数值越大越慢
            }
        }

        private void HHTPlugins_FormClosing(object sender, FormClosingEventArgs e)
        {
            //Application.ExitThread();
            // Environment.Exit(0);
            try
            {
                th.Abort();
                t.Abort();
            }
            catch 
            {

            }
            Close();          
        }

        Thread t;
        private void button1_Click(object sender, EventArgs e)
        {
            if (cb_WMacModel.Text=="D3")
            {
                t = new Thread(new ThreadStart(StartD3));
                t.Start();
            }
            else
            {
                t = new Thread(new ThreadStart(StartMAC));
                t.Start();
            }
        }

        void StartD3()
        {
            lb_show.Text = "";
            string PN = tb_PN.Text.Trim();
            if (PN.Length != 14)
            {
                tb_PN.Focus();
                lb_show.Text = "PN码只能为14位";
                return;
            }
            HHTService.Insert(PN, tb_MACUrl.Text.Trim(), "", cb_WMacModel.Text.Trim(), "", "包装");
            lb_show.Text = "保存成功!";
            tb_PN.Focus();
            tb_MACUrl.Text = "";
            tb_PN.Text = "";
            t.Abort();
        }

        void StartMAC()
        {
            lb_show.Text = "";
            string QRCode = tb_MACUrl.Text.Trim();
            string PN = tb_PN.Text.Trim();
            if (QRCode=="")
            {
                tb_MACUrl.Focus();
                lb_show.Text = "请扫描二维码";
                return;
            }
            if (PN.Length!=14)
            {
                tb_PN.Focus();
                lb_show.Text = "PN码只能为14位";
                return;
            }

            IntPtr maindHwnd = WinAPI.FindWindow(null, "DEVICE写入工具V3.4 [201904.29]");
            if (maindHwnd == IntPtr.Zero)
            {
                MessageBox.Show("DEVICE写入工具V3.4 未打开");
                return;
            }
            
            IntPtr button = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.BUTTON.app.0."+cb_sysversion.Text, null);
            IntPtr button1 = WinAPI.FindWindowEx(maindHwnd, button, "WindowsForms10.BUTTON.app.0."+cb_sysversion.Text, null);
            IntPtr button2 = WinAPI.FindWindowEx(maindHwnd, button1, "WindowsForms10.BUTTON.app.0."+cb_sysversion.Text, null);//扫描序号
            IntPtr button3 = WinAPI.FindWindowEx(maindHwnd, button2, "WindowsForms10.BUTTON.app.0."+cb_sysversion.Text, null);//开始停止按钮
            WinAPI.SendMessage(button3, WinAPI.WM_GETTEXT, buffer_size, buffer);
            if (buffer.ToString() != "停止测试")
            {
                MessageBox.Show("DEVICE写入工具V3.4 未开始自动测试"+buffer.ToString());
                return;
            }
            WinAPI.SendMessage(button2, WinAPI.WM_GETTEXT, buffer_size, buffer);
            if (buffer.Length < 7)
            {
                MessageBox.Show("未链接端口");
                return;
            }
            string str = buffer.ToString().Split(',')[0].ToString();
            if (str != "请扫描序列号" && str != "序列号解析错误")
            {
                MessageBox.Show("未链接端口");
                return;
            }

            IntPtr Hwnd = WinAPI.FindWindow(null, "BY验证工具201811.21");
            if (Hwnd == IntPtr.Zero)
            {
                MessageBox.Show("BY验证工具201811.21 工具未打开");
                return;
            }

            IntPtr text = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.RichEdit20W.app.0."+cb_sysversion.Text, null);

            WinAPI.SendMessage(text, WinAPI.WM_SETTEXT, (int)IntPtr.Zero, QRCode);
            WinAPI.SendMessage(text, WinAPI.WM_KEYDOWN, WinAPI.VK_RETURN, 0);//回车事件
            WinAPI.SendMessage(text, WinAPI.WM_KEYUP, WinAPI.VK_RETURN, 0);

            int k = 0;
            while (true)
            {
                WinAPI.SendMessage(button1, WinAPI.WM_GETTEXT, buffer_size, buffer);
                if (buffer.ToString() == "绑定成功")
                {
                    k++;
                    break;
                } 
                Thread.Sleep(200);
            }

            text = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.RichEdit20W.app.0."+cb_sysversion.Text, null);
            WinAPI.SendMessage(text, WinAPI.WM_GETTEXT, buffer_size, buffer);
            string[] st = buffer.ToString().Replace("\r\n", " ").Split(' ');
            string mac = st[0].Trim().ToUpper().Replace("MAC:", "");
            string deviceid = st[1].Trim().ToUpper().Replace("DEVICEID:", "");

            IntPtr Checktext = WinAPI.FindWindowEx(Hwnd, IntPtr.Zero, "WindowsForms10.RichEdit20W.app.0."+cb_sysversion.Text, null);
            IntPtr Checktext1 = WinAPI.FindWindowEx(Hwnd, Checktext, "WindowsForms10.RichEdit20W.app.0."+cb_sysversion.Text, null);
            WinAPI.SendMessage(Checktext1, WinAPI.WM_SETTEXT, (int)IntPtr.Zero, QRCode);
            WinAPI.SendMessage(Checktext1, WinAPI.WM_KEYDOWN, WinAPI.VK_RETURN, 0);//回车事件
            WinAPI.SendMessage(Checktext1, WinAPI.WM_KEYUP, WinAPI.VK_RETURN, 0);

            WinAPI.SendMessage(Checktext, WinAPI.WM_GETTEXT, buffer_size, buffer);
            if (buffer.ToString().ToUpper().IndexOf(mac) == -1)
            {
                MessageBox.Show("BY验证工具不通过");
                return;
            }
            if (PN==""||QRCode=="")
            {
                MessageBox.Show("PN或二维码不能为空");
                return;
            }
            if (HHTService.Exists_PN(PN))
            {
                HHTService.UpdateHHT_PN(PN, QRCode, deviceid, cb_WMacModel.Text.Trim(),mac, "包装");
            }
            else
            {

                HHTService.Insert(PN, QRCode, deviceid, cb_WMacModel.Text.Trim(), mac, "包装");
            }
            tb_PN.Focus();
          //  ShowForm();

            tb_MACUrl.Text = "";
            tb_PN.Text = "";
            lb_show.Text = "写号成功,共扫描"+k+"次";
            t.Abort();
           


        }

        private void tb_MACUrl_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                lb_show.Text = "";
                if (tb_PN.Text.Trim()=="")
                {
                    tb_PN.Focus();
                    return;
                }
                if (tb_MACUrl.Text.Trim() == "")
                {
                    tb_MACUrl.Focus();
                    return;
                }
                button1_Click(null,null);
                tb_PN.Focus();
            }
        }

        private void tb_PN_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                lb_show.Text = "";
                if (tb_PN.Text == "")
                {
                    tb_PN.Focus();
                    return;
                }
                if (tb_MACUrl.Text.Trim()==""&& cb_WMacModel.Text.Trim()!="D3")
                {
                    tb_MACUrl.Focus();
                    return;
                }
                button1_Click(null, null);
                tb_PN.Focus();
            }
        }

      
    }
}

<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Graph.Core</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Graph.Authentication.AzureIdentityAccessTokenProvider">
            <summary>
            An overload of the Access Token Provider that has the defaults for Microsoft Graph.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Authentication.AzureIdentityAccessTokenProvider.#ctor(Azure.Core.TokenCredential,System.String[],Microsoft.Kiota.Authentication.Azure.ObservabilityOptions,System.Boolean,System.String[])">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Graph.Authentication.AzureIdentityAccessTokenProvider.#ctor(Azure.Core.TokenCredential,System.String[],Microsoft.Kiota.Authentication.Azure.ObservabilityOptions,System.String[])">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider">
            <summary>
            An overload of the Azure Identity Authentication Provider that has the defaults for Microsoft Graph.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider.#ctor(Azure.Core.TokenCredential,System.String[],Microsoft.Kiota.Authentication.Azure.ObservabilityOptions,System.Boolean,System.String[])">
            <summary>
            The <see cref="T:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider"/> constructor
            </summary>
            <param name="credential">The credential implementation to use to obtain the access token.</param>
            <param name="allowedHosts">The list of allowed hosts for which to request access tokens.</param>
            <param name="scopes">The scopes to request the access token for.</param>
            <param name="observabilityOptions">The observability options to use for the authentication provider.</param>
            <param name="isCaeEnabled">Determines if the Continuous Access Evaluation (CAE) is enabled.</param>
        </member>
        <member name="M:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider.#ctor(Azure.Core.TokenCredential,System.String[],Microsoft.Kiota.Authentication.Azure.ObservabilityOptions,System.String[])">
            <summary>
            The <see cref="T:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider"/> constructor
            </summary>
            <param name="credential">The credential implementation to use to obtain the access token.</param>
            <param name="allowedHosts">The list of allowed hosts for which to request access tokens.</param>
            <param name="scopes">The scopes to request the access token for.</param>
            <param name="observabilityOptions">The observability options to use for the authentication provider.</param>
        </member>
        <member name="T:Microsoft.Graph.CoreConstants">
            <summary>
            Constants for the Graph Core library.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.PollingIntervalInMs">
            <summary>
            Polling interval for task completion.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.Headers">
            <summary>
            Header constants.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.Bearer">
            Authorization bearer.
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.SdkVersionHeaderName">
            SDK Version header
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.SdkVersionHeaderValueFormatString">
            SDK Version header
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.FormUrlEncodedContentType">
            Content-Type header
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.ThrowSiteHeaderName">
            Throw-site header
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.ClientRequestId">
            Client Request Id
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Headers.FeatureFlag">
            Feature Flag
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.MimeTypeNames">
            <summary>
            MimeType constants.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.MimeTypeNames.Application">
            <summary>
            MimeTypeNames.Application constants.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.MimeTypeNames.Application.Json">
            JSON content type value
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.MimeTypeNames.Application.Stream">
            Stream content type value
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.Serialization">
            <summary>
            Serialization constants.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Serialization.ODataType">
            OData type
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Serialization.ODataNextLink">
            OData next link
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.BatchRequest">
            <summary>
            Batch request constants.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.BatchRequest.MaxNumberOfRequests">
            <summary>
            Maximum number of individual requests.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.Encoding">
            <summary>
            Encoding constants
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.Encoding.GZip">
            gzip encoding.
        </member>
        <member name="T:Microsoft.Graph.CoreConstants.OdataInstanceAnnotations">
            <summary>
            Constants used to specify OData instance annotations.
            https://www.odata.org/vocabularies/
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.OdataInstanceAnnotations.NextLink">
            <summary>
            The nextLink annotations string.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.CoreConstants.OdataInstanceAnnotations.DeltaLink">
            <summary>
            The deltaLink annotations string.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.ClientException">
            <summary>
            Graph client exception.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ClientException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new client exception.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The possible innerException.</param>
        </member>
        <member name="T:Microsoft.Graph.ServiceException">
            <summary>
            Graph service exception.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new service exception.
            </summary>
            <param name="message">The error message.</param>
            <param name="innerException">The possible innerException.</param>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.#ctor(System.String,System.Net.Http.Headers.HttpResponseHeaders,System.Int32,System.Exception)">
            <summary>
            Creates a new service exception.
            </summary>
            <param name="message">The error message.</param>
            <param name="innerException">The possible innerException.</param>
            <param name="responseHeaders">The HTTP response headers from the response.</param>
            <param name="statusCode">The HTTP status code from the response.</param>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.#ctor(System.String,System.Net.Http.Headers.HttpResponseHeaders,System.Int32,System.String,System.Exception)">
            <summary>
            Creates a new service exception.
            </summary>
            <param name="message">The error message.</param>
            <param name="innerException">The possible innerException.</param>
            <param name="responseHeaders">The HTTP response headers from the response.</param>
            <param name="statusCode">The HTTP status code from the response.</param>
            <param name="rawResponseBody">The raw JSON response body.</param>
        </member>
        <member name="P:Microsoft.Graph.ServiceException.ResponseHeaders">
            <summary>
            The HTTP response headers from the response.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.ServiceException.RawResponseBody">
            <summary>
            Provide the raw JSON response body.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.ServiceException.AdditionalData">
            <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.IsMatch(System.String)">
            <summary>
            Checks if a given error code has been returned in the response at any level in the error stack.
            </summary>
            <param name="errorCode">The error code.</param>
            <returns>True if the error code is in the stack.</returns>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Graph.ServiceException.GetFieldDeserializers">
            <summary>
            The deserialization information for the current model
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ServiceException.Serialize(Microsoft.Kiota.Abstractions.Serialization.ISerializationWriter)">
            <summary>
            Serializes information the current object
            <param name="writer">Serialization writer to use to serialize this model</param>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.HttpClientExtensions.SetFeatureFlag(System.Net.Http.HttpClient,Microsoft.Graph.FeatureFlag)">
            <summary>
            Adds featureflag to existing header values.
            </summary>
            <param name="httpClient">The http client to set FeatureUsage header.</param>
            <param name="featureFlag">The Feature usage flag to set.</param>
        </member>
        <member name="M:Microsoft.Graph.HttpClientExtensions.ContainsFeatureFlag(System.Net.Http.HttpClient,Microsoft.Graph.FeatureFlag)">
            <summary>
            Checks if a featureflag existing in the default header values.
            </summary>
            <param name="httpClient">The http client to set FeatureUsage header.</param>
            <param name="featureFlag">The Feature usage flag to check for.</param>
        </member>
        <member name="T:Microsoft.Graph.HttpRequestMessageExtensions">
            <summary>
            Contains extension methods for <see cref="T:System.Net.Http.HttpRequestMessage"/>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.HttpRequestMessageExtensions.GetFeatureFlags(System.Net.Http.HttpRequestMessage)">
            <summary>
            Get's feature request header value from the incoming <see cref="T:System.Net.Http.HttpRequestMessage"/>
            </summary>
            <param name="httpRequestMessage">The <see cref="T:System.Net.Http.HttpRequestMessage"/> object</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.HttpRequestMessageExtensions.GetRequestContext(System.Net.Http.HttpRequestMessage)">
            <summary>
            Gets a <see cref="T:Microsoft.Graph.GraphRequestContext"/> from <see cref="T:System.Net.Http.HttpRequestMessage"/>
            </summary>
            <param name="httpRequestMessage">The <see cref="T:System.Net.Http.HttpRequestMessage"/> representation of the request.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.IDecryptableContentExtensions">
            <summary>
            Contains extension methods for <see cref="T:Microsoft.Graph.IDecryptableContentExtensions"/>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.IDecryptableContentExtensions.DecryptAsync``1(Microsoft.Graph.IDecryptableContent,System.Func{System.String,System.String,System.Threading.Tasks.Task{System.Security.Cryptography.X509Certificates.X509Certificate2}})">
            <summary>
            Validates the signature and decrypted content attached with the notification.
            </summary>
            <typeparam name="T">Type to deserialize the data to.</typeparam>
            <param name="encryptedContent">The encrypted content of type <see cref="T:Microsoft.Graph.IDecryptableContent"/></param>
            <param name="certificateProvider">Certificate provider to decrypt the content.
            The first parameter is the certificate ID provided when creating the subscription.
            The second is the certificate thumbprint. The certificate WILL be disposed at the end of decryption.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="certificateProvider"/> is null</exception>
            <returns>Decrypted content as the provided type.</returns>
        </member>
        <member name="M:Microsoft.Graph.IDecryptableContentExtensions.DecryptAsync(Microsoft.Graph.IDecryptableContent,System.Func{System.String,System.String,System.Threading.Tasks.Task{System.Security.Cryptography.X509Certificates.X509Certificate2}})">
            <summary>
            Validates the signature and decrypted content attached with the notification.
            https://docs.microsoft.com/en-us/graph/webhooks-with-resource-data#decrypting-resource-data-from-change-notifications
            </summary>
            <param name="encryptedContent">The encrypted content of type <see cref="T:Microsoft.Graph.IDecryptableContent"/></param>
            <param name="certificateProvider">Certificate provider to decrypt the content.
            The first parameter is the certificate ID provided when creating the subscription.
            The second is the certificate thumbprint. The certificate WILL be disposed at the end of decryption.</param>
            <exception cref="T:System.IO.InvalidDataException">Thrown when the <see cref="P:Microsoft.Graph.IDecryptableContent.DataSignature"/> value does not match the signature in the payload</exception>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="certificateProvider"/> is null</exception>
            <exception cref="T:System.ApplicationException">Thrown when there is a failure in attempting to decrypt the information</exception>
            <returns>Decrypted content as string.</returns>
        </member>
        <member name="T:Microsoft.Graph.IEncryptableSubscriptionExtensions">
            <summary>
            Contains extension methods for <see cref="T:Microsoft.Graph.IEncryptableSubscription"/>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.IEncryptableSubscriptionExtensions.AddPublicEncryptionCertificate(Microsoft.Graph.IEncryptableSubscription,System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Adds the public encryption certificate information for change notifications with resource data to the subscription creation information.
            </summary>
            <param name="subscription">The subscription instance of type <see cref="T:Microsoft.Graph.IEncryptableSubscription"/></param>
            <param name="certificate">Certificate to use for encryption</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="certificate"/> is null</exception>
        </member>
        <member name="T:Microsoft.Graph.ParseNodeExtensions">
            <summary>
            Extension helpers for the <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParseNode"/>
            </summary>
        </member>
        <member name="T:Microsoft.Graph.ITokenValidableExtension">
            <summary>
            Contains extension methods for <see cref="T:Microsoft.Graph.ITokenValidableExtension"/>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ITokenValidableExtension.AreTokensValid``2(Microsoft.Graph.ITokenValidable{``0,``1},System.Collections.Generic.IEnumerable{System.Guid},System.Collections.Generic.IEnumerable{System.Guid},System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Validates tokens attached with the notification collection. If the result is false, the notification collection should be discarded.
            </summary>
            <param name="collection">Collection instance of <see cref="T:Microsoft.Graph.ITokenValidable`2"/></param>
            <param name="tenantIds">List of tenant ids that notifications might be originating from.</param>
            <param name="appIds">List of application id (client ids) that subscriptions have been created from.</param>
            <param name="wellKnownUri">Well known URL to get the signing certificates for the tokens.
            If you are not using the public cloud you need to pass the value corresponding to your national deployment.</param>
            <param name="issuerFormats">Issuer formats for the "aud" claim in the tokens.
            If you are not using the public cloud you need to pass the value corresponding to your national deployment.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="tenantIds"/> or <paramref name="appIds"/> is null or empty</exception>
            <returns>Are tokens valid or not.</returns>
        </member>
        <member name="M:Microsoft.Graph.ITokenValidableExtension.AreTokensValidAsync``2(Microsoft.Graph.ITokenValidable{``0,``1},System.Collections.Generic.IEnumerable{System.Guid},System.Collections.Generic.IEnumerable{System.Guid},System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Validates tokens attached with the notification collection. If the result is false, the notification collection should be discarded.
            </summary>
            <param name="collection">Collection instance of <see cref="T:Microsoft.Graph.ITokenValidable`2"/></param>
            <param name="tenantIds">List of tenant ids that notifications might be originating from.</param>
            <param name="appIds">List of application id (client ids) that subscriptions have been created from.</param>
            <param name="wellKnownUri">Well known URL to get the signing certificates for the tokens.
            If you are not using the public cloud you need to pass the value corresponding to your national deployment.</param>
            <param name="issuerFormats">Issuer formats for the "aud" claim in the tokens.
            If you are not using the public cloud you need to pass the value corresponding to your national deployment.</param>
            <exception cref="T:System.ArgumentNullException">Thrown when <paramref name="tenantIds"/> or <paramref name="appIds"/> is null or empty</exception>
            <returns>Are tokens valid or not.</returns>
        </member>
        <member name="T:Microsoft.Graph.ExpressionExtractHelper">
            <summary>
            Helper class to extract $select or $expand parameters from strongly-typed expressions.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ExpressionExtractHelper.ExtractMembers``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}},System.String@)">
            <summary>
            Extract referenced members of the type T from the given expression as a list of strings
            </summary>
            <param name="expression">The expression to search</param>
            <param name="error">Message about what's wrong with the expression if return value is null</param>
            <returns>A comma-separated list of strings or null</returns>
        </member>
        <member name="T:Microsoft.Graph.ReadOnlySubStream">
            <summary>
            Helper stream class to represent a slice of a larger stream to save memory when dealing with large streams
            and remove the extra copy operations
            This class is inspired from System.IO.Compression in dot net core. Reference implementation can be found here
            https://github.com/dotnet/corefx/blob/d59f6e5a1bdabdd05317fd727efb59345e328b80/src/System.IO.Compression/src/System/IO/Compression/ZipCustomStreams.cs#L147
            </summary>
        </member>
        <member name="T:Microsoft.Graph.StringHelper">
            <summary>
            Helper class for string casing.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.StringHelper.ConvertTypeToTitleCase(System.String)">
            <summary>
            Converts the type string to title case.
            </summary>
            <param name="typeString">The type string.</param>
            <returns>The converted string.</returns>
        </member>
        <member name="M:Microsoft.Graph.StringHelper.ConvertTypeToLowerCamelCase(System.String)">
            <summary>
            Converts the type string to lower camel case.
            </summary>
            <param name="typeString">The type string.</param>
            <returns>The converted string.</returns>
        </member>
        <member name="M:Microsoft.Graph.StringHelper.ConvertIdentifierToLowerCamelCase(System.String)">
            <summary>
            Converts the identifier string to lower camel case.
            </summary>
            <param name="identifierString">The identifier string.</param>
            <returns>The converted string.</returns>
        </member>
        <member name="T:Microsoft.Graph.UrlHelper">
            <summary>
            Helper class for working with URLs.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.UrlHelper.GetQueryOptions(System.Uri)">
            <summary>
            Parse query options from the URL.
            </summary>
            <param name="resultUri"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.HttpMethods">
            <summary>
            Enum used specify Http methods
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.GET">
            <summary>
            The GET method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.POST">
            <summary>
            The POST method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.PATCH">
            <summary>
            The PATCH method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.PUT">
            <summary>
            The PUT method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.DELETE">
            <summary>
            The DELETE method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.HEAD">
            <summary>
            The HEAD method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.CONNECT">
            <summary>
            The CONNECT method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.OPTIONS">
            <summary>
            The OPTIONS method.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.HttpMethods.TRACE">
            <summary>
            The TRACE method.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.AsyncOperationStatus">
            <summary>
            The type AsyncOperationStatus.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.AsyncOperationStatus.Operation">
            <summary>
            Gets or sets operation.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.AsyncOperationStatus.PercentageComplete">
            <summary>
            Gets or sets percentageComplete.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.AsyncOperationStatus.Status">
            <summary>
            Gets or sets status.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.AsyncOperationStatus.AdditionalData">
            <summary>
            Gets or sets additional data.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.AsyncOperationStatus.GetFieldDeserializers">
            <summary>
            Gets the field deserializers for the <see cref="T:Microsoft.Graph.AsyncOperationStatus"/> instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.AsyncOperationStatus.Serialize(Microsoft.Kiota.Abstractions.Serialization.ISerializationWriter)">
            <summary>
            Serialize the <see cref="T:Microsoft.Graph.AsyncOperationStatus"/> instance
            </summary>
            <param name="writer">The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.ISerializationWriter"/> to serialize the instance</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the writer is null</exception>
        </member>
        <member name="T:Microsoft.Graph.BatchRequestStep">
            <summary>
            A single batch request step.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestStep.RequestId">
            <summary>
            A unique batch request id property.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestStep.Request">
            <summary>
            A http request message for an individual batch request operation.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestStep.DependsOn">
            <summary>
            An OPTIONAL array of batch request ids specifying the order of execution for individual batch requests.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestStep.#ctor(System.String,System.Net.Http.HttpRequestMessage,System.Collections.Generic.List{System.String})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestStep"/>.
            </summary>
            <param name="requestId">A unique batch request id.</param>
            <param name="httpRequestMessage">A http request message for an individual batch request operation.</param>
            <param name="dependsOn">An OPTIONAL array of batch request ids specifying the order of execution for individual batch requests.</param>
        </member>
        <member name="T:Microsoft.Graph.IDecryptableContent">
            <summary>
            The IDecryptableContent interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IDecryptableContent.Data">
            <summary>
            The Data string
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IDecryptableContent.DataKey">
            <summary>
            The DataKey string
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IDecryptableContent.DataSignature">
            <summary>
            The DataSignature string
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IDecryptableContent.EncryptionCertificateId">
            <summary>
            The EncryptionCertificateId string
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IDecryptableContent.EncryptionCertificateThumbprint">
            <summary>
            The EncryptionCertificateThumbprint string
            </summary>
        </member>
        <member name="T:Microsoft.Graph.IEncryptableSubscription">
            <summary>
            The IEncryptableSubscription interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IEncryptableSubscription.EncryptionCertificate">
            <summary>
            The encryption certificate
            </summary>
        </member>
        <member name="T:Microsoft.Graph.IEncryptedContentBearer`1">
            <summary>
            The IEncryptedContentBearer interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IEncryptedContentBearer`1.EncryptedContent">
            <summary>
            The encrypted content
            </summary>
        </member>
        <member name="T:Microsoft.Graph.ITokenValidable`2">
            <summary>
            The ITokenValidable interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.ITokenValidable`2.ValidationTokens">
            <summary>
            The collection of validation tokens
            </summary>
        </member>
        <member name="P:Microsoft.Graph.ITokenValidable`2.Value">
            <summary>
            The collection of encrypted token bearers
            </summary>
        </member>
        <member name="T:Microsoft.Graph.IUploadSession">
            <summary>
            The IUploadSession interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IUploadSession.ExpirationDateTime">
            <summary>
            Expiration date of the upload session
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IUploadSession.NextExpectedRanges">
            <summary>
            The ranges yet to be uploaded to the server
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IUploadSession.UploadUrl">
            <summary>
            The URL for upload
            </summary>
        </member>
        <member name="T:Microsoft.Graph.ReferenceRequestBody">
            <summary>
            The reference request body.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.ReferenceRequestBody.ODataId">
            <summary>
            The OData.id value.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.UploadResult`1">
            <summary>
            Result that we get from uploading a slice
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="F:Microsoft.Graph.UploadResult`1.UploadSession">
            <summary>
            The UploadSession containing information about the created upload session.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.UploadResult`1.ItemResponse">
            <summary>
            The uploaded item, once upload has completed.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.UploadResult`1.Location">
            <summary>
            The uploaded item location, once upload has completed.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.UploadResult`1.UploadSucceeded">
            <summary>
            Status of the request.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.Core.Models.UploadSession">
            <summary>
            Concrete implementation of the IUploadSession interface
            </summary>
        </member>
        <member name="P:Microsoft.Graph.Core.Models.UploadSession.ExpirationDateTime">
            <summary>
            Expiration date of the upload session
            </summary>
        </member>
        <member name="P:Microsoft.Graph.Core.Models.UploadSession.NextExpectedRanges">
            <summary>
            The ranges yet to be uploaded to the server
            </summary>
        </member>
        <member name="P:Microsoft.Graph.Core.Models.UploadSession.UploadUrl">
            <summary>
            The URL for upload
            </summary>
        </member>
        <member name="P:Microsoft.Graph.Core.Models.UploadSession.AdditionalData">
            <summary>
            Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Core.Models.UploadSession.GetFieldDeserializers">
            <summary>
            The deserialization information for the current model
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Core.Models.UploadSession.Serialize(Microsoft.Kiota.Abstractions.Serialization.ISerializationWriter)">
            <summary>
            Serializes information the current object
            <param name="writer">Serialization writer to use to serialize this model</param>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Core.Models.UploadSession.CreateFromDiscriminatorValue(Microsoft.Kiota.Abstractions.Serialization.IParseNode)">
            <summary>
            Creates a new instance of the appropriate class based on discriminator value
            <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
            </summary>
        </member>
        <member name="T:Microsoft.Graph.Core.Requests.BatchRequestBuilder">
            <summary>
            The type BatchRequestBuilder
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Core.Requests.BatchRequestBuilder.#ctor(Microsoft.Kiota.Abstractions.IRequestAdapter)">
            <summary>
            Constructs a new BatchRequestBuilder.
            </summary>
            <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        </member>
        <member name="P:Microsoft.Graph.Core.Requests.BatchRequestBuilder.UrlTemplate">
            <summary>
            Url template to use to build the URL for the current request builder
            </summary>
        </member>
        <member name="P:Microsoft.Graph.Core.Requests.BatchRequestBuilder.RequestAdapter">
            <summary>
            The request adapter to use to execute the requests.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.Core.Requests.BatchRequestBuilder.PostAsync(Microsoft.Graph.BatchRequestContent,System.Threading.CancellationToken,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Sends out the <see cref="T:Microsoft.Graph.BatchRequestContent"/> using the POST method
            </summary>
            <param name="batchRequestContent">The <see cref="T:Microsoft.Graph.BatchRequestContent"/> for the request</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <param name="errorMappings">The error mappings to use for handling error responses</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.Core.Requests.BatchRequestBuilder.PostAsync(Microsoft.Graph.BatchRequestContentCollection,System.Threading.CancellationToken,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Sends out the <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/> using the POST method
            </summary>
            <param name="batchRequestContentCollection">The <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/> for the request</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <param name="errorMappings">The error mappings to use for handling error responses</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.Core.Requests.BatchRequestBuilder.ToPostRequestInformationAsync(Microsoft.Graph.BatchRequestContent,System.Threading.CancellationToken)">
            <summary>
            Create <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance to post to batch endpoint
            <param name="batchRequestContent">The <see cref="T:Microsoft.Graph.BatchRequestContent"/> for the request</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            </summary>
        </member>
        <member name="T:Microsoft.Graph.AsyncMonitor`1">
            <summary>
            Monitor for async operations to the Graph service on the client.
            </summary>
            <typeparam name="T">The object type to return.</typeparam>
        </member>
        <member name="M:Microsoft.Graph.AsyncMonitor`1.#ctor(Microsoft.Graph.IBaseClient,System.String,Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory)">
            <summary>
            Construct an Async Monitor.
            </summary>
            <param name="client">The client to monitor.</param>
            <param name="monitorUrl">The URL to monitor.</param>
            <param name="parseNodeFactory"> The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory"/> to use for response handling</param>
        </member>
        <member name="M:Microsoft.Graph.AsyncMonitor`1.PollForOperationCompletionAsync(System.IProgress{Microsoft.Graph.AsyncOperationStatus},System.Threading.CancellationToken)">
            <summary>
            Poll to check for completion of an async call to the Graph service.
            </summary>
            <param name="progress">The progress status.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The operation task.</returns>
        </member>
        <member name="T:Microsoft.Graph.BaseGraphRequestAdapter">
            <summary>
            The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> instance for use with microsoft graph
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BaseGraphRequestAdapter.#ctor(Microsoft.Kiota.Abstractions.Authentication.IAuthenticationProvider,Microsoft.Graph.GraphClientOptions,Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory,Microsoft.Kiota.Abstractions.Serialization.ISerializationWriterFactory,System.Net.Http.HttpClient)">
            <summary>
            The public constructor for <see cref="T:Microsoft.Graph.BaseGraphRequestAdapter"/>
            </summary>
            <param name="authenticationProvider">The authentication provider.</param>
            <param name="graphClientOptions">The options for the graph client</param>
            <param name="parseNodeFactory">The parse node factory.</param>
            <param name="serializationWriterFactory">The serialization writer factory.</param>
            <param name="httpClient">The native HTTP client.</param>
        </member>
        <member name="T:Microsoft.Graph.BatchRequestContent">
            <summary>
            A <see cref="T:System.Net.Http.HttpContent"/> implementation to handle json batch requests.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContent.BatchRequestSteps">
            <summary>
            A BatchRequestSteps property.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContent.RequestAdapter">
            <summary>
            The request adapter for sending the batch request
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.#ctor(Microsoft.Graph.IBaseClient)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestContent"/>.
            </summary>
            <param name="baseClient">The <see cref="T:Microsoft.Graph.IBaseClient"/> for making requests</param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.#ctor(Microsoft.Graph.IBaseClient,Microsoft.Graph.BatchRequestStep[])">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestContent"/>.
            </summary>
            <param name="baseClient">The <see cref="T:Microsoft.Graph.IBaseClient"/> for making requests</param>
            <param name="batchRequestSteps">A list of <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add to the batch request content.</param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.#ctor(Microsoft.Kiota.Abstractions.IRequestAdapter,Microsoft.Graph.BatchRequestStep[])">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestContent"/>.
            </summary>
            <param name="requestAdapter">The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> for making requests</param>
            <param name="batchRequestSteps">A list of <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add to the batch request content.</param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.AddBatchRequestStep(Microsoft.Graph.BatchRequestStep)">
            <summary>
            Adds a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to batch request content if doesn't exists.
            </summary>
            <param name="batchRequestStep">A <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <returns>True or false based on addition or not addition of the provided <see cref="T:Microsoft.Graph.BatchRequestStep"/>. </returns>
            <exception cref="T:System.ArgumentException"> When the the request step contains a depends on to a request id that is not present.</exception>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.AddBatchRequestStep(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds a <see cref="T:System.Net.Http.HttpRequestMessage"/> to batch request content.
            </summary>
            <param name="httpRequestMessage">A <see cref="T:System.Net.Http.HttpRequestMessage"/> to use to build a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <returns>The requestId of the newly created <see cref="T:Microsoft.Graph.BatchRequestStep"/></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.AddBatchRequestStepAsync(Microsoft.Kiota.Abstractions.RequestInformation,System.String)">
            <summary>
            Adds a <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> to batch request content
            </summary>
            <param name="requestInformation">A <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> to use to build a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <param name="requestId">An optional string that will be used as the requestId of the batch request</param>
            <returns>The requestId of the  newly created <see cref="T:Microsoft.Graph.BatchRequestStep"/></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.RemoveBatchRequestStepWithId(System.String)">
            <summary>
            Removes a <see cref="T:Microsoft.Graph.BatchRequestStep"/> from batch request content for the specified id.
            </summary>
            <param name="requestId">A unique batch request id to remove.</param>
            <returns>True or false based on removal or not removal of a <see cref="T:Microsoft.Graph.BatchRequestStep"/>.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.NewBatchWithFailedRequests(System.Collections.Generic.Dictionary{System.String,System.Net.HttpStatusCode})">
            <summary>
            Creates a new <see cref="T:Microsoft.Graph.BatchRequestContent"/> with all <see cref="T:Microsoft.Graph.BatchRequestStep"/> that failed.
            </summary>
            <param name="responseStatusCodes">A dictionary with response codes, get with batchResponseContent.GetResponsesStatusCodesAsync()</param>
            <returns>new <see cref="T:Microsoft.Graph.BatchRequestContent"/> with all failed requests.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.GetBatchRequestContentAsync(System.Threading.CancellationToken)">
            <summary>
            Get the content of the batchRequest in the form of a stream.
            It is the responsibility of the caller to dispose of the stream returned.
            </summary>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <returns>A stream object with the contents of the batch request</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
            <summary>
            Serialize the HTTP content to a stream as an asynchronous operation.
            </summary>
            <param name="stream">The target stream.</param>
            <param name="context">Information about the transport (channel binding token, for example). This parameter may be null.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContent.TryComputeLength(System.Int64@)">
            <summary>
            Determines whether the HTTP content has a valid length in bytes.
            </summary>
            <param name="length">The length in bytes of the HTTP content.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.BatchRequestContentCollection">
            <summary>
            A collection of batch requests that are automatically managed.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.#ctor(Microsoft.Graph.IBaseClient,System.Int32)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/>.
            </summary>
            <param name="baseClient">The <see cref="T:Microsoft.Graph.IBaseClient"/> for making requests</param>
            <param name="batchRequestLimit">Number of requests that may be placed in a single batch</param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.#ctor(Microsoft.Kiota.Abstractions.IRequestAdapter,System.Int32)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/>.
            </summary>
            <param name="requestAdapter">The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> for making requests</param>
            <param name="batchRequestLimit">Number of requests that may be placed in a single batch</param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.AddBatchRequestStep(Microsoft.Graph.BatchRequestStep)">
            <summary>
            Adds a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to batch request content.
            </summary>
            <param name="batchRequestStep">A <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <returns>True or false based on addition or not addition of the provided <see cref="T:Microsoft.Graph.BatchRequestStep"/>. </returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.AddBatchRequestStep(System.Net.Http.HttpRequestMessage)">
            <summary>
            Adds a <see cref="T:System.Net.Http.HttpRequestMessage"/> to batch request content.
            </summary>
            <param name="httpRequestMessage">A <see cref="T:System.Net.Http.HttpRequestMessage"/> to use to build a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <returns>The requestId of the newly created <see cref="T:Microsoft.Graph.BatchRequestStep"/></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.AddBatchRequestStepAsync(Microsoft.Kiota.Abstractions.RequestInformation,System.String)">
            <summary>
            Adds a <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> to batch request content
            </summary>
            <param name="requestInformation">A <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> to use to build a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to add.</param>
            <param name="requestId">An optional string that will be used as the requestId of the batch request</param>
            <returns>The requestId of the  newly created <see cref="T:Microsoft.Graph.BatchRequestStep"/></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.RemoveBatchRequestStepWithId(System.String)">
            <summary>
            Removes a <see cref="T:Microsoft.Graph.BatchRequestStep"/> from batch request content for the specified id.
            </summary>
            <param name="requestId">A unique batch request id to remove.</param>
            <returns>True or false based on removal or not removal of a <see cref="T:Microsoft.Graph.BatchRequestStep"/>.</returns>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentCollection.BatchRequestSteps">
            <summary>
            A BatchRequestSteps property.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentCollection.NewBatchWithFailedRequests(System.Collections.Generic.Dictionary{System.String,System.Net.HttpStatusCode})">
            <summary>
            Creates a new <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/> with all <see cref="T:Microsoft.Graph.BatchRequestStep"/> that failed.
            </summary>
            <param name="responseStatusCodes">A dictionary with response codes, get by executing batchResponseContentCollection.GetResponsesStatusCodesAsync()</param>
            <returns>new <see cref="T:Microsoft.Graph.BatchRequestContentCollection"/> with all failed requests.</returns>
        </member>
        <member name="T:Microsoft.Graph.BatchRequestContentSteps">
            <summary>
            Represents a collection of ordered <see cref="T:Microsoft.Graph.BatchRequestStep"/> objects.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Graph.BatchRequestContentSteps"/> class which keeps track of the order of the steps.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentSteps.Item(System.String)">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Graph.BatchRequestStep"/> with the specified key.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentSteps.Keys">
            <summary>
            Gets the keys in the collection.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentSteps.Values">
            <summary>
             Gets the values in the collection.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentSteps.Count">
            <summary>
            Gets the number of elements in the collection.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.BatchRequestContentSteps.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Add(System.String,Microsoft.Graph.BatchRequestStep)">
            <summary>
            Adds a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to the collection.
            </summary>
            <param name="key"></param>
            <param name="value"></param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Add(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Graph.BatchRequestStep})">
            <summary>
            Adds a <see cref="T:Microsoft.Graph.BatchRequestStep"/> to the collection.
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Clear">
            <summary>
            Clears the collection.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Contains(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Graph.BatchRequestStep})">
            <summary>
            Determines whether the collection contains a specific value.
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.ContainsKey(System.String)">
            <summary>
            Determines whether the collection contains a specific key.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.CopyTo(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Graph.BatchRequestStep}[],System.Int32)">
            <summary>
            Copies the elements of the collection to an array, starting at a particular array index.
            </summary>
            <param name="array"></param>
            <param name="arrayIndex"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Remove(System.String)">
            <summary>
            Removes the <see cref="T:Microsoft.Graph.BatchRequestStep"/> with the specified key from the collection.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.Remove(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Graph.BatchRequestStep})">
            <summary>
            Removes the <see cref="T:Microsoft.Graph.BatchRequestStep"/> with the specified key from the collection.
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchRequestContentSteps.TryGetValue(System.String,Microsoft.Graph.BatchRequestStep@)">
            <summary>
            Tries to get the value associated with the specified key.
            </summary>
            <param name="key"></param>
            <param name="value"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Microsoft.Graph.BatchResponseContent">
            <summary>
            Handles batch request responses.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.#ctor(System.Net.Http.HttpResponseMessage,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.BatchResponseContent"/>
            </summary>
            <param name="httpResponseMessage">A <see cref="T:System.Net.Http.HttpResponseMessage"/> of a batch request execution.</param>
            <param name="errorMappings">A dictionary of error mappings to handle failed responses.</param>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponsesAsync">
            <summary>
            Gets all batch responses <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            All <see cref="T:System.Net.Http.HttpResponseMessage"/> in the dictionary MUST be disposed since they implement <see cref="T:System.IDisposable"/>.
            </summary>
            <returns>A Dictionary of id and <see cref="T:System.Net.Http.HttpResponseMessage"/> representing batch responses.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponsesStatusCodesAsync">
            <summary>
            Gets all batch responses statuscodes <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            </summary>
            <returns>A Dictionary of id and <see cref="T:System.Net.HttpStatusCode"/> representing batch responses.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponseByIdAsync(System.String)">
            <summary>
            Gets a batch response as <see cref="T:System.Net.Http.HttpResponseMessage"/> for the specified batch request id.
            The returned <see cref="T:System.Net.Http.HttpResponseMessage"/> MUST be disposed since it implements an <see cref="T:System.IDisposable"/>.
            </summary>
            <param name="requestId">A batch request id.</param>
            <returns>A <see cref="T:System.Net.Http.HttpResponseMessage"/> response object for a batch request.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponseByIdAsync``1(System.String,Microsoft.Kiota.Abstractions.IResponseHandler)">
            <summary>
            Gets a batch response as a requested type for the specified batch request id.
            </summary>
            <param name="requestId">A batch request id.</param>
            <param name="responseHandler">ResponseHandler to use for the response</param>
            <returns>A deserialized object of type T<see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponseStreamByIdAsync(System.String)">
            <summary>
            Gets a batch response content as a stream
            </summary>
            <param name="requestId">A batch request id.</param>
            <returns>The response stream of the batch response object</returns>
            <remarks> Stream should be dispose once done with.</remarks>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetNextLinkAsync">
            <summary>
            Gets the @NextLink of a batch response.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.IsSuccessStatusCode(System.Net.HttpStatusCode)">
            <summary>
            Checks is a <see cref="T:System.Net.HttpStatusCode"/> can be marked as successful
            </summary>
            <param name="statusCode">A single <see cref="T:System.Net.HttpStatusCode"/>.</param>
            <returns>Returns true if status code is between 200 and 300.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetResponseMessageFromJObject(System.Text.Json.JsonElement)">
            <summary>
            Gets a <see cref="T:System.Net.Http.HttpResponseMessage"/> from <see cref="T:System.Text.Json.JsonElement"/> representing a batch response item.
            </summary>
            <param name="jResponseItem">A single batch response item of type <see cref="T:System.Text.Json.JsonElement"/>.</param>
            <returns>A single batch response as a <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContent.GetBatchResponseContentAsync">
            <summary>
            Gets the <see cref="T:System.Net.Http.HttpContent"/> of a batch response as a <see cref="T:System.Text.Json.JsonDocument"/>.
            </summary>
            <returns>A batch response content as <see cref="T:System.Text.Json.JsonDocument"/>.</returns>
        </member>
        <member name="T:Microsoft.Graph.BatchResponseContentCollection">
            <summary>
            Handles batch request responses.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContentCollection.GetResponseByIdAsync(System.String)">
            <summary>
            Gets a batch response as <see cref="T:System.Net.Http.HttpResponseMessage"/> for the specified batch request id.
            The returned <see cref="T:System.Net.Http.HttpResponseMessage"/> MUST be disposed since it implements an <see cref="T:System.IDisposable"/>.
            </summary>
            <param name="requestId">A batch request id.</param>
            <returns>A <see cref="T:System.Net.Http.HttpResponseMessage"/> response object for a batch request.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContentCollection.GetResponseByIdAsync``1(System.String,Microsoft.Kiota.Abstractions.IResponseHandler)">
            <summary>
            Gets a batch response as a requested type for the specified batch request id.
            </summary>
            <param name="requestId">A batch request id.</param>
            <param name="responseHandler">ResponseHandler to use for the response</param>
            <returns>A deserialized object of type T<see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContentCollection.GetResponseStreamByIdAsync(System.String)">
            <summary>
            Gets a batch response content as a stream
            </summary>
            <param name="requestId">A batch request id.</param>
            <returns>The response stream of the batch response object</returns>
            <remarks> Stream should be dispose once done with.</remarks>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContentCollection.GetResponsesAsync">
            <summary>
            Gets all batch responses <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            All <see cref="T:System.Net.Http.HttpResponseMessage"/> in the dictionary MUST be disposed since they implement <see cref="T:System.IDisposable"/>.
            </summary>
            <returns>A Dictionary of id and <see cref="T:System.Net.Http.HttpResponseMessage"/> representing batch responses.</returns>
            <remarks>Not implemented, use GetResponsesStatusCodesAsync and fetch individual responses</remarks>
        </member>
        <member name="M:Microsoft.Graph.BatchResponseContentCollection.GetResponsesStatusCodesAsync">
            <summary>
            Gets all batch responses statuscodes <see cref="T:System.Collections.Generic.Dictionary`2"/>.
            </summary>
            <returns>A Dictionary of id and <see cref="T:System.Net.HttpStatusCode"/> representing batch responses.</returns>
        </member>
        <member name="T:Microsoft.Graph.DeltaResponseHandler`1">
            <summary>
            PREVIEW
            A response handler that exposes the list of changes returned in a response.
            This supports scenarios where the service expresses changes to 'null'. The
            deserializer can't express changes to null so you can now discover if a property
            has been set to null. This is intended for use with a Delta query scenario.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.#ctor(Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.DeltaResponseHandler`1"/>.
            </summary>
            <param name="parseNodeFactory"> The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory"/> to use for response handling</param>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.HandleResponseAsync``2(``0,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Process raw HTTP response into requested domain type.
            </summary>
            <typeparam name="NativeResponseType">The type of the response</typeparam>
            <typeparam name="ModelType">The type to return</typeparam>
            <param name="response">The HttpResponseMessage to handle</param>
            <param name="errorMappings">The errorMappings to use in the event of failed requests</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.GetResponseStringAsync(System.Net.Http.HttpResponseMessage)">
            <summary>
            Get the response content string
            </summary>
            <param name="hrm">The response object</param>
            <returns>The full response string to return</returns>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.GetResponseBodyWithChangelistAsync(System.String)">
            <summary>
            Gets the response with change lists set on each item.
            </summary>
            <param name="deltaResponseBody">The entire response body as a string.</param>
            <returns>A task with a string representation of the response body. The changes are set on each response item.</returns>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.DiscoverChangedPropertiesAsync(System.Text.Json.JsonElement)">
            <summary>
            Inspects the response item and captures the list of properties on a new property
            named 'changes'.
            </summary>
            <param name="responseItem">The item to inspect for properties.</param>
            <returns>The item with the 'changes' property set on it.</returns>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.GetObjectPropertiesAsync(System.Text.Json.JsonElement,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Gets all changes on the object.
            </summary>
            <param name="changedObject">The responseItem to inspect for changes.</param>
            <param name="changes">The list of properties returned in the response.</param>
            <param name="parentName">The parent object of this changed object.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.DeltaResponseHandler`1.AddOrReplacePropertyToObject``1(System.Text.Json.JsonElement,System.String,``0)">
            <summary>
            Adds a property with the given property name to the JsonElement object. This function is currently necessary as
            <see cref="T:System.Text.Json.JsonElement"/> is currently readonly.
            </summary>
            <param name="jsonElement">The Original JsonElement to add/replace a property</param>
            <param name="propertyName">The property name to use</param>
            <param name="newItem">The object to be added</param>
            <typeparam name="NewItemType">The type of the object to be added</typeparam>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.FeatureFlag">
            <summary>
            Feature Flags
            </summary>
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.None">
            None set
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.RedirectHandler">
            Redirect Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.RetryHandler">
            Retry Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.AuthHandler">
            Auth Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.DefaultHttpProvider">
            Default Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.LoggingHandler">
            Logging Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.ServiceDiscoveryHandler">
            Service Discovery Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.CompressionHandler">
            Compression Handler
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.ConnectionPoolManager">
            Connection Pool Manager
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.LongRunningOperationHandler">
            Long Running Operation Handler 
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.BatchRequestContext">
            Batch Request Content Used
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.PageIteratorTask">
            Page Iterator task Used
        </member>
        <member name="F:Microsoft.Graph.FeatureFlag.FileUploadTask">
            File Upload task Used
        </member>
        <member name="T:Microsoft.Graph.GraphClientFactory">
            <summary>
            GraphClientFactory class to create the HTTP client
            </summary>
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.defaultTimeout">
            The default value for the overall request timeout.
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.cloudList">
            Microsoft Graph service national cloud endpoints
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.Global_Cloud">
            Global endpoint
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.USGOV_Cloud">
            US_GOV endpoint
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.USGOV_DOD_Cloud">
            US_GOV endpoint
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.China_Cloud">
            China endpoint
        </member>
        <member name="F:Microsoft.Graph.GraphClientFactory.Germany_Cloud">
            Germany endpoint
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.Create(Microsoft.Graph.GraphClientOptions,System.String,System.String,System.Net.IWebProxy,System.Net.Http.HttpMessageHandler)">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured with the handlers provided.
            </summary>
            <param name="version">The graph version to use.</param>
            <param name="nationalCloud">The national cloud endpoint to use.</param>
            <param name="graphClientOptions">The <see cref="T:Microsoft.Graph.GraphClientOptions"/> to use with the client</param>
            <param name="proxy">The proxy to be used with created client.</param>
            <param name="finalHandler">The last HttpMessageHandler to HTTP calls.
            The default implementation creates a new instance of <see cref="T:System.Net.Http.HttpClientHandler"/> for each HttpClient.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.Create(System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler},System.String,System.String,System.Net.IWebProxy,System.Net.Http.HttpMessageHandler,System.Boolean)">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured with the handlers provided.
            </summary>
            <param name="version">The graph version to use.</param>
            <param name="nationalCloud">The national cloud endpoint to use.</param>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as an
            <see cref="T:System.Net.Http.HttpRequestMessage"/> travels from the <see cref="T:System.Net.Http.HttpClient"/> to the network and an
            <see cref="T:System.Net.Http.HttpResponseMessage"/> travels from the network back to <see cref="T:System.Net.Http.HttpClient"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for
            an outbound request message but last for an inbound response message.</param>
            <param name="proxy">The proxy to be used with created client.</param>
            <param name="finalHandler">The last HttpMessageHandler to HTTP calls.</param>
            <param name="disposeHandler">true if the inner handler should be disposed of by Dispose(), false if you intend to reuse the inner handler..</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> instance with the configured handlers.</returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.Create(Microsoft.Kiota.Abstractions.Authentication.BaseBearerTokenAuthenticationProvider,System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler},System.String,System.String,System.Net.IWebProxy,System.Net.Http.HttpMessageHandler,System.Boolean)">
            <summary>
               Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured to authenticate requests using the provided <see cref="T:Microsoft.Kiota.Abstractions.Authentication.BaseBearerTokenAuthenticationProvider"/>.
            </summary>
            <param name="authenticationProvider">The authentication provider to initialise the Authorization handler</param>
            <param name="handlers">Custom middleware pipeline to which the Authorization handler is appended. If null, default handlers are initialised</param>
            <param name="version">The Graph version to use in the base URL</param>
            <param name="nationalCloud">The national cloud endpoint to use</param>
            <param name="proxy">The proxy to be used with the created client</param>
            <param name="finalHandler">The last HttpMessageHandler to HTTP calls.</param>
            <param name="disposeHandler">true if the inner handler should be disposed of by Dispose(), false if you intend to reuse the inner handler..</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> instance with the configured handlers</returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.Create(Azure.Core.TokenCredential,System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler},System.String,System.String,System.Net.IWebProxy,System.Net.Http.HttpMessageHandler,System.Boolean)">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured to authenticate requests using the provided <see cref="T:Azure.Core.TokenCredential"/>.
            </summary>
            <param name="tokenCredential">Token credential object use to initialise an <see cref="T:Microsoft.Graph.Authentication.AzureIdentityAuthenticationProvider"/></param>
            <param name="handlers">Custom middleware pipeline to which the Authorization handler is appended. If null, default handlers are initialised</param>
            <param name="version">The Graph version to use in the base URL</param>
            <param name="nationalCloud">The national cloud endpoint to use</param>
            <param name="proxy">The proxy to be used with the created client</param>
            <param name="finalHandler">The last HttpMessageHandler to HTTP calls</param>
            <param name="disposeHandler">true if the inner handler should be disposed of by Dispose(), false if you intend to reuse the inner handler.</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> instance with the configured handlers</returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.CreateDefaultHandlers(Microsoft.Graph.GraphClientOptions)">
            <summary>
            Create a default set of middleware for calling Microsoft Graph
            </summary>
            <param name="graphClientOptions">The <see cref="T:Microsoft.Graph.GraphClientOptions"/> to use with the client</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.CreatePipeline(System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler},System.Net.Http.HttpMessageHandler)">
            <summary>
            Creates an instance of an <see cref="T:System.Net.Http.HttpMessageHandler"/> using the <see cref="T:System.Net.Http.DelegatingHandler"/> instances
            provided by <paramref name="handlers"/>. The resulting pipeline can be used to manually create <see cref="T:System.Net.Http.HttpClient"/>
            or <see cref="T:System.Net.Http.HttpMessageInvoker"/> instances with customized message handlers.
            </summary>
            <param name="finalHandler">The inner handler represents the destination of the HTTP message channel.</param>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as part
            of sending an <see cref="T:System.Net.Http.HttpRequestMessage"/> and receiving an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for
            an outbound request message but last for an inbound response message.</param>
            <returns>The HTTP message channel.</returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.CreatePipelineWithFeatureFlags(System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler},System.Net.Http.HttpMessageHandler)">
            <summary>
            Creates an instance of an <see cref="T:System.Net.Http.HttpMessageHandler"/> using the <see cref="T:System.Net.Http.DelegatingHandler"/> instances
            provided by <paramref name="handlers"/>. The resulting pipeline can be used to manually create <see cref="T:System.Net.Http.HttpClient"/>
            or <see cref="T:System.Net.Http.HttpMessageInvoker"/> instances with customized message handlers.
            </summary>
            <param name="finalHandler">The inner handler represents the destination of the HTTP message channel.</param>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as part
            of sending an <see cref="T:System.Net.Http.HttpRequestMessage"/> and receiving an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for
            an outbound request message but last for an inbound response message.</param>
            <returns>A tuple with The HTTP message channel and FeatureFlag for the handlers.</returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.GetNativePlatformHttpHandler(System.Net.IWebProxy)">
            <summary>
            Gets a platform's native http handler i.e. NSUrlSessionHandler for Xamarin.iOS and Xamarin.Mac, AndroidMessageHandler for Xamarin.Android and HttpClientHandler for others.
            </summary>
            <param name="proxy">The proxy to be used with created client.</param>
            <returns>
            1. NSUrlSessionHandler for Xamarin.iOS and Xamarin.Mac
            2. AndroidMessageHandler for Xamarin.Android.
            3. HttpClientHandler for other platforms.
            </returns>
        </member>
        <member name="M:Microsoft.Graph.GraphClientFactory.GetHandlerFeatureFlag(System.Net.Http.DelegatingHandler)">
            <summary>
            Gets feature flag for the specified handler.
            </summary>
            <param name="delegatingHandler">The <see cref="T:System.Net.Http.DelegatingHandler"/> to get its feaure flag.</param>
            <returns>Delegating handler feature flag.</returns>
        </member>
        <member name="T:Microsoft.Graph.GraphClientOptions">
            <summary>
            The options for setting up a given graph client
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphClientOptions.GraphServiceTargetVersion">
            <summary>
            The target version of the api endpoint we are targeting (v1 or beta)
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphClientOptions.GraphServiceLibraryClientVersion">
            <summary>
            The version of the service library in use. Should be in the format `x.x.x` (Semantic version)
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphClientOptions.GraphCoreClientVersion">
            <summary>
            The version of the core library in use. Should be in the format `x.x.x` (Semantic version).
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphClientOptions.GraphProductPrefix">
            <summary>
            The product prefix to use in setting the telemetry headers.
            Will default to `graph-dotnet` if not set.
            </summary>
        </member>
        <member name="T:Microsoft.Graph.GraphRequestContext">
            <summary>
            The graph request context class
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphRequestContext.ClientRequestId">
            <summary>
            A ClientRequestId property
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphRequestContext.CancellationToken">
            <summary>
            A CancellationToken property
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphRequestContext.FeatureUsage">
            <summary>
            A FeatureUsage property
            </summary>
        </member>
        <member name="T:Microsoft.Graph.GraphResponse">
            <summary>
            The GraphResponse Object
            </summary>
        </member>
        <member name="M:Microsoft.Graph.GraphResponse.#ctor(Microsoft.Kiota.Abstractions.RequestInformation,System.Net.Http.HttpResponseMessage)">
            <summary>
            The GraphResponse Constructor
            </summary>
            <param name="requestInformation">The Request made for the response</param>
            <param name="httpResponseMessage">The response</param>
        </member>
        <member name="P:Microsoft.Graph.GraphResponse.StatusCode">
            <summary>
            The Response Status code
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphResponse.Content">
            <summary>
            The Response Content
            </summary>
        </member>
        <member name="P:Microsoft.Graph.GraphResponse.HttpHeaders">
            <summary>
            The Response Headers
            </summary>
        </member>
        <member name="F:Microsoft.Graph.GraphResponse.RequestInformation">
            <summary>
            The reference to the original request
            </summary>
        </member>
        <member name="M:Microsoft.Graph.GraphResponse.ToHttpResponseMessage">
            <summary>
            Get the native Response Message
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.GraphResponse.Dispose">
            <summary>
            Cleanup
            </summary>
        </member>
        <member name="T:Microsoft.Graph.GraphResponse`1">
            <summary>
            The GraphResponse Object
            </summary>
        </member>
        <member name="M:Microsoft.Graph.GraphResponse`1.#ctor(Microsoft.Kiota.Abstractions.RequestInformation,System.Net.Http.HttpResponseMessage)">
            <summary>
            The GraphResponse Constructor
            </summary>
            <param name="requestInformation">The Request made for the response</param>
            <param name="httpResponseMessage">The response</param>
        </member>
        <member name="M:Microsoft.Graph.GraphResponse`1.GetResponseObjectAsync(Microsoft.Kiota.Abstractions.IResponseHandler,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Gets the deserialized object
            </summary>
            <param name="responseHandler">The response handler to use for the reponse</param>
            <param name="errorMappings">The errorMappings to use in the event of a non sucess request</param>
        </member>
        <member name="T:Microsoft.Graph.IAsyncMonitor`1">
            <summary>
            Monitor for async operations to the Graph service on the client.
            </summary>
            <typeparam name="T">The object type to return.</typeparam>
        </member>
        <member name="M:Microsoft.Graph.IAsyncMonitor`1.PollForOperationCompletionAsync(System.IProgress{Microsoft.Graph.AsyncOperationStatus},System.Threading.CancellationToken)">
            <summary>
            Poll to check for completion of an async call to the Graph service.
            </summary>
            <param name="progress">The progress status.</param>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The operation task.</returns>
        </member>
        <member name="T:Microsoft.Graph.IBaseClient">
            <summary>
            A default client interface.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IBaseClient.RequestAdapter">
            <summary>
            Gets the <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> for sending requests.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.IBaseClient.Batch">
            <summary>
            Gets the <see cref="T:Microsoft.Graph.Core.Requests.BatchRequestBuilder"/> for building batch Requests
            </summary>
        </member>
        <member name="T:Microsoft.Graph.GraphTelemetryHandler">
            <summary>
            A <see cref="T:System.Net.Http.DelegatingHandler"/> implementation that telemetry for graph.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.GraphTelemetryHandler.assemblyVersion">
            The version for current assembly.
        </member>
        <member name="F:Microsoft.Graph.GraphTelemetryHandler.SdkVersionHeaderValue">
            The value for the SDK version header.
        </member>
        <member name="M:Microsoft.Graph.GraphTelemetryHandler.#ctor(Microsoft.Graph.GraphClientOptions)">
            <summary>
            The <see cref="T:Microsoft.Graph.GraphClientOptions"/> constructor.
            </summary>
            <param name="graphClientOptions"></param>
        </member>
        <member name="M:Microsoft.Graph.GraphTelemetryHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
            <summary>
            Sends a HTTP request.
            </summary>
            <param name="httpRequest">The <see cref="T:System.Net.Http.HttpRequestMessage"/> to be sent.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> for the request.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.ResponseHandler`1">
            <summary>
            Provides method(s) to deserialize raw HTTP responses into strong types.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.ResponseHandler`1.#ctor(Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.ResponseHandler`1"/>.
            </summary>
            <param name="parseNodeFactory"> The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory"/> to use for response handling</param>
        </member>
        <member name="M:Microsoft.Graph.ResponseHandler`1.HandleResponseAsync``2(``0,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Process raw HTTP response into requested domain type.
            </summary>
            <typeparam name="NativeResponseType">The type of the response</typeparam>
            <typeparam name="ModelType">The type to return</typeparam>
            <param name="response">The HttpResponseMessage to handle</param>
            <param name="errorMappings">The errorMappings to use in the event of failed requests</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.ResponseHandler`1.ValidateSuccessfulResponseAsync(System.Net.Http.HttpResponseMessage,System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Validates the HttpResponse message is a successful response. Otherwise, throws a ServiceException with the error information
            present in the response body.
            </summary>
            <param name="httpResponseMessage">The <see cref="T:System.Net.Http.HttpResponseMessage"/> to validate</param>
            <param name="errorMapping">The errorMappings to use in the event of failed requests</param>
        </member>
        <member name="T:Microsoft.Graph.UploadResponseHandler">
            <summary>
            The ResponseHandler for upload requests
            </summary>
        </member>
        <member name="M:Microsoft.Graph.UploadResponseHandler.#ctor(Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Graph.UploadResponseHandler"/>.
            </summary>
            <param name="parseNodeFactory"> The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParseNodeFactory"/> to use for response handling</param>
        </member>
        <member name="M:Microsoft.Graph.UploadResponseHandler.HandleResponseAsync``1(System.Net.Http.HttpResponseMessage)">
            <summary>
            Process raw HTTP response from Upload request
            </summary>
            <typeparam name="T">The type to return</typeparam>
            <param name="response">The HttpResponseMessage to handle.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.UploadSessionRequestBuilder.#ctor(Microsoft.Graph.IUploadSession,Microsoft.Kiota.Abstractions.IRequestAdapter)">
            <summary>
            Create a new UploadSessionRequest
            </summary>
            <param name="uploadSession">The IUploadSession to use in the request.</param>
            <param name="requestAdapter">The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> for handling requests.</param>
        </member>
        <member name="M:Microsoft.Graph.UploadSessionRequestBuilder.DeleteAsync(System.Threading.CancellationToken)">
            <summary>
            Deletes the specified Session
            </summary>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <returns>The task to await.</returns>
        </member>
        <member name="M:Microsoft.Graph.UploadSessionRequestBuilder.ToDeleteRequestInformation">
            <summary>
            Creates <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance for a DELETE request
            </summary>
            <returns>The <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance.</returns>
        </member>
        <member name="M:Microsoft.Graph.UploadSessionRequestBuilder.GetAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the specified UploadSession.
            </summary>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <returns>The Item.</returns>
        </member>
        <member name="M:Microsoft.Graph.UploadSessionRequestBuilder.ToGetRequestInformation">
            <summary>
            Creates <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance for a GET request
            </summary>
            <returns>The <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance.</returns>
        </member>
        <member name="P:Microsoft.Graph.UploadSliceRequestBuilder`1.RangeBegin">
            <summary>
            The beginning of the slice range to send.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.UploadSliceRequestBuilder`1.RangeEnd">
            <summary>
            The end of the slice range to send.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.UploadSliceRequestBuilder`1.TotalSessionLength">
            <summary>
            The length in bytes of the session.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.UploadSliceRequestBuilder`1.RangeLength">
            <summary>
            The range length of the slice to send.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.UploadSliceRequestBuilder`1.#ctor(System.String,Microsoft.Kiota.Abstractions.IRequestAdapter,System.Int64,System.Int64,System.Int64)">
            <summary>
            Request for uploading one slice of a session
            </summary>
            <param name="sessionUrl">URL to upload the slice.</param>
            <param name="requestAdapter">Client used for sending the slice.</param>
            <param name="rangeBegin">Beginning of range of this slice</param>
            <param name="rangeEnd">End of range of this slice</param>
            <param name="totalSessionLength">Total session length. This MUST be consistent
            across all slice.</param>
        </member>
        <member name="M:Microsoft.Graph.UploadSliceRequestBuilder`1.PutAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Uploads the slice using PUT.
            </summary>
            <param name="stream">Stream of data to be sent in the request. Length must be equal to the length
            of this slice (as defined by this.RangeLength)</param>
            <param name="cancellationToken"><see cref="T:System.Threading.CancellationToken"/> to use for cancelling requests</param>
            <returns>The status of the upload. If UploadSession.AdditionalData.ContainsKey("successResponse")
            is true, then the item has completed, and the value is the created item from the server.</returns>
        </member>
        <member name="M:Microsoft.Graph.UploadSliceRequestBuilder`1.CreatePutRequestInformationAsync(System.IO.Stream)">
            <summary>
            Create <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance to upload the file slice
            <param name="stream">The <see cref="T:System.IO.Stream"/> to upload</param>
            </summary>
        </member>
        <member name="M:Microsoft.Graph.UploadSliceRequestBuilder`1.CreatePutRequestInformation(System.IO.Stream)">
            <summary>
            Create <see cref="T:Microsoft.Kiota.Abstractions.RequestInformation"/> instance to upload the file slice
            <param name="stream">The <see cref="T:System.IO.Stream"/> to upload</param>
            </summary>
        </member>
        <member name="T:Microsoft.Graph.LargeFileUploadTask`1">
            <summary>
            Task to help with resumable large file uploads.
            </summary>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.#ctor(Microsoft.Kiota.Abstractions.Serialization.IParsable,System.IO.Stream,System.Int32,Microsoft.Kiota.Abstractions.IRequestAdapter)">
            <summary>
            Task to help with resumable large file uploads. Generates slices based on <paramref name="uploadSession"/>
            information, and can control uploading of requests.
            </summary>
            <param name="uploadSession">Session information of type <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/>></param>
            <param name="uploadStream">Readable, seekable stream to be uploaded. Length of session is determined via uploadStream.Length</param>
            <param name="maxSliceSize">Max size(in bytes) of each slice to be uploaded. Defaults to 5MB. When uploading to OneDrive or SharePoint, this value needs to be a multiple of 320 KiB (327,680 bytes).
            If less than 0, default value of 5 MiB is used.</param>
            <param name="requestAdapter"><see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> to use for making upload requests. The client should not set Auth headers as upload urls do not need them.</param>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.#ctor(Microsoft.Graph.IUploadSession,System.IO.Stream,System.Int32,Microsoft.Kiota.Abstractions.IRequestAdapter)">
            <summary>
            Task to help with resumable large file uploads. Generates slices based on <paramref name="uploadSession"/>
            information, and can control uploading of requests.
            </summary>
            <param name="uploadSession">Session information of type <see cref="T:Microsoft.Graph.IUploadSession"/>></param>
            <param name="uploadStream">Readable, seekable stream to be uploaded. Length of session is determined via uploadStream.Length</param>
            <param name="maxSliceSize">Max size(in bytes) of each slice to be uploaded. Defaults to 5MB. When uploading to OneDrive or SharePoint, this value needs to be a multiple of 320 KiB (327,680 bytes).
            If less than 0, default value of 5 MiB is used.</param>
            <param name="requestAdapter"><see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> to use for making upload requests. The client should not set Auth headers as upload urls do not need them.</param>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.ExtractSessionFromParsable(Microsoft.Kiota.Abstractions.Serialization.IParsable)">
            <summary>
            Extract an <see cref="T:Microsoft.Graph.IUploadSession"/> from an <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/>.
            </summary>
            <param name="uploadSession"><see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/> to initialize an <see cref="T:Microsoft.Graph.IUploadSession"/> from</param>
            <returns>A <see cref="T:Microsoft.Graph.IUploadSession"/> instance</returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.InitializeAdapter(System.String)">
            <summary>
            Initialize a baseClient to use for the upload that does not have Auth enabled as the upload URLs explicitly do not need authentication.
            </summary>
            <param name="uploadUrl">Url to perform the upload to from the session</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.UploadSliceAsync(Microsoft.Graph.UploadSliceRequestBuilder{`0},System.Collections.Generic.ICollection{System.Exception},System.Threading.CancellationToken)">
            <summary>
            Write a slice of data using the UploadSliceRequest.
            </summary>
            <param name="uploadSliceRequestBuilder">The UploadSliceRequest to make the request with.</param>
            <param name="exceptionTrackingList">A list of exceptions to use to track progress. SlicedUpload may retry.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to use for requests</param>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.GetUploadSliceRequests">
            <summary>
            Get the series of requests needed to complete the upload session. Call <see cref="M:Microsoft.Graph.LargeFileUploadTask`1.UpdateSessionStatusAsync(System.Threading.CancellationToken)"/>
            first to update the internal session information.
            </summary>
            <returns>All requests currently needed to complete the upload session.</returns>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.UploadAsync(System.IProgress{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Upload the whole session.
            </summary>
            <param name="maxTries">Number of times to retry entire session before giving up.</param>
            <param name="progress">IProgress object to monitor the progress of the upload.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to use for requests</param>
            <returns>Item information returned by server.</returns>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.ResumeAsync(System.IProgress{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Get info about the upload session and resume from where it left off.
            </summary>
            <param name="maxTries">Number of times to retry entire session before giving up.</param>
            <param name="progress">IProgress object to monitor the progress of the upload.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to use for requests</param>
            <returns>Item information returned by server.</returns>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.UpdateSessionStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Get the status of the session. Stores returned session internally.
            Updates internal list of ranges remaining to be uploaded (according to the server).
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to use for requests</param>
            <returns><see cref="T:Microsoft.Graph.IUploadSession"/>> returned by the server.</returns>
        </member>
        <member name="M:Microsoft.Graph.LargeFileUploadTask`1.DeleteSessionAsync(System.Threading.CancellationToken)">
            <summary>
            Delete the session.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to use for requests</param>
            <returns>Once returned task is complete, the session has been deleted.</returns>
        </member>
        <member name="T:Microsoft.Graph.PageIterator`2">
            <summary>
            Use PageIterator&lt;TEntity&gt; to automatically page through result sets across multiple calls 
            and process each item in the result set.
            </summary>
            <typeparam name="TEntity">The Microsoft Graph entity type returned in the result set.</typeparam>
            <typeparam name="TCollectionPage">The Microsoft Graph collection response type returned in the collection response.</typeparam>
        </member>
        <member name="P:Microsoft.Graph.PageIterator`2.Deltalink">
            <summary>
            The @odata.deltaLink returned from a delta query.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.PageIterator`2.Nextlink">
            <summary>
            The @odata.nextLink returned in a paged result.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.PageIterator`2.State">
            <summary>
            The PageIterator state.
            </summary>
        </member>
        <member name="P:Microsoft.Graph.PageIterator`2.IsProcessPageItemCallbackAsync">
            <summary>
            Boolean value representing if the callback is Async
            </summary>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.CreatePageIterator(Microsoft.Graph.IBaseClient,`1,System.Func{`0,System.Boolean},System.Func{Microsoft.Kiota.Abstractions.RequestInformation,Microsoft.Kiota.Abstractions.RequestInformation},System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Creates the PageIterator with the results of an initial paged request. 
            </summary>
            <param name="client">The GraphServiceClient object used to execute the next request on paging </param>
            <param name="page">A generated implementation of ICollectionPage.</param>
            <param name="callback">A Func delegate that processes type TEntity in the result set and should return false if the iterator should cancel processing.</param>
            <param name="requestConfigurator">A Func delegate that configures the NextPageRequest</param>
            <param name="errorMapping">The error mappings to use in case of failed request during page iteration</param>
            <returns>A PageIterator&lt;TEntity&gt; that will process additional result pages based on the rules specified in Func&lt;TEntity,bool&gt; processPageItems</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.CreatePageIterator(Microsoft.Kiota.Abstractions.IRequestAdapter,`1,System.Func{`0,System.Boolean},System.Func{Microsoft.Kiota.Abstractions.RequestInformation,Microsoft.Kiota.Abstractions.RequestInformation},System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Creates the PageIterator with the results of an initial paged request. 
            </summary>
            <param name="requestAdapter">The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> object used to create the NextPageRequest for a delta query.</param>
            <param name="page">A generated implementation of ICollectionPage.</param>
            <param name="callback">A Func delegate that processes type TEntity in the result set and should return false if the iterator should cancel processing.</param>
            <param name="requestConfigurator">A Func delegate that configures the NextPageRequest</param>
            <param name="errorMapping">The error mappings to use in case of failed request during page iteration</param>
            <returns>A PageIterator&lt;TEntity&gt; that will process additional result pages based on the rules specified in Func&lt;TEntity,bool&gt; processPageItems</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.CreatePageIterator(Microsoft.Graph.IBaseClient,`1,System.Func{`0,System.Threading.Tasks.Task{System.Boolean}},System.Func{Microsoft.Kiota.Abstractions.RequestInformation,Microsoft.Kiota.Abstractions.RequestInformation},System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Creates the PageIterator with the results of an initial paged request. 
            </summary>
            <param name="client">The GraphServiceClient object used to create the NextPageRequest for a delta query.</param>
            <param name="page">A generated implementation of ICollectionPage.</param>
            <param name="asyncCallback">A Func delegate that processes type TEntity in the result set aynchrnously and should return false if the iterator should cancel processing.</param>
            <param name="requestConfigurator">A Func delegate that configures the NextPageRequest</param>
            <param name="errorMapping">The error mappings to use in case of failed request during page iteration</param>
            <returns>A PageIterator&lt;TEntity&gt; that will process additional result pages based on the rules specified in Func&lt;TEntity,bool&gt; processPageItems</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.CreatePageIterator(Microsoft.Kiota.Abstractions.IRequestAdapter,`1,System.Func{`0,System.Threading.Tasks.Task{System.Boolean}},System.Func{Microsoft.Kiota.Abstractions.RequestInformation,Microsoft.Kiota.Abstractions.RequestInformation},System.Collections.Generic.Dictionary{System.String,Microsoft.Kiota.Abstractions.Serialization.ParsableFactory{Microsoft.Kiota.Abstractions.Serialization.IParsable}})">
            <summary>
            Creates the PageIterator with the results of an initial paged request. 
            </summary>
            <param name="requestAdapter">The <see cref="T:Microsoft.Kiota.Abstractions.IRequestAdapter"/> object used to execute the next request on paging .</param>
            <param name="page">A generated implementation of ICollectionPage.</param>
            <param name="asyncCallback">A Func delegate that processes type TEntity in the result set aynchrnously and should return false if the iterator should cancel processing.</param>
            <param name="requestConfigurator">A Func delegate that configures the NextPageRequest</param>
            <param name="errorMapping">The error mappings to use in case of failed request during page iteration</param>
            <returns>A PageIterator&lt;TEntity&gt; that will process additional result pages based on the rules specified in Func&lt;TEntity,bool&gt; processPageItems</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.IntrapageIterateAsync">
            <summary>
            Iterate across the content of a single results page with the callback.
            </summary>
            <returns>A boolean value that indicates whether the callback cancelled 
            iterating across the page results or whether there are more pages to page. 
            A return value of false indicates that the iterator should stop iterating.</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.InterpageIterateAsync(System.Threading.CancellationToken)">
            <summary>
            Call the next page request when there is another page of data.
            </summary>
            <param name="token"></param>
            <returns>The task object that represents the results of this asynchronous operation.</returns>
            <exception cref="T:Microsoft.Graph.ServiceException">Thrown when the service encounters an error with
            a request.</exception>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.IterateAsync">
            <returns>The task object that represents the results of this asynchronous operation.</returns>
            <exception cref="T:Microsoft.Graph.ServiceException">Thrown when the service encounters an error with
            a request.</exception>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.IterateAsync(System.Threading.CancellationToken)">
            <param name="token">The CancellationToken used to stop iterating calls for more pages.</param>
            <returns>The task object that represents the results of this asynchronous operation.</returns>
            <exception cref="T:Microsoft.Graph.ServiceException">Thrown when the service encounters an error with
            a request or there is an internal error with the service.</exception>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.ResumeAsync">
            <returns>The task object that represents the results of this asynchronous operation.</returns>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.ResumeAsync(System.Threading.CancellationToken)">
            <param name="token">The CancellationToken used to stop iterating calls for more pages.</param>
            <returns>The task object that represents the results of this asynchronous operation.</returns>
            <exception cref="T:Microsoft.Graph.ServiceException">Thrown when the service encounters an error with
            a request.</exception>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.ExtractEntityListFromParsable(`1)">
            <summary>
            Helper method to extract the collection rom an <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/> instance.
            </summary>
            <param name="parsableCollection">The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/> to extract the collection from</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">Thrown when the object doesn't contain a collection inside it</exception>
        </member>
        <member name="M:Microsoft.Graph.PageIterator`2.ExtractNextLinkFromParsable(`1,System.String)">
            <summary>
            Helper method to extract the nextLink property from an <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/> instance.
            </summary>
            <param name="parsableCollection">The <see cref="T:Microsoft.Kiota.Abstractions.Serialization.IParsable"/> to extract the nextLink from</param>
            <param name="nextLinkPropertyName">The property name of the nextLink string</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Graph.PagingState">
            <summary>
            Specifies the state of the PageIterator.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.NotStarted">
            <summary>
            The iterator has neither started iterating thorugh the initial page nor request more pages.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.Paused">
            <summary>
            The callback returned false or a cancellation token was set. The iterator is resumeable.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.IntrapageIteration">
            <summary>
            Iterating across the contents of page.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.InterpageIteration">
            <summary>
            Iterating across paged requests.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.Delta">
            <summary>
            A deltaToken was returned. The iterator is resumeable.
            </summary>
        </member>
        <member name="F:Microsoft.Graph.PagingState.Complete">
            <summary>
            Reached the end of a non-deltaLink paged result set.
            </summary>
        </member>
    </members>
</doc>

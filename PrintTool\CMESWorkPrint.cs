﻿using EasyWork.bll.Public;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class CMESWorkPrint : Form
    {
        DataTable dt;
        public CMESWorkPrint()
        {
            InitializeComponent();
            printPreviewDialog1.Width = 100;
            printPreviewDialog1.Height = 300;
            //printDocument1.DefaultPageSettings.Landscape = true;
            this.printDocument1.OriginAtMargins = true;//启用页边距
            this.pageSetupDialog1.EnableMetric = true; //以毫米为单位    

            tblr.Text = PublicService.GetConfigData("WXPsidWorkLR", string.Empty);
            tbud.Text = PublicService.GetConfigData("WXPsidWorkUD", string.Empty);
            if (tblr.Text == "")
            {
                tblr.Text = "0";
            }
            if (tbud.Text == "")
            {
                tbud.Text = "0";
            }
            tb_sn.Focus();
        }

         private void printDocument1_PrintPage(object sender, System.Drawing.Printing.PrintPageEventArgs e)
         {
            int x = int.Parse(tblr.Text.Trim());
            int y = int.Parse(tbud.Text.Trim());
            BarCode.Code128 _Code = new BarCode.Code128();
            //string sn = "AJVVUT1222002688";
            //string psid = "RBBKK15H1B3";
            //string model = "York-AN10C";
            //string code = "03031234-001";
            //string imei = "A00000D0094E5A";
            //string type = "整机拆主板";
            //string incode = "03031234-002";
            //string error = "不开机，就是不开机啊";
            //string bsn = "9RHQUT212J000994";


            e.Graphics.DrawString("HONOR客户机跟踪工单", new Font("宋体", (float)13, FontStyle.Bold), Brushes.Black, 90 + x, -95 + y);


            //大横线
            e.Graphics.DrawLine(Pens.Black, 485 + x, -98 + y, -95 + x, -98 + y);//1
            //e.Graphics.DrawLine(Pens.Black, 485 + x, -76 + y, -95 + x, -76 + y);//2
            e.Graphics.DrawLine(Pens.Black, 485 + x, -28 + y, -95 + x, -28 + y);//3
            e.Graphics.DrawLine(Pens.Black, 485 + x, -8 + y, -95 + x, -8 + y);//4
            e.Graphics.DrawLine(Pens.Black, 485 + x, 11 + y, -95 + x, 11 + y);//5
            e.Graphics.DrawLine(Pens.Black, 485 + x, 30 + y, -95 + x, 30 + y);//6
            e.Graphics.DrawLine(Pens.Black, 485 + x, 50 + y, -95 + x, 50 + y);//7
            e.Graphics.DrawLine(Pens.Black, 485 + x, 70 + y, -95 + x, 70 + y);//8
            e.Graphics.DrawLine(Pens.Black, 485 + x, 90 + y, -95 + x, 90 + y);//9
            e.Graphics.DrawLine(Pens.Black, 210 + x, 110 + y, -95 + x, 110 + y);//10
            e.Graphics.DrawLine(Pens.Black, 210 + x, 130 + y, -95 + x, 130 + y);//11
            e.Graphics.DrawLine(Pens.Black, 210 + x, 150 + y, -95 + x, 150 + y);//12   205
            e.Graphics.DrawLine(Pens.Black, 210 + x, 170 + y, -95 + x, 170 + y);//13
            e.Graphics.DrawLine(Pens.Black, 210 + x, 190 + y, -95 + x, 190 + y);//14
            e.Graphics.DrawLine(Pens.Black, 485 + x, 210 + y, -95 + x, 210 + y);//15


            //大竖线
            e.Graphics.DrawLine(Pens.Black, -95 + x, -98 + y, -95 + x, 210 + y);
            e.Graphics.DrawLine(Pens.Black, 485 + x, -98 + y, 485 + x, 210 + y);
            e.Graphics.DrawLine(Pens.Black, 210 + x, -76 + y, 210 + x, -8 + y); //第二三四五行竖线
            e.Graphics.DrawLine(Pens.Black, -10 + x, -8 + y, -10 + x, 76 + y); //第四五行竖线
            e.Graphics.DrawLine(Pens.Black, 360 + x, 30 + y, 360 + x, 90 + y); //第四五行竖线
            //e.Graphics.DrawLine(Pens.Black, 380 + x, 25 + y, 380 + x, 125 + y); //第四五六七行竖线

            e.Graphics.DrawLine(Pens.Black, -55 + x, 50 + y, -55 + x, 210 + y); //第6-11行竖线
            e.Graphics.DrawLine(Pens.Black, -10 + x, 50 + y, -10 + x, 210 + y); 
            e.Graphics.DrawLine(Pens.Black, 55 + x, 50 + y, 55 + x, 210 + y); 
            e.Graphics.DrawLine(Pens.Black, 95 + x, 30 + y, 95 + x, 210 + y); 
            e.Graphics.DrawLine(Pens.Black, 145 + x, 50 + y, 145 + x, 210 + y); 
            e.Graphics.DrawLine(Pens.Black, 210 + x, 30 + y, 210 + x, 210 + y);

            //第一行数据
            //SN
            e.Graphics.DrawString("整机SN:", new Font("宋体", (float)10), Brushes.Black, -90 + x, -63 + y);
            Bitmap sn_ime = _Code.GetCodeImage(dt.Rows[0]["SN"].ToString(), BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(sn_ime, -30 + x, -73 + y, 212, 30);
            e.Graphics.DrawString(dt.Rows[0]["SN"].ToString(), new Font("宋体", (float)10), Brushes.Black, -30 + x, -43 + y);
            //BSN
            e.Graphics.DrawString("单板:", new Font("宋体", (float)10), Brushes.Black, 220 + x, -63 + y);
            Bitmap bsn_ime = _Code.GetCodeImage(dt.Rows[0]["VeneerCode"].ToString(), BarCode.Code128.Encode.Code128B);
            e.Graphics.DrawImage(bsn_ime, 260 + x, -73 + y, 212, 30);
            e.Graphics.DrawString(dt.Rows[0]["VeneerCode"].ToString(), new Font("宋体", (float)10), Brushes.Black, 260 + x, -43 + y);

            //第二行数据
            e.Graphics.DrawString("CCP建单时间:", new Font("宋体", (float)10, FontStyle.Bold), Brushes.Black, -30 + x, -25 + y);
            e.Graphics.DrawString(dt.Rows[0]["CCPTime"].ToString(), new Font("宋体", (float)10, FontStyle.Bold), Brushes.Black, 60 + x, -25 + y);
            e.Graphics.DrawString("结单日期:", new Font("宋体", (float)10, FontStyle.Bold), Brushes.Black, 260 + x, -25 + y);
            e.Graphics.DrawString((DateTime.Parse(dt.Rows[0]["CCPTime"].ToString()).AddDays(3).ToString()), new Font("宋体", (float)10, FontStyle.Bold), Brushes.Black, 330 + x, -25 + y);


            //第三行数据
            e.Graphics.DrawString("返厂原因:", new Font("宋体", (float)10), Brushes.Black, -90 + x, -2 + y);
            e.Graphics.DrawString(dt.Rows[0]["Originalfault"].ToString(), new Font("宋体", (float)8), Brushes.Black, -5 + x, -3 + y);

            //第四行数据
            e.Graphics.DrawString("传播名:", new Font("宋体", (float)10), Brushes.Black, -90 + x, 15 + y);
            e.Graphics.DrawString(dt.Rows[0]["Model"].ToString(), new Font("宋体", (float)8), Brushes.Black, -5 + x, 15 + y);

            //第四行数据
            e.Graphics.DrawString("维修类型", new Font("宋体", (float)10), Brushes.Black, -90 + x, 35 + y);
            e.Graphics.DrawString(dt.Rows[0]["WXtype"].ToString(), new Font("宋体", (float)8), Brushes.Black, -5 + x, 35 + y);
            e.Graphics.DrawString("保修状态:" + dt.Rows[0]["BXstate"].ToString(), new Font("宋体", (float)10,FontStyle.Bold), Brushes.Black, 105 + x, 35 + y);
            e.Graphics.DrawString("维修结果:", new Font("宋体", (float)10), Brushes.Black, 250 + x, 35 + y);
            e.Graphics.DrawString("OK:_", new Font("宋体", (float)10), Brushes.Black, 390 + x, 35 + y);
            e.Graphics.DrawString("退回:_", new Font("宋体", (float)10), Brushes.Black, 430 + x, 35 + y);

            //第五行数据
            e.Graphics.DrawString("工序", new Font("宋体", (float)10), Brushes.Black, -90 + x, 55 + y);
            e.Graphics.DrawString("工号", new Font("宋体", (float)10), Brushes.Black, -50 + x, 55 + y);
            e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 0 + x, 55 + y);
            e.Graphics.DrawString("工序", new Font("宋体", (float)10), Brushes.Black, 60 + x, 55 + y);
            e.Graphics.DrawString("工号", new Font("宋体", (float)10), Brushes.Black, 105 + x, 55 + y);
            e.Graphics.DrawString("不良故障", new Font("宋体", (float)8), Brushes.Black, 155 + x, 55 + y);
            e.Graphics.DrawString("是否下任务令:", new Font("宋体", (float)10), Brushes.Black, 250 + x, 55 + y);
            e.Graphics.DrawString("是:_", new Font("宋体", (float)10), Brushes.Black, 390 + x, 55 + y);
            e.Graphics.DrawString("否:_", new Font("宋体", (float)10), Brushes.Black, 440 + x, 55 + y);

            //第六行数据
            e.Graphics.DrawString("初检", new Font("宋体", (float)10), Brushes.Black, -90 + x, 73 + y);
            e.Graphics.DrawString("MT", new Font("宋体", (float)10), Brushes.Black, 65 + x, 72 + y);
            e.Graphics.DrawString("是否过CBT:", new Font("宋体", (float)10), Brushes.Black, 250 + x, 73 + y);
            e.Graphics.DrawString("是:_", new Font("宋体", (float)10), Brushes.Black, 390 + x, 73 + y);
            e.Graphics.DrawString("否:_", new Font("宋体", (float)10), Brushes.Black, 440 + x, 73 + y);

            //第七行数据
            e.Graphics.DrawString("拆机", new Font("宋体", (float)10), Brushes.Black, -90 + x, 93 + y);
            e.Graphics.DrawString("气密性", new Font("宋体", (float)8), Brushes.Black, 55 + x, 93 + y);
            e.Graphics.DrawString("备注:", new Font("宋体", (float)10), Brushes.Black, 215 + x, 93 + y);

            //第八行数据
            e.Graphics.DrawString("维修", new Font("宋体", (float)10), Brushes.Black, -90 + x, 113 + y);
            e.Graphics.DrawString("UT/CW", new Font("宋体", (float)10), Brushes.Black, 55 + x, 113 + y);

            //第九行数据
            e.Graphics.DrawString("CBT", new Font("宋体", (float)10), Brushes.Black, -90 + x, 133 + y);
            e.Graphics.DrawString("PQC", new Font("宋体", (float)10), Brushes.Black, 60 + x, 133 + y);

            //第十行数据
            e.Graphics.DrawString("组装", new Font("宋体", (float)10), Brushes.Black, -90 + x, 153 + y);
            e.Graphics.DrawString("压合", new Font("宋体", (float)10), Brushes.Black, 60 + x, 153 + y);

            //第十一行数据
            e.Graphics.DrawString("PT", new Font("宋体", (float)10), Brushes.Black, -90 + x, 173 + y);
            e.Graphics.DrawString("OBA", new Font("宋体", (float)10), Brushes.Black, 60 + x, 173 + y);

            //第十二行数据
            e.Graphics.DrawString("MMI", new Font("宋体", (float)10), Brushes.Black, -90 + x, 193 + y);

        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            if (tb_sn.Text.Trim() == "")
            {
                MessageBox.Show("SN为空，不能打印");
                return;
            }
            string sql = $"select * from mes_honor.dbo.cmes where sn = '{tb_sn.Text.Trim()}' order by id desc";
            dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count == 0)
            {
                MessageBox.Show("系统没有该SN数据");
                return;
            }

            //printPreviewDialog1.Document = printDocument1;
            //printPreviewDialog1.ShowDialog();

            printDocument1.Print();
            tb_sn.Text = "";
            tb_sn.Focus();
            return;
        }

        

        private void tb_sn_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }
    }
}

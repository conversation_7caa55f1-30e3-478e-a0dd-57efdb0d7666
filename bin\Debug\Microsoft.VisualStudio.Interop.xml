<?xml version="1.0"?>
<doc>
  <assembly>
    <name>Microsoft.VisualStudio.Interop</name>
  </assembly>
  <members>
    <member name="M:MarshalHelper.ReleaseComObject(System.Object)">
      <summary>
            A wrapper for <see cref="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)" /> that no-ops when run on .NET on non-Windows platforms.
            </summary>
    </member>
    <member name="T:Microsoft.VisualStudio.Interop.ExcludeFromIDLAttribute">
      <summary>
      </summary>
    </member>
    <member name="T:Microsoft.VisualStudio.Interop.ExcludeFromProxyBuildAttribute">
      <summary>
      </summary>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsProgress.Report(System.Double)">
      <summary>
            Reports the progress of an operation.
            </summary>
      <param name="value">The progress of the operation.</param>
      <remarks>
        <paramref name="value" /> can be in the range [0.0..1.0], where 0.0 is 0%
            complete and 1.0 is 100% complete.  A value outside that range indicates
            indeterminate progress.
            <para>
            Implementers should be prepared for this method to be called on any thread.
            </para></remarks>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult">
      <summary>
            The result of an asynchronous save operation.  This is the value returned from the
            GetResult method on the asynchronous save's returned <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsTask" />.
            </summary>
    </member>
    <member name="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.DataLossIncurred">
      <summary>
            This is set to <c>true</c> if the save was successful but there was data loss.
            </summary>
      <remarks>
            Setting this to <c>true</c> is similar to returning STG_S_DATALOSS from synchronous
            save operations.
            </remarks>
    </member>
    <member name="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.SaveCanceled">
      <summary>
            This is set to <c>true</c> if the user canceled the save operation.
            </summary>
    </member>
    <member name="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.FileName">
      <summary>
            The name of the saved file.  This can be <c>null</c> or empty if the name of the
            saved file did not change.
            </summary>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState">
      <summary>
            Optional interface that can be implemented by docdata objects that need to
            preserve state data at the beginning of an aync save operation.
            </summary>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState.GetAsyncSaveState">
      <summary>
            Returns a state object (such as a checkpoint) for an async save operation.
            </summary>
      <remarks>
            This method is called at the beginning of an asynchronous save. A docdata can
            implement this interface and supply a state object. It can retrieve the state
            object in its implementation of <see cref="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFile.SaveAsync(System.String,System.Boolean,Microsoft.VisualStudio.Shell.Interop.IVsProgress)" /> or
            <see cref="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFileFormat.SaveAsync(System.String,System.Boolean,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)" /> by calling
            IVsAsyncRunningDocumentTable.GetAsyncSaveState.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable.SaveDocumentsAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsHierarchy,System.UInt32,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves one or more documents asynchronously.
            </summary>
      <param name="saveOpts">The save options</param>
      <param name="hierarchy">
            The hierarchy that owns the document to save, or <c>null</c>.  This
            parameter is only used if <paramref name="docCookie" /> is VSCOOKIE_NIL
            and <paramref name="itemid" /> is not VSITEMID_SELECTION.  In that case,
            if this parameter is non-null, then the document identified by
            <paramref name="itemid" /> is saved.
            </param>
      <param name="itemid">
            The item identifier for the document to save, or VSITEMID_SELECTION.
            This parameter is only used if <paramref name="docCookie" /> is VSCOOKIE_NIL.
            In that case, if this value is VSITEMID_SELECTION then all selected documents
            are saved.  If this value is not VSITEMID_SELECTION and <paramref name="hierarchy" />
            is non-null, this value must be something other than VSITEMID_NIL.
            </param>
      <param name="docCookie">
            The cookie for the document, or VSCOOKIE_NIL.  If this parameter is not
            VSCOOKIE_NIL, <paramref name="hierarchy" /> and <paramref name="itemid" /> are
            ignored and the document identified by the cookie is saved.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>A task representing the asynchronous operation</returns>
      <remarks>
            If <paramref name="docCookie" /> is VSCOOKIE_NIL, <paramref name="itemid" />
            is not VSITEMID_SELECTION, and <paramref name="hierarchy" /> is <c>null</c>,
            all dirty documents are saved.
            <para>
            Implementers should be prepared for this method to be called on any thread.
            </para></remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable.GetAsyncSaveState(Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState)">
      <summary>
            Retrieves the state object (such as a checkpoint) for an async save operation.
            </summary>
      <param name="provider">The provider of the state object.</param>
      <remarks>
            This method is called from a docdata's implementation of <see cref="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFile.SaveAsync(System.String,System.Boolean,Microsoft.VisualStudio.Shell.Interop.IVsProgress)" />
            or <see cref="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFileFormat.SaveAsync(System.String,System.Boolean,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)" /> to fetch the state object supplied by
            <paramref name="provider" /> in its implementation of <see cref="M:Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState.GetAsyncSaveState" />.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable2.SaveDocumentsAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsHierarchy,System.UInt32,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <inheritdoc cref="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable.SaveDocumentsAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsHierarchy,System.UInt32,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)" />
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable2.GetAsyncSaveState(Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState)">
      <inheritdoc cref="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable.GetAsyncSaveState(Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState)" />
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable2.UpdateAsyncSaveState(Microsoft.VisualStudio.Shell.Interop.IVsProvideAsyncSaveState,System.Object)">
      <summary>
            Updates the state object (such as a checkpoint) for an async save operation.
            </summary>
      <param name="provider">The provider of the state object.</param>
      <param name="saveState">The provider's updated state object.</param>
      <remarks>
            A provider  might want to do this if the state object changed, say, while processing the
            OnBeforeSave or OnBeforeSaveAsync event.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncRunningDocumentTable2.UnlockDocumentAsync(System.UInt32,System.UInt32)">
      <summary>
            Removes a lock from a document.
            </summary>
      <param name="lockType">The type of lock to remove.</param>
      <param name="docCookie">The document cookie.</param>
      <returns>A task representing the asynchronous operation.</returns>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncShell.SaveDocDataToFileAsync(Microsoft.VisualStudio.Shell.Interop.VSSAVEFLAGS,System.Object,System.String,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves a docdata to a file asynchronously.
            </summary>
      <param name="flags">The save flags.</param>
      <param name="persistFile">The file in which the docdata is to be saved.</param>
      <param name="untitledPath">
            File path to which the doc data for an as-yet unsaved document is to be saved.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.  The result of the returned task
            will be <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult" />.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSolution.SaveSolutionElementAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsHierarchy,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves a solution element asynchronously.
            </summary>
      <param name="saveOpts">The save options.</param>
      <param name="hierarchy">The hierarchy.</param>
      <param name="docCookie">The document cookie.</param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncPersistHierarchyItem.SaveItemAsync(Microsoft.VisualStudio.Shell.Interop.VSSAVEFLAGS,System.String,System.UInt32,System.Object,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves a hierarchy item, asynchronously.
            </summary>
      <param name="flags">The save flags.</param>
      <param name="silentSaveAsName">
            The file name to be applied when <paramref name="flags" /> is set to VSSAVE_SilentSave.
            </param>
      <param name="itemid">The ID of the hierarchy item to be saved.</param>
      <param name="docData">The document data of the item to be saved.</param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.  The result of the returned task
            will be <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult" />.  The <see cref="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.FileName" />
            property is unused.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncPersistHierarchyItem.ReloadItemAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Reloads a hierarchy item, asynchronously.
            </summary>
      <param name="itemid">The ID of the hierarchy item to be reloaded.</param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncPersistDocData.LoadDocDataAsync(System.String,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Loads the document data for a given document, asynchronously.
            </summary>
      <param name="moniker">The moniker for the document to be loaded.</param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncPersistDocData.SaveDocDataAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves the document data, asynchronously.
            </summary>
      <param name="flags">The save flags</param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.  The result of the returned task
            will be <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult" />.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsAsyncPersistDocData.ReloadDocDataAsync(System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Reloads the document data asynchronously and in the process determines whether to ignore
            a subsequent file change.
            </summary>
      <param name="flags">
            Flag indicating whether to ignore the next file change when reloading the document data.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFile.LoadAsync(System.String,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Opens a specified file asynchronously and initializes an object from the file contents.
            </summary>
      <param name="filename">The name of the file to load.</param>
      <param name="grfMode">
            File format mode. If zero, the object uses the usual defaults as if the user had opened the file.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFile.SaveAsync(System.String,System.Boolean,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves a copy of the object to the specified file, asynchronously.
            </summary>
      <param name="filename">
            The file name. This parameter can be <c>null</c>, in which case the object is saved to its
            current file. If the object is in the untitled state and <paramref name="filename" /> is <c>null</c>,
            <see cref="T:System.ArgumentException" /> is thrown.
            </param>
      <param name="remember">
            Indicates whether <paramref name="filename" /> is to be used as the current working file.  If
            <c>true</c>, <paramref name="filename" /> becomes the current file and the object should clear
            its dirty flag after the save. If <c>false</c>, this save operation is a Save a Copy As operation.
            In this case, the current file is unchanged and the object does not clear its dirty flag.
            If <paramref name="filename" /> is <c>null</c>, <paramref name="remember" /> is ignored.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.  The result of the returned task
            will be <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult" />.  The <see cref="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.FileName" />
            property is unused.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFileFormat.LoadAsync(System.String,System.UInt32,System.Boolean,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Opens a specified file asynchronously and initializes an object from the file contents.
            </summary>
      <param name="filename">The name of the file to load.</param>
      <param name="grfMode">
            File format mode. If zero, the object uses the usual defaults as if the user had opened the file.
            </param>
      <param name="readOnly">
            A value of <c>true</c> indicates that the file should be opened as read-only.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IAsyncPersistFileFormat.SaveAsync(System.String,System.Boolean,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsProgress)">
      <summary>
            Saves a copy of the object to the specified file, asynchronously.
            </summary>
      <param name="filename">
            The file name. This parameter can be <c>null</c>, in which case the object is saved to its
            current file. If the object is in the untitled state and <paramref name="filename" /> is <c>null</c>,
            <see cref="T:System.ArgumentException" /> is thrown.
            </param>
      <param name="remember">
            Indicates whether <paramref name="filename" /> is to be used as the current working file.  If
            <c>true</c>, <paramref name="filename" /> becomes the current file and the object should clear
            its dirty flag after the save. If <c>false</c>, this save operation is a Save a Copy As operation.
            In this case, the current file is unchanged and the object does not clear its dirty flag.
            If <paramref name="filename" /> is <c>null</c>, <paramref name="remember" /> is ignored.
            </param>
      <param name="formatIndex">
            A value that indicates the format in which the file will be saved. The caller passes <c>DEF_FORMAT_INDEX</c>
            if the object is to choose its default (current) format. Otherwise, the value is interpreted as the index
            into the list of formats, as returned by a call to the <see cref="M:Microsoft.VisualStudio.Shell.Interop.IPersistFileFormat.GetFormatList(System.String@)" />
            method. An index value of 0 indicates the first format, 1 the second format, and so on.
            </param>
      <param name="progress">The interface through which progress is reported.</param>
      <returns>
            A task representing the asynchronous operation.  The result of the returned task
            will be <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult" />.  The <see cref="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncSaveResult.FileName" />
            property is unused.
            </returns>
      <remarks>
            Implementers should be prepared for this method to be called on any thread.
            </remarks>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.__VSRDTSAVENOTIFICATIONFLAGS">
      <summary>
            A set of flags that describe attributes about a given save.
            </summary>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocumentTable6.NotifyOnBeforeSave(System.UInt32,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsTask)">
      <summary>
            Notifies event listeners that a save is about to occur.
            </summary>
      <param name="cookie">The document cookie.</param>
      <param name="saveNotificationFlags">Provides additional information about the save.</param>
      <param name="saveTask">A task representing the save operation.</param>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocumentTable6.NotifyOnAfterSave(System.UInt32,System.UInt32)">
      <summary>
            Notifies event listeners that a save has occurred.
            </summary>
      <param name="cookie">The document cookie.</param>
      <param name="saveNotificationFlags">Provides additional information about the save.</param>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents7.OnBeforeSaveAsync(System.UInt32,System.UInt32,Microsoft.VisualStudio.Shell.Interop.IVsTask)">
      <summary>
            Called right before a save is about to occur.
            </summary>
      <param name="cookie">The document cookie.</param>
      <param name="flags">Provides additional information about the save.</param>
      <param name="saveTask">A task representing the save operation.</param>
      <returns>
            An optional task representing async work done by the event sink.  If this is non-null,
            the RunningDocTable will await its completion before continuing the save.
            </returns>
      <remarks>
            When the RunningDocTable notifies the event sink about save events, it will first check if the event implements this
            interface and call this method. If not, it will check if it implements <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents3" />
            and call <see cref="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents3.OnBeforeSave(System.UInt32)" />. Note that this means the RDT will not call both methods.
            <para>
            Implementers should be prepared for this method to be called on any thread.
            </para></remarks>
    </member>
    <member name="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents7.OnAfterSaveAsync(System.UInt32,System.UInt32)">
      <summary>
            Called right after a save has occurred.
            </summary>
      <param name="cookie">The document cookie.</param>
      <param name="flags">Provides additional information about the save.</param>
      <returns>
            An optional task representing async work done by the event sink.  If this is non-null,
            the RunningDocTable will await its completion before continuing the save.
            </returns>
      <remarks>
            When the RunningDocTable notifies the event sink about save events, it will first check if the event implements this
            interface and call this method. If not, it will check if it implements <see cref="T:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents3" />
            and call <see cref="M:Microsoft.VisualStudio.Shell.Interop.IVsRunningDocTableEvents3.OnAfterSave(System.UInt32)" />. Note that this means the RDT will not call both methods.
            <para>
            Implementers should be prepared for this method to be called on any thread.
            </para></remarks>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.__VSSTATUSBARANIMATIONINDEX">
      <summary>
            A set of built in status bar animation indices.
            </summary>
    </member>
    <member name="F:Microsoft.VisualStudio.Shell.Interop.__VSSPROPID12.VSSPROPID_ShutdownStarting">
      <summary>
            This property will be set to true after any required pre-close checks have been passed (files saved),
            but before any shutdown work has begun (like solution close)
            </summary>
    </member>
    <member name="F:Microsoft.VisualStudio.Shell.Interop.__VSSPROPID13.VSSPROPID_EnableEnhancedTooltips">
      <summary>
            This property controls whether the enhanced tooltip behavior (tooltip will show on focus) is enabled in VS.
            </summary>
    </member>
    <member name="F:Microsoft.VisualStudio.Shell.Interop.__VSSPROPID13.VSHPROPID_SlowEnumeration">
      <summary>
            This property indicates that a hierarchy and its nested items may be slow to be enumerated.
            </summary>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.IVsAsyncCommandParameters">
      <summary>
            An instance of this interface is sent as the input argument to commands with the <c>AsyncFromUIInvocation</c>,
            when they are executed by a UI gesture such as a menu command, toolbar button, or key binding.
            </summary>
    </member>
    <member name="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncCommandParameters.ShouldRunAsync">
      <summary>
            Indicates whether the command should be run asynchronously
            </summary>
    </member>
    <member name="P:Microsoft.VisualStudio.Shell.Interop.IVsAsyncCommandParameters.OriginalArgument">
      <summary>
            The original argument to the command invocation, or <c>null</c> if none was specified.
            </summary>
    </member>
    <member name="T:Microsoft.VisualStudio.Shell.Interop.__FCSTORAGEFLAGS2">
      <summary>
            Adds to the <see cref="T:Microsoft.VisualStudio.Shell.Interop.__FCSTORAGEFLAGS" /> enumaration.
            </summary>
    </member>
    <member name="F:Microsoft.VisualStudio.Shell.Interop.__FCSTORAGEFLAGS2.FCSF_AVOIDPACKAGELOAD">
      <summary>
            Avoids loading packages when opening a category.
            This is useful when retrieving font and color info that don't require package loads.
            </summary>
    </member>
    <member name="M:Microsoft.VisualStudio.Designer.Interfaces.IVSMDPerPropertyBrowsing.GetPropertyAttributes(System.Int32,System.UInt32@,System.IntPtr,System.IntPtr)">
      <summary>
            Gets the list of attributes for the object.
            </summary>
      <param name="dispid">The dispid of the property to retrieve attributes</param>
      <param name="pceltAttrs">the number of attribute type names in <paramref name="ppbstrTypeNames" /></param>
      <param name="ppbstrTypeNames">Attribute type names, such as System.ComponentModel.BrowsableAttribute, or System.ComponentModel.DescriptionAttribute.  This can be the name of any type that derives from System.Attribute. The array is callee allocated and will be caller freed using <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />. Strings themselves should be freed with <see cref="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)" />. It can also be a static instance name such as System.ComponentModel.BrowsableAttribute.No, which will cause the initializer value to be ignored.</param>
      <param name="ppvarAttrValues">An array of variants to be used to initialize the given attributes.  If the attributes have a ctor that takes a parameter, the given argument will be used to iniitalize the attribute.  If the initializer is NULL, VT_EMPTY or VT_NULL, the default ctor will be called.  Variants will be caller freed individually by pInvoking VariantClear. The array must be freed calling <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />.
            </param>
    </member>
    <member name="T:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch">
      <summary>
             State of single reload batch. Include description of relevant [externally] changed files,
             resulting project /solution actions (such reload).
            
             IVsSolutionReloadBatch is freethreaded object and can be used without switching to UI thread.
             </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.DisableChangeNotifications">
      <summary>
            Needed to support original solution external file changes handler behavoir. This will only apply while batch is active.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.IsFilesChanged(System.UInt32,System.String[],System.Boolean[])">
      <summary>
            Check the requested files against batch state (based on aggregated changes files via TryAddChangedFiles).
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.GetProjectsAction(System.UInt32,System.Guid[],System.UInt32[])">
      <summary>
            Check the status of a projects
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.TryAddSolutionReload">
      <summary>
            Request solution reload
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.TryAddProjectsReload(System.UInt32,System.UInt32,System.Guid[])">
      <summary>
            Request specific projects reload, fForceReload applies to all specified projects. Note even if reloadAction = DRA_ReloadProject [some of] selected proejcts can still end up being forcefully reloaded (even if they support own reload)
            if some other logic reqires that for same project[s]. If fForceRelad = DRA_ForceReloadProject, it is guarantee that they will be forcefully reloaded (unless solution is reloaded which will override any project reloads).
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.TryAddChangedFiles(System.UInt32,System.String[])">
      <summary>
            Add files changes hints (can be used by reload logic extender during compute stage).
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.BatchId">
      <summary>
            Batch id - unique withing solution open session.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.LastChangeId">
      <summary>
            Last state change id. Each call to TryAddXXX might change the state if introduce a new reload action or new changed files hint.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.Stage">
      <summary>
            Note if BatchStage != COMPLETED, the state can be changed at any time.
            Only the IVsSolutionBatchUpdateManagerEvents event listeners are guaranteed to have stable data in this case
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.HandlerId">
      <summary>
            batch handler friendly id.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.HasSolutionReload">
      <summary>
            batch require solution reload.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.BatchTask">
      <summary>
            VS task tracking batch progress.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.RequestComplete">
      <summary>
            VS request batch completion as soon as possible.
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch.RequestCancel">
      <summary>
            VS request batch cancellation.
            </summary>
    </member>
    <member name="T:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler">
      <summary>
            State of single reload batch. Include description of relevant [externally] changed files,
            resulting project /solution actions (such reload).
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.CanGiveControl(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler)">
      <summary>
            check current provider if it will allow giving up control to a different reload handler.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.CanTakeControl(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler)">
      <summary>
            check if provider can/want take control and handle the currently opened scope.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.CanJoin(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler)">
      <summary>
            check if provider is ok to join the currently opened scope and let existing handler drive it.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.CanCommitBatch(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch,System.UInt32,System.UInt32,System.UInt32@)">
      <summary>
            called on BG thread when a state changed, or handler instructed us (via timeout).
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.CanCommitBatchUI(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch,System.UInt32,System.UInt32,System.UInt32@)">
      <summary>
            called on UI thread before commencing modal reload phase.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.ExecuteReloadUI(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatch,System.UInt32,System.Guid[],System.UInt32[])">
      <summary>
            comence the actual reload, once this is called no additional changes can be included in batch scope and it will be closed (one way or another) after this method finis.
            In theory we can support async, but all current cases do require UI thread anyway (and it is unlikely we want to change that).
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler.HandlerId">
      <summary>
            friendly HandlerId. it is string instead of usual guid for better readibility. We dont publish any predefined values.
            this scenario is always an arm race, so if specific provider wants details they need to discover them.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_ReloadProject">
      <summary>
            Project to be reload (closed and opened) if needed, Example: project file[s] change. If project can not handle external change, it will be reloaded by solution.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_ForceReloadProject">
      <summary>
            Project to be reload (closed and opened) for this batch.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_ReloadSolution">
      <summary>
            Force solution reload, superseeds any other actions.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_AddProject">
      <summary>
            Project to be added to the solution for this batch.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_RemoveProject">
      <summary>
            Project to be removed from the solution for this batch.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_RenameProject">
      <summary>
            Project to be renamed in the solution for this batch.
            </summary>
    </member>
    <member name="F:Microsoft.Internal.VisualStudio.Shell.Interop._DelayedReloadAction.DRA_UpdateProjectParent">
      <summary>
            Project parent to be changed in the solution for this batch.
            </summary>
    </member>
    <member name="T:Microsoft.Internal.VisualStudio.Shell.Interop.SVsSolutionReloadManagerService">
      <summary>
            IVsSolutionReloadManager implementation service id.
            </summary>
    </member>
    <member name="T:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager">
      <summary>
             Manages external changes that are resolved to project[s] reload.
             Provides batching and ability to specialize reload logic.
            
             IVsSolutionReloadManager is freethreaded service and can be called from any thread without switching to UI thread.
             </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager.Advise(System.Guid@,System.Object)">
      <summary>
            The IVsSolutionReloadManagerEvents can be used to both react on reload changes as well as influence the outcome.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager.Unadvise(System.UInt32)">
      <summary>
            The IVsSolutionReloadManagerEvents can be used to both react on reload changes as well as influence the outcome.
            </summary>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager.BeginBatch(Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadBatchHandler)">
      <summary>
            Start a reload batch scope
            </summary>
      <param name="pExecutor">pExecutor is the batch commit handler. can be null in which case we use the current or default one.</param>
    </member>
    <member name="M:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager.AddSolutionReload">
      <summary>
            A mechniasm for propagating "unbound" reload changes, aka where changes handler, just want to be included in any currently open batch.
            if there is no such we will create a default batch for these.
            Changes can still can be rejected from reload manager (for example if there is no open solution). In that case we return ppHandlingBatch==null;
            </summary>
    </member>
    <member name="P:Microsoft.Internal.VisualStudio.Shell.Interop.IVsSolutionReloadManager.CurrentBatch">
      <summary>
            get the current reload batch (if any, can return null).
            </summary>
    </member>
    <member name="M:EnvDTE90a.Process4.Attach2(System.Object)">
      <summary>
      </summary>
      <param name="Engines">Single <see cref="T:System.String" /> or an array of either <see cref="T:System.String" /> or <see cref="T:EnvDTE80.Engine" /> objects</param>
    </member>
    <member name="M:EnvDTE90.Process3.Attach2(System.Object)">
      <summary>
      </summary>
      <param name="Engines">Single <see cref="T:System.String" /> or an array of either <see cref="T:System.String" /> or <see cref="T:EnvDTE80.Engine" /> objects</param>
    </member>
    <member name="M:EnvDTE80.Process2.Attach2(System.Object)">
      <summary>
      </summary>
      <param name="Engines">Single <see cref="T:System.String" /> or an array of either <see cref="T:System.String" /> or <see cref="T:EnvDTE80.Engine" /> objects</param>
    </member>
    <member name="M:EnvDTE80.TaskItems2.Add(System.String,System.String,System.String,EnvDTE.vsTaskPriority,System.Object,System.Boolean,System.String,System.Int32,System.Boolean,System.Boolean)">
      <summary>
      </summary>
      <remarks>The <paramref name="Priority" /> defaults to <see cref="F:EnvDTE.vsTaskPriority.vsTaskPriorityMedium" /> in IDL but we can't default it here as the <paramref name="Icon" /> cannot be defaulted</remarks>
    </member>
    <member name="M:EnvDTE80.TaskItems2.Add2(System.String,System.String,System.String,EnvDTE.vsTaskPriority,System.Object,System.Boolean,System.String,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
      <summary>
      </summary>
      <remarks>The <paramref name="Priority" /> defaults to <see cref="F:EnvDTE.vsTaskPriority.vsTaskPriorityMedium" /> in IDL but we can't default it here as the <paramref name="Icon" /> cannot be defaulted</remarks>
    </member>
    <member name="M:EnvDTE80.EditPoint2.TryToShow(EnvDTE.vsPaneShowHow,System.Object)">
      <summary>
      </summary>
      <remarks>The <paramref name="How" /> param defaults to <see cref="F:EnvDTE.vsPaneShowHow.vsPaneShowCentered" /> in IDL but we can't default it here because <paramref name="PointOrCount" /> can't be defaulted.</remarks>
    </member>
    <member name="M:EnvDTE80.TextPane2.TryToShow(EnvDTE.TextPoint,EnvDTE.vsPaneShowHow,System.Object)">
      <summary>
      </summary>
      <remarks>The <paramref name="How" /> param defaults to <see cref="F:EnvDTE.vsPaneShowHow.vsPaneShowAsIs" /> in IDL but can't be defaulted here because <paramref name="PointOrCount" /> can't be defaulted.</remarks>
    </member>
    <member name="M:EnvDTE.TextPane.TryToShow(EnvDTE.TextPoint,EnvDTE.vsPaneShowHow,System.Object)">
      <summary>
      </summary>
      <remarks>The <paramref name="How" /> param defaults to <see cref="F:EnvDTE.vsPaneShowHow.vsPaneShowAsIs" /> in IDL but can't be defaulted here because <paramref name="PointOrCount" /> can't be defaulted.</remarks>
    </member>
    <member name="M:EnvDTE.TaskItems.Add(System.String,System.String,System.String,EnvDTE.vsTaskPriority,System.Object,System.Boolean,System.String,System.Int32,System.Boolean,System.Boolean)">
      <summary>
      </summary>
      <remarks>The <paramref name="Priority" /> defaults to <see cref="F:EnvDTE.vsTaskPriority.vsTaskPriorityMedium" /> in IDL but we can't default it here as the <paramref name="Icon" /> cannot be defaulted</remarks>
    </member>
  </members>
<Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315" /><SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" /><DigestValue>AKxRDBkV4o7Q/cDYibCY1LmnuaVBYPdKruW0mqNqPJA=</DigestValue></Reference></SignedInfo><SignatureValue>VIdlLWdJAa7cFvbf5QsVmGirvNEkjKPgsV2kaoVOz/dkmIooZcVpU7TUXSMmAMIkLuWrS0AzSbhROTKXN9n2w+kl+uVd+85q1sm717WGcIFzv3T8oG1QpTA9WmwNZg944xSE1m1EiZtaPbnmkaBT3u9/f3hJJppipu0tp/IK/hIPhKJgnl4svO56R7hMROkVPzMhRmpZfN4Xdem4a9/Bj5RuNIcT9rsRrojeF+ND/qQAyE0ttLN6jXYpLp0+LOxx3uusGKJ6O7R6YFftsSfWTKsU/BfJrDr+Cs4VbODesyDKOqn4H28hQ/8Sn8oAvg6+YU7QfsL3+VkEolkGDasipA==</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>t7DdFnHRqRlz2SG+YjXxQdMWfK5yb2J8Q+lH9gR14JaW0xH6Hvpjv/6C1pEcQMKaXYrbElTg9KIJSm7Z4fVqdgwES3MWxmluGGpzlkgdS8i0aR550OTzpYdlOba4ON4EI75TWZUAd5S/s6z7WzbzAOxNFpJqPmemBZ7H+2npPihs2hm6AHhTuLimY0F2OUZjMxO9AcGs+4bwNOYw1EXUSh9Iv9civekw7j+yckRSzrwN1FzVs9NEfcO6aTA3DZV7a5mz4oL98RPRX6X5iUbYjmUCne9yu9lro5o+v0rt/gwU6TquzYHZ7VtpSX1912uqHuBfT5PcUYZMB7JOybvRPw==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>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</X509Certificate><X509Certificate>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</X509Certificate><X509Certificate>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</X509Certificate></X509Data></KeyInfo><Object Id="ts-countersig"><X509Data><X509Certificate>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</X509Certificate><X509Certificate>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</X509Certificate></X509Data><CounterSignature ts-format="cms-timestamp-message" xmlns="http://schemas.microsoft.com/xmldsig/timestamp/2003">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</CounterSignature></Object></Signature></doc>
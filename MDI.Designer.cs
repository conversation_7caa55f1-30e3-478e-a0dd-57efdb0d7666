﻿namespace EasyWork.Honor
{
    partial class MDI
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MDI));
            this.Top_panel = new System.Windows.Forms.Panel();
            this.Me_Name = new System.Windows.Forms.Label();
            this.Logo_panel = new System.Windows.Forms.Panel();
            this.MeBrie_label = new System.Windows.Forms.Label();
            this.LogoMe = new System.Windows.Forms.PictureBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tab_HW = new System.Windows.Forms.TabPage();
            this.Bar_HW = new DevComponents.DotNetBar.SideBar();
            this.HonorApp = new DevComponents.DotNetBar.SideBarPanelItem();
            this.bt_Compartner1 = new DevComponents.DotNetBar.ButtonItem();
            this.bt_Compartner2 = new DevComponents.DotNetBar.ButtonItem();
            this.bt_KnowledgeLibrary1 = new DevComponents.DotNetBar.ButtonItem();
            this.bt_KnowledgeLibrary2 = new DevComponents.DotNetBar.ButtonItem();
            this.CCP01 = new DevComponents.DotNetBar.ButtonItem();
            this.CCP02 = new DevComponents.DotNetBar.ButtonItem();
            this.CCP03 = new DevComponents.DotNetBar.ButtonItem();
            this.CCP04 = new DevComponents.DotNetBar.ButtonItem();
            this.TGMES01 = new DevComponents.DotNetBar.ButtonItem();
            this.TGMES02 = new DevComponents.DotNetBar.ButtonItem();
            this.TGMES03 = new DevComponents.DotNetBar.ButtonItem();
            this.CSPM01 = new DevComponents.DotNetBar.ButtonItem();
            this.CSPM02 = new DevComponents.DotNetBar.ButtonItem();
            this.webCompartner = new DevComponents.DotNetBar.ButtonItem();
            this.btWXMES = new DevComponents.DotNetBar.SideBarPanelItem();
            this.btAutoReadWXReport = new DevComponents.DotNetBar.ButtonItem();
            this.btDataVal = new DevComponents.DotNetBar.ButtonItem();
            this.btColorBoxPrint = new DevComponents.DotNetBar.ButtonItem();
            this.btWXFenPei = new DevComponents.DotNetBar.ButtonItem();
            this.btWXMESDataMerge = new DevComponents.DotNetBar.ButtonItem();
            this.btAddZPLSeripts = new DevComponents.DotNetBar.ButtonItem();
            this.btAutoReadILookLog = new DevComponents.DotNetBar.ButtonItem();
            this.PCDataVal = new DevComponents.DotNetBar.ButtonItem();
            this.menuStrip = new System.Windows.Forms.MenuStrip();
            this.项目PToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.知识库ZToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ts_oldweknow = new System.Windows.Forms.ToolStripMenuItem();
            this.ts_new_weknow = new System.Windows.Forms.ToolStripMenuItem();
            this.tab_BW = new System.Windows.Forms.TabPage();
            this.Bar_BW = new DevComponents.DotNetBar.SideBar();
            this.btPrintTool = new DevComponents.DotNetBar.SideBarPanelItem();
            this.bt_ZPLPublicPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_MingPaiPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_ChuHuoPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_BoxNoPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_WXPsidWorkPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_GZChuHuoPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_CheckReportPrint = new DevComponents.DotNetBar.ButtonItem();
            this.bt_CMESWorkPrint = new DevComponents.DotNetBar.ButtonItem();
            this.btClientPlugins = new DevComponents.DotNetBar.SideBarPanelItem();
            this.btHHTPlugins = new DevComponents.DotNetBar.ButtonItem();
            this.sideBarPanelItem1 = new DevComponents.DotNetBar.SideBarPanelItem();
            this.buttonItem2 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem6 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem7 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem8 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem9 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem10 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem11 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem12 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem13 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem14 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem15 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem16 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem17 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem18 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem19 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem20 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem21 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem22 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem23 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem24 = new DevComponents.DotNetBar.ButtonItem();
            this.buttonItem1 = new DevComponents.DotNetBar.ButtonItem();
            this.CSPM03 = new DevComponents.DotNetBar.ButtonItem();
            this.CSPM04 = new DevComponents.DotNetBar.ButtonItem();
            this.Top_panel.SuspendLayout();
            this.Logo_panel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LogoMe)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.tab_HW.SuspendLayout();
            this.menuStrip.SuspendLayout();
            this.tab_BW.SuspendLayout();
            this.SuspendLayout();
            // 
            // Top_panel
            // 
            this.Top_panel.BackColor = System.Drawing.Color.White;
            this.Top_panel.Controls.Add(this.Me_Name);
            this.Top_panel.Controls.Add(this.Logo_panel);
            this.Top_panel.Dock = System.Windows.Forms.DockStyle.Top;
            this.Top_panel.Location = new System.Drawing.Point(0, 0);
            this.Top_panel.Name = "Top_panel";
            this.Top_panel.Size = new System.Drawing.Size(264, 120);
            this.Top_panel.TabIndex = 1;
            // 
            // Me_Name
            // 
            this.Me_Name.AutoSize = true;
            this.Me_Name.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Me_Name.ForeColor = System.Drawing.Color.SteelBlue;
            this.Me_Name.Location = new System.Drawing.Point(129, 11);
            this.Me_Name.Name = "Me_Name";
            this.Me_Name.Size = new System.Drawing.Size(0, 17);
            this.Me_Name.TabIndex = 5;
            // 
            // Logo_panel
            // 
            this.Logo_panel.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("Logo_panel.BackgroundImage")));
            this.Logo_panel.Controls.Add(this.MeBrie_label);
            this.Logo_panel.Controls.Add(this.LogoMe);
            this.Logo_panel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Logo_panel.Dock = System.Windows.Forms.DockStyle.Right;
            this.Logo_panel.Location = new System.Drawing.Point(-499, 0);
            this.Logo_panel.Name = "Logo_panel";
            this.Logo_panel.Size = new System.Drawing.Size(763, 120);
            this.Logo_panel.TabIndex = 0;
            // 
            // MeBrie_label
            // 
            this.MeBrie_label.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MeBrie_label.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(120)))), ((int)(((byte)(120)))));
            this.MeBrie_label.Location = new System.Drawing.Point(582, 4);
            this.MeBrie_label.Name = "MeBrie_label";
            this.MeBrie_label.Size = new System.Drawing.Size(174, 113);
            this.MeBrie_label.TabIndex = 7;
            this.MeBrie_label.Text = "曾经学霸的我只因好奇学渣的世界，进去就迷路了......";
            // 
            // LogoMe
            // 
            this.LogoMe.Image = ((System.Drawing.Image)(resources.GetObject("LogoMe.Image")));
            this.LogoMe.Location = new System.Drawing.Point(502, 3);
            this.LogoMe.Name = "LogoMe";
            this.LogoMe.Size = new System.Drawing.Size(71, 76);
            this.LogoMe.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.LogoMe.TabIndex = 4;
            this.LogoMe.TabStop = false;
            // 
            // tabControl1
            // 
            this.tabControl1.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.tabControl1.Controls.Add(this.tab_HW);
            this.tabControl1.Controls.Add(this.tab_BW);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabControl1.ItemSize = new System.Drawing.Size(50, 40);
            this.tabControl1.Location = new System.Drawing.Point(0, 120);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(264, 533);
            this.tabControl1.TabIndex = 4;
            // 
            // tab_HW
            // 
            this.tab_HW.Controls.Add(this.Bar_HW);
            this.tab_HW.Controls.Add(this.menuStrip);
            this.tab_HW.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tab_HW.Location = new System.Drawing.Point(4, 4);
            this.tab_HW.Name = "tab_HW";
            this.tab_HW.Padding = new System.Windows.Forms.Padding(3);
            this.tab_HW.Size = new System.Drawing.Size(256, 485);
            this.tab_HW.TabIndex = 1;
            this.tab_HW.Text = "荣耀应用";
            this.tab_HW.ToolTipText = "华为应用";
            this.tab_HW.UseVisualStyleBackColor = true;
            // 
            // Bar_HW
            // 
            this.Bar_HW.AccessibleRole = System.Windows.Forms.AccessibleRole.ToolBar;
            this.Bar_HW.BorderStyle = DevComponents.DotNetBar.eBorderType.None;
            this.Bar_HW.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Bar_HW.ExpandedPanel = this.HonorApp;
            this.Bar_HW.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.World);
            this.Bar_HW.Location = new System.Drawing.Point(3, 3);
            this.Bar_HW.Name = "Bar_HW";
            this.Bar_HW.Panels.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.HonorApp,
            this.btWXMES});
            this.Bar_HW.Size = new System.Drawing.Size(250, 479);
            this.Bar_HW.Style = DevComponents.DotNetBar.eDotNetBarStyle.Office2007;
            this.Bar_HW.TabIndex = 0;
            this.Bar_HW.Text = "BarHW";
            // 
            // HonorApp
            // 
            this.HonorApp.FontBold = true;
            this.HonorApp.Name = "HonorApp";
            this.HonorApp.SubItems.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.bt_Compartner1,
            this.bt_Compartner2,
            this.bt_KnowledgeLibrary1,
            this.bt_KnowledgeLibrary2,
            this.CCP01,
            this.CCP02,
            this.CCP03,
            this.CCP04,
            this.TGMES01,
            this.TGMES02,
            this.TGMES03,
            this.CSPM01,
            this.CSPM02,
            this.CSPM03,
            this.CSPM04,
            this.webCompartner});
            this.HonorApp.Text = "荣耀应用";
            // 
            // bt_Compartner1
            // 
            this.bt_Compartner1.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_Compartner1.ImagePaddingHorizontal = 8;
            this.bt_Compartner1.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_Compartner1.Name = "bt_Compartner1";
            this.bt_Compartner1.Text = "Compartner1";
            this.bt_Compartner1.Click += new System.EventHandler(this.bt_Compartner1_Click);
            // 
            // bt_Compartner2
            // 
            this.bt_Compartner2.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_Compartner2.ImagePaddingHorizontal = 8;
            this.bt_Compartner2.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_Compartner2.Name = "bt_Compartner2";
            this.bt_Compartner2.Text = "Compartner2";
            this.bt_Compartner2.Click += new System.EventHandler(this.bt_Compartner2_Click);
            // 
            // bt_KnowledgeLibrary1
            // 
            this.bt_KnowledgeLibrary1.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_KnowledgeLibrary1.ImagePaddingHorizontal = 8;
            this.bt_KnowledgeLibrary1.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_KnowledgeLibrary1.Name = "bt_KnowledgeLibrary1";
            this.bt_KnowledgeLibrary1.Text = "知识库";
            this.bt_KnowledgeLibrary1.Click += new System.EventHandler(this.bt_KnowledgeLibrary1_Click);
            // 
            // bt_KnowledgeLibrary2
            // 
            this.bt_KnowledgeLibrary2.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_KnowledgeLibrary2.ImagePaddingHorizontal = 8;
            this.bt_KnowledgeLibrary2.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_KnowledgeLibrary2.Name = "bt_KnowledgeLibrary2";
            this.bt_KnowledgeLibrary2.Text = "知识库2";
            this.bt_KnowledgeLibrary2.Click += new System.EventHandler(this.bt_KnowledgeLibrary2_Click);
            // 
            // CCP01
            // 
            this.CCP01.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CCP01.ImagePaddingHorizontal = 8;
            this.CCP01.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CCP01.Name = "CCP01";
            this.CCP01.Text = "CCP01-物流员";
            this.CCP01.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CCP02
            // 
            this.CCP02.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CCP02.ImagePaddingHorizontal = 8;
            this.CCP02.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CCP02.Name = "CCP02";
            this.CCP02.Text = "CCP02-工程师";
            this.CCP02.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CCP03
            // 
            this.CCP03.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CCP03.ImagePaddingHorizontal = 8;
            this.CCP03.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CCP03.Name = "CCP03";
            this.CCP03.Text = "CCP03-物流员2";
            this.CCP03.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CCP04
            // 
            this.CCP04.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CCP04.ImagePaddingHorizontal = 8;
            this.CCP04.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CCP04.Name = "CCP04";
            this.CCP04.Text = "CCP04-工程师2";
            this.CCP04.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // TGMES01
            // 
            this.TGMES01.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.TGMES01.ImagePaddingHorizontal = 8;
            this.TGMES01.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.TGMES01.Name = "TGMES01";
            this.TGMES01.Text = "TGMES01-录入";
            this.TGMES01.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // TGMES02
            // 
            this.TGMES02.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.TGMES02.ImagePaddingHorizontal = 8;
            this.TGMES02.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.TGMES02.Name = "TGMES02";
            this.TGMES02.Text = "TGMES02-接收";
            this.TGMES02.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // TGMES03
            // 
            this.TGMES03.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.TGMES03.ImagePaddingHorizontal = 8;
            this.TGMES03.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.TGMES03.Name = "TGMES03";
            this.TGMES03.Text = "TGMES03-查询";
            this.TGMES03.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CSPM01
            // 
            this.CSPM01.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CSPM01.ImagePaddingHorizontal = 8;
            this.CSPM01.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CSPM01.Name = "CSPM01";
            this.CSPM01.Text = "CSPM01-物料查询";
            this.CSPM01.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CSPM02
            // 
            this.CSPM02.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CSPM02.ImagePaddingHorizontal = 8;
            this.CSPM02.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CSPM02.Name = "CSPM02";
            this.CSPM02.Text = "CSPM02-BOM查询";
            this.CSPM02.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // webCompartner
            // 
            this.webCompartner.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.webCompartner.ImagePaddingHorizontal = 8;
            this.webCompartner.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.webCompartner.Name = "webCompartner";
            this.webCompartner.Text = "WebCompartner";
            this.webCompartner.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // btWXMES
            // 
            this.btWXMES.FontBold = true;
            this.btWXMES.Name = "btWXMES";
            this.btWXMES.SubItems.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.btAutoReadWXReport,
            this.btDataVal,
            this.btColorBoxPrint,
            this.btWXFenPei,
            this.btWXMESDataMerge,
            this.btAddZPLSeripts,
            this.btAutoReadILookLog,
            this.PCDataVal});
            this.btWXMES.Text = "WXMES";
            // 
            // btAutoReadWXReport
            // 
            this.btAutoReadWXReport.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btAutoReadWXReport.ImagePaddingHorizontal = 8;
            this.btAutoReadWXReport.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btAutoReadWXReport.Name = "btAutoReadWXReport";
            this.btAutoReadWXReport.Text = "读取TGMES维修报表";
            this.btAutoReadWXReport.Click += new System.EventHandler(this.btAutoReadWXReport_Click);
            // 
            // btDataVal
            // 
            this.btDataVal.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btDataVal.ImagePaddingHorizontal = 8;
            this.btDataVal.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btDataVal.Name = "btDataVal";
            this.btDataVal.Text = "维修数据验证";
            this.btDataVal.Click += new System.EventHandler(this.btDataVal_Click);
            // 
            // btColorBoxPrint
            // 
            this.btColorBoxPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btColorBoxPrint.ImagePaddingHorizontal = 8;
            this.btColorBoxPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btColorBoxPrint.Name = "btColorBoxPrint";
            this.btColorBoxPrint.Text = "彩盒标打印";
            this.btColorBoxPrint.Click += new System.EventHandler(this.btColorBoxPrint_Click);
            // 
            // btWXFenPei
            // 
            this.btWXFenPei.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btWXFenPei.ImagePaddingHorizontal = 8;
            this.btWXFenPei.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btWXFenPei.Name = "btWXFenPei";
            this.btWXFenPei.Text = "维修分配";
            this.btWXFenPei.Click += new System.EventHandler(this.btWXFenPei_Click);
            // 
            // btWXMESDataMerge
            // 
            this.btWXMESDataMerge.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btWXMESDataMerge.ImagePaddingHorizontal = 8;
            this.btWXMESDataMerge.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btWXMESDataMerge.Name = "btWXMESDataMerge";
            this.btWXMESDataMerge.Text = "WXMES数据合并";
            this.btWXMESDataMerge.Click += new System.EventHandler(this.btWXMESDataMerge_Click);
            // 
            // btAddZPLSeripts
            // 
            this.btAddZPLSeripts.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btAddZPLSeripts.ImagePaddingHorizontal = 8;
            this.btAddZPLSeripts.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btAddZPLSeripts.Name = "btAddZPLSeripts";
            this.btAddZPLSeripts.Text = "添加ZPL脚本";
            this.btAddZPLSeripts.Click += new System.EventHandler(this.btAddZPLSeripts_Click);
            // 
            // btAutoReadILookLog
            // 
            this.btAutoReadILookLog.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btAutoReadILookLog.ImagePaddingHorizontal = 8;
            this.btAutoReadILookLog.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btAutoReadILookLog.Name = "btAutoReadILookLog";
            this.btAutoReadILookLog.Text = "读取ILOOK串口日志";
            this.btAutoReadILookLog.Click += new System.EventHandler(this.btAutoReadILookLog_Click);
            // 
            // PCDataVal
            // 
            this.PCDataVal.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.PCDataVal.ImagePaddingHorizontal = 8;
            this.PCDataVal.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.PCDataVal.Name = "PCDataVal";
            this.PCDataVal.Text = "PC数据验证";
            this.PCDataVal.Click += new System.EventHandler(this.PCDataVal_Click);
            // 
            // menuStrip
            // 
            this.menuStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.项目PToolStripMenuItem});
            this.menuStrip.Location = new System.Drawing.Point(3, 3);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Padding = new System.Windows.Forms.Padding(4, 1, 0, 1);
            this.menuStrip.Size = new System.Drawing.Size(252, 21);
            this.menuStrip.TabIndex = 1;
            this.menuStrip.Text = "MenuStrip";
            this.menuStrip.Visible = false;
            // 
            // 项目PToolStripMenuItem
            // 
            this.项目PToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.知识库ZToolStripMenuItem});
            this.项目PToolStripMenuItem.Name = "项目PToolStripMenuItem";
            this.项目PToolStripMenuItem.Size = new System.Drawing.Size(59, 19);
            this.项目PToolStripMenuItem.Text = "项目(&P)";
            // 
            // 知识库ZToolStripMenuItem
            // 
            this.知识库ZToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ts_oldweknow,
            this.ts_new_weknow});
            this.知识库ZToolStripMenuItem.Name = "知识库ZToolStripMenuItem";
            this.知识库ZToolStripMenuItem.Size = new System.Drawing.Size(131, 22);
            this.知识库ZToolStripMenuItem.Text = "知识库 (&Z)";
            // 
            // ts_oldweknow
            // 
            this.ts_oldweknow.Checked = true;
            this.ts_oldweknow.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ts_oldweknow.Name = "ts_oldweknow";
            this.ts_oldweknow.Size = new System.Drawing.Size(150, 22);
            this.ts_oldweknow.Text = " 旧知识库 (&O)";
            // 
            // ts_new_weknow
            // 
            this.ts_new_weknow.Name = "ts_new_weknow";
            this.ts_new_weknow.Size = new System.Drawing.Size(150, 22);
            this.ts_new_weknow.Text = "Weknow (&N)";
            this.ts_new_weknow.Click += new System.EventHandler(this.ts_new_weknow_Click);
            // 
            // tab_BW
            // 
            this.tab_BW.Controls.Add(this.Bar_BW);
            this.tab_BW.Location = new System.Drawing.Point(4, 4);
            this.tab_BW.Name = "tab_BW";
            this.tab_BW.Size = new System.Drawing.Size(256, 485);
            this.tab_BW.TabIndex = 2;
            this.tab_BW.Text = "百沃应用";
            this.tab_BW.ToolTipText = "百沃应用";
            this.tab_BW.UseVisualStyleBackColor = true;
            // 
            // Bar_BW
            // 
            this.Bar_BW.AccessibleRole = System.Windows.Forms.AccessibleRole.ToolBar;
            this.Bar_BW.BorderStyle = DevComponents.DotNetBar.eBorderType.None;
            this.Bar_BW.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Bar_BW.ExpandedPanel = this.btPrintTool;
            this.Bar_BW.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.World);
            this.Bar_BW.Location = new System.Drawing.Point(0, 0);
            this.Bar_BW.Name = "Bar_BW";
            this.Bar_BW.Panels.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.btPrintTool,
            this.btClientPlugins,
            this.sideBarPanelItem1});
            this.Bar_BW.Size = new System.Drawing.Size(256, 485);
            this.Bar_BW.Style = DevComponents.DotNetBar.eDotNetBarStyle.Office2007;
            this.Bar_BW.TabIndex = 1;
            this.Bar_BW.Text = "BarHW";
            // 
            // btPrintTool
            // 
            this.btPrintTool.FontBold = true;
            this.btPrintTool.Name = "btPrintTool";
            this.btPrintTool.SubItems.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.bt_ZPLPublicPrint,
            this.bt_MingPaiPrint,
            this.bt_ChuHuoPrint,
            this.bt_BoxNoPrint,
            this.bt_WXPsidWorkPrint,
            this.bt_GZChuHuoPrint,
            this.bt_CheckReportPrint,
            this.bt_CMESWorkPrint});
            this.btPrintTool.Text = "打印区域";
            // 
            // bt_ZPLPublicPrint
            // 
            this.bt_ZPLPublicPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_ZPLPublicPrint.ImagePaddingHorizontal = 8;
            this.bt_ZPLPublicPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_ZPLPublicPrint.Name = "bt_ZPLPublicPrint";
            this.bt_ZPLPublicPrint.Text = "斑马打印机通用打印";
            this.bt_ZPLPublicPrint.Click += new System.EventHandler(this.bt_ZPLPublicPrint_Click);
            // 
            // bt_MingPaiPrint
            // 
            this.bt_MingPaiPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_MingPaiPrint.ImagePaddingHorizontal = 8;
            this.bt_MingPaiPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_MingPaiPrint.Name = "bt_MingPaiPrint";
            this.bt_MingPaiPrint.Text = "铭牌标签打印";
            this.bt_MingPaiPrint.Click += new System.EventHandler(this.bt_MingPaiPrint_Click);
            // 
            // bt_ChuHuoPrint
            // 
            this.bt_ChuHuoPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_ChuHuoPrint.ImagePaddingHorizontal = 8;
            this.bt_ChuHuoPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_ChuHuoPrint.Name = "bt_ChuHuoPrint";
            this.bt_ChuHuoPrint.Text = "维修出货盒标";
            this.bt_ChuHuoPrint.Click += new System.EventHandler(this.bt_ChuHuoPrint_Click);
            // 
            // bt_BoxNoPrint
            // 
            this.bt_BoxNoPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_BoxNoPrint.ImagePaddingHorizontal = 8;
            this.bt_BoxNoPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_BoxNoPrint.Name = "bt_BoxNoPrint";
            this.bt_BoxNoPrint.Text = "中箱号打印";
            this.bt_BoxNoPrint.Click += new System.EventHandler(this.bt_BoxNoPrint_Click);
            // 
            // bt_WXPsidWorkPrint
            // 
            this.bt_WXPsidWorkPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_WXPsidWorkPrint.ImagePaddingHorizontal = 8;
            this.bt_WXPsidWorkPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_WXPsidWorkPrint.Name = "bt_WXPsidWorkPrint";
            this.bt_WXPsidWorkPrint.Text = "维修工单打印";
            this.bt_WXPsidWorkPrint.Click += new System.EventHandler(this.bt_WXPsidWorkPrint_Click);
            // 
            // bt_GZChuHuoPrint
            // 
            this.bt_GZChuHuoPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_GZChuHuoPrint.ImagePaddingHorizontal = 8;
            this.bt_GZChuHuoPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_GZChuHuoPrint.Name = "bt_GZChuHuoPrint";
            this.bt_GZChuHuoPrint.Text = "改制出货盒标";
            this.bt_GZChuHuoPrint.Click += new System.EventHandler(this.bt_GZChuHuoPrint_Click);
            // 
            // bt_CheckReportPrint
            // 
            this.bt_CheckReportPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_CheckReportPrint.ImagePaddingHorizontal = 8;
            this.bt_CheckReportPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_CheckReportPrint.Name = "bt_CheckReportPrint";
            this.bt_CheckReportPrint.Text = "改制检测报告打印";
            this.bt_CheckReportPrint.Click += new System.EventHandler(this.bt_CheckReportPrint_Click);
            // 
            // bt_CMESWorkPrint
            // 
            this.bt_CMESWorkPrint.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.bt_CMESWorkPrint.ImagePaddingHorizontal = 8;
            this.bt_CMESWorkPrint.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.bt_CMESWorkPrint.Name = "bt_CMESWorkPrint";
            this.bt_CMESWorkPrint.Text = "客户机工单打印";
            this.bt_CMESWorkPrint.Click += new System.EventHandler(this.bt_CMESWorkPrint_Click);
            // 
            // btClientPlugins
            // 
            this.btClientPlugins.FontBold = true;
            this.btClientPlugins.Name = "btClientPlugins";
            this.btClientPlugins.SubItems.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.btHHTPlugins});
            this.btClientPlugins.Text = "客户工具";
            // 
            // btHHTPlugins
            // 
            this.btHHTPlugins.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.btHHTPlugins.ImagePaddingHorizontal = 8;
            this.btHHTPlugins.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.btHHTPlugins.Name = "btHHTPlugins";
            this.btHHTPlugins.Text = "火火免";
            this.btHHTPlugins.Click += new System.EventHandler(this.btHHTPlugins_Click);
            // 
            // sideBarPanelItem1
            // 
            this.sideBarPanelItem1.FontBold = true;
            this.sideBarPanelItem1.Name = "sideBarPanelItem1";
            this.sideBarPanelItem1.SubItems.AddRange(new DevComponents.DotNetBar.BaseItem[] {
            this.buttonItem2});
            this.sideBarPanelItem1.Text = "工具小助手";
            // 
            // buttonItem2
            // 
            this.buttonItem2.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem2.ImagePaddingHorizontal = 8;
            this.buttonItem2.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem2.Name = "buttonItem2";
            this.buttonItem2.Text = "分类小助力手";
            this.buttonItem2.Click += new System.EventHandler(this.buttonItem2_Click);
            // 
            // buttonItem6
            // 
            this.buttonItem6.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem6.ImagePaddingHorizontal = 8;
            this.buttonItem6.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem6.Name = "buttonItem6";
            this.buttonItem6.Text = "供应链CSPM01";
            this.buttonItem6.Tooltip = " cnh0029m00";
            // 
            // buttonItem7
            // 
            this.buttonItem7.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem7.ImagePaddingHorizontal = 8;
            this.buttonItem7.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem7.Name = "buttonItem7";
            this.buttonItem7.Text = "服务CSPM02";
            this.buttonItem7.Tooltip = "cnh0001s0e";
            // 
            // buttonItem8
            // 
            this.buttonItem8.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem8.ImagePaddingHorizontal = 8;
            this.buttonItem8.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem8.Name = "buttonItem8";
            this.buttonItem8.Text = "出货提单CSPM03";
            this.buttonItem8.Tooltip = "cnh0005s03";
            // 
            // buttonItem9
            // 
            this.buttonItem9.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem9.ImagePaddingHorizontal = 8;
            this.buttonItem9.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem9.Name = "buttonItem9";
            this.buttonItem9.Text = "申请调拨CMES";
            this.buttonItem9.Tooltip = "bwwx009";
            // 
            // buttonItem10
            // 
            this.buttonItem10.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem10.ImagePaddingHorizontal = 8;
            this.buttonItem10.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem10.Name = "buttonItem10";
            this.buttonItem10.Text = "国内改制CSPM05";
            this.buttonItem10.Tooltip = "cnh0009m00";
            // 
            // buttonItem11
            // 
            this.buttonItem11.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem11.ImagePaddingHorizontal = 8;
            this.buttonItem11.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem11.Name = "buttonItem11";
            this.buttonItem11.Text = "海外改制CSPM06";
            this.buttonItem11.Tooltip = "cnh0016m00";
            // 
            // buttonItem12
            // 
            this.buttonItem12.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem12.ImagePaddingHorizontal = 8;
            this.buttonItem12.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem12.Name = "buttonItem12";
            this.buttonItem12.Text = "摄像头业务CSPM07";
            this.buttonItem12.Tooltip = "cnh0018m00";
            // 
            // buttonItem13
            // 
            this.buttonItem13.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem13.ImagePaddingHorizontal = 8;
            this.buttonItem13.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem13.Name = "buttonItem13";
            this.buttonItem13.Text = "客户机CSPM08";
            this.buttonItem13.Tooltip = "CNH0050M00";
            // 
            // buttonItem14
            // 
            this.buttonItem14.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem14.ImagePaddingHorizontal = 8;
            this.buttonItem14.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem14.Name = "buttonItem14";
            this.buttonItem14.Text = "研发CSPM09";
            this.buttonItem14.Tooltip = "CNH9954m04";
            // 
            // buttonItem15
            // 
            this.buttonItem15.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem15.ImagePaddingHorizontal = 8;
            this.buttonItem15.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem15.Name = "buttonItem15";
            this.buttonItem15.Text = "研发字库CSPM10";
            this.buttonItem15.Tooltip = "C05996E0003";
            // 
            // buttonItem16
            // 
            this.buttonItem16.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem16.ImagePaddingHorizontal = 8;
            this.buttonItem16.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem16.Name = "buttonItem16";
            this.buttonItem16.Text = "维修数据文件TGMES01";
            this.buttonItem16.Tooltip = "BWWX2015";
            // 
            // buttonItem17
            // 
            this.buttonItem17.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem17.ImagePaddingHorizontal = 8;
            this.buttonItem17.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem17.Name = "buttonItem17";
            this.buttonItem17.Text = "排产与接收TGMES02";
            this.buttonItem17.Tooltip = "bwwanghengheng";
            // 
            // buttonItem18
            // 
            this.buttonItem18.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem18.ImagePaddingHorizontal = 8;
            this.buttonItem18.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem18.Name = "buttonItem18";
            this.buttonItem18.Text = "改制数据文件TGMES03";
            this.buttonItem18.Tooltip = "bwgz2018";
            // 
            // buttonItem19
            // 
            this.buttonItem19.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem19.ImagePaddingHorizontal = 8;
            this.buttonItem19.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem19.Name = "buttonItem19";
            this.buttonItem19.Text = "改制OPC-TGMES04";
            this.buttonItem19.Tooltip = "baiwotgmes";
            // 
            // buttonItem20
            // 
            this.buttonItem20.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem20.ImagePaddingHorizontal = 8;
            this.buttonItem20.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem20.Name = "buttonItem20";
            this.buttonItem20.Text = "改制PLUS-TGMES05";
            this.buttonItem20.Tooltip = "baiwotgmes";
            // 
            // buttonItem21
            // 
            this.buttonItem21.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem21.ImagePaddingHorizontal = 8;
            this.buttonItem21.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem21.Name = "buttonItem21";
            this.buttonItem21.Text = "维修OPC-TGMES06";
            this.buttonItem21.Tooltip = "h_baiwo";
            // 
            // buttonItem22
            // 
            this.buttonItem22.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem22.ImagePaddingHorizontal = 8;
            this.buttonItem22.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem22.Name = "buttonItem22";
            this.buttonItem22.Text = "维修PLUS-TGMES07";
            this.buttonItem22.Tooltip = "h_baiwo";
            // 
            // buttonItem23
            // 
            this.buttonItem23.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem23.ImagePaddingHorizontal = 8;
            this.buttonItem23.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem23.Name = "buttonItem23";
            this.buttonItem23.Text = "接收CCP01";
            this.buttonItem23.Tooltip = "c05996e0003";
            // 
            // buttonItem24
            // 
            this.buttonItem24.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem24.ImagePaddingHorizontal = 8;
            this.buttonItem24.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem24.Name = "buttonItem24";
            this.buttonItem24.Text = "工程师CCP02";
            this.buttonItem24.Tooltip = "C05996E0004";
            // 
            // buttonItem1
            // 
            this.buttonItem1.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.buttonItem1.ImagePaddingHorizontal = 8;
            this.buttonItem1.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.buttonItem1.Name = "buttonItem1";
            this.buttonItem1.Text = "GDP";
            this.buttonItem1.Tooltip = "C05996";
            // 
            // CSPM03
            // 
            this.CSPM03.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CSPM03.ImagePaddingHorizontal = 8;
            this.CSPM03.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CSPM03.Name = "CSPM03";
            this.CSPM03.Text = "CSPM03-进境维修仓库";
            this.CSPM03.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // CSPM04
            // 
            this.CSPM04.ButtonStyle = DevComponents.DotNetBar.eButtonStyle.ImageAndText;
            this.CSPM04.ImagePaddingHorizontal = 8;
            this.CSPM04.ImagePosition = DevComponents.DotNetBar.eImagePosition.Top;
            this.CSPM04.Name = "CSPM04";
            this.CSPM04.Text = "CSPM04-进境维修高维";
            this.CSPM04.Click += new System.EventHandler(this.HonorPublicLogin_Click);
            // 
            // MDI
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(264, 653);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.Top_panel);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimumSize = new System.Drawing.Size(278, 631);
            this.Name = "MDI";
            this.Text = "Form1";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MDI_FormClosing);
            this.Top_panel.ResumeLayout(false);
            this.Top_panel.PerformLayout();
            this.Logo_panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LogoMe)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.tab_HW.ResumeLayout(false);
            this.tab_HW.PerformLayout();
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.tab_BW.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel Top_panel;
        private System.Windows.Forms.Label MeBrie_label;
        public System.Windows.Forms.Label Me_Name;
        private System.Windows.Forms.PictureBox LogoMe;
        private System.Windows.Forms.Panel Logo_panel;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tab_HW;
        private System.Windows.Forms.TabPage tab_BW;
        private DevComponents.DotNetBar.SideBar Bar_HW;
        private DevComponents.DotNetBar.SideBarPanelItem HonorApp;
        private System.Windows.Forms.MenuStrip menuStrip;
        private System.Windows.Forms.ToolStripMenuItem 项目PToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 知识库ZToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ts_oldweknow;
        private System.Windows.Forms.ToolStripMenuItem ts_new_weknow;
        private DevComponents.DotNetBar.ButtonItem buttonItem6;
        private DevComponents.DotNetBar.ButtonItem buttonItem7;
        private DevComponents.DotNetBar.ButtonItem buttonItem8;
        private DevComponents.DotNetBar.ButtonItem buttonItem9;
        private DevComponents.DotNetBar.ButtonItem buttonItem10;
        private DevComponents.DotNetBar.ButtonItem buttonItem11;
        private DevComponents.DotNetBar.ButtonItem buttonItem12;
        private DevComponents.DotNetBar.ButtonItem buttonItem13;
        private DevComponents.DotNetBar.ButtonItem buttonItem14;
        private DevComponents.DotNetBar.ButtonItem buttonItem15;
        private DevComponents.DotNetBar.ButtonItem buttonItem16;
        private DevComponents.DotNetBar.ButtonItem buttonItem17;
        private DevComponents.DotNetBar.ButtonItem buttonItem18;
        private DevComponents.DotNetBar.ButtonItem buttonItem19;
        private DevComponents.DotNetBar.ButtonItem buttonItem20;
        private DevComponents.DotNetBar.ButtonItem buttonItem21;
        private DevComponents.DotNetBar.ButtonItem buttonItem22;
        private DevComponents.DotNetBar.ButtonItem buttonItem23;
        private DevComponents.DotNetBar.ButtonItem buttonItem24;
        private DevComponents.DotNetBar.ButtonItem buttonItem1;
        private DevComponents.DotNetBar.SideBar Bar_BW;
        private DevComponents.DotNetBar.SideBarPanelItem btPrintTool;
        private DevComponents.DotNetBar.ButtonItem bt_MingPaiPrint;
        private DevComponents.DotNetBar.ButtonItem bt_ZPLPublicPrint;
        private DevComponents.DotNetBar.SideBarPanelItem btClientPlugins;
        private DevComponents.DotNetBar.ButtonItem btHHTPlugins;
        private DevComponents.DotNetBar.ButtonItem bt_ChuHuoPrint;
        private DevComponents.DotNetBar.ButtonItem bt_BoxNoPrint;
        private DevComponents.DotNetBar.ButtonItem bt_WXPsidWorkPrint;
        private DevComponents.DotNetBar.ButtonItem bt_CMESWorkPrint;
        private DevComponents.DotNetBar.ButtonItem bt_GZChuHuoPrint;
        private DevComponents.DotNetBar.ButtonItem bt_Compartner1;
        private DevComponents.DotNetBar.ButtonItem bt_KnowledgeLibrary1;
        private DevComponents.DotNetBar.ButtonItem bt_CheckReportPrint;
        private DevComponents.DotNetBar.ButtonItem CCP01;
        private DevComponents.DotNetBar.ButtonItem CCP02;
        private DevComponents.DotNetBar.ButtonItem TGMES01;
        private DevComponents.DotNetBar.ButtonItem TGMES02;
        private DevComponents.DotNetBar.ButtonItem TGMES03;
        private DevComponents.DotNetBar.ButtonItem CSPM01;
        private DevComponents.DotNetBar.ButtonItem CSPM02;
        private DevComponents.DotNetBar.SideBarPanelItem sideBarPanelItem1;
        private DevComponents.DotNetBar.ButtonItem buttonItem2;
        private DevComponents.DotNetBar.SideBarPanelItem btWXMES;
        private DevComponents.DotNetBar.ButtonItem btDataVal;
        private DevComponents.DotNetBar.ButtonItem PCDataVal;
        private DevComponents.DotNetBar.ButtonItem btColorBoxPrint;
        private DevComponents.DotNetBar.ButtonItem btWXFenPei;
        private DevComponents.DotNetBar.ButtonItem btWXMESDataMerge;
        private DevComponents.DotNetBar.ButtonItem btAddZPLSeripts;
        private DevComponents.DotNetBar.ButtonItem webCompartner;
        private DevComponents.DotNetBar.ButtonItem btAutoReadILookLog;
        private DevComponents.DotNetBar.ButtonItem btAutoReadWXReport;
        private DevComponents.DotNetBar.ButtonItem CCP03;
        private DevComponents.DotNetBar.ButtonItem CCP04;
        private DevComponents.DotNetBar.ButtonItem bt_Compartner2;
        private DevComponents.DotNetBar.ButtonItem bt_KnowledgeLibrary2;
        private DevComponents.DotNetBar.ButtonItem CSPM03;
        private DevComponents.DotNetBar.ButtonItem CSPM04;
    }
}


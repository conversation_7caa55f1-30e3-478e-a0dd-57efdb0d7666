﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor
{
    public partial class ProgressForm : Form
    {
        private ProgressBar progressBar;
        private Label statusLabel;
        private bool _canClose = false;

        public ProgressForm(string title)
        {
            this.Text = title;
            this.Size = new Size(400, 100);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            progressBar = new ProgressBar
            {
                Location = new Point(10, 10),
                Size = new Size(365, 23),
                Style = ProgressBarStyle.Continuous
            };

            statusLabel = new Label
            {
                Location = new Point(10, 40),
                Size = new Size(365, 23),
                TextAlign = ContentAlignment.MiddleCenter
            };

            this.Controls.AddRange(new Control[] { progressBar, statusLabel });
        }

        public void UpdateProgress(int percentage)
        {
            if (this.IsDisposed || !this.IsHandleCreated)
                return;

            progressBar.Value = Math.Max(0, Math.Min(100, percentage));
            statusLabel.Text = $"下载进度: {percentage}%";
            
            progressBar.Refresh();
            statusLabel.Refresh();
            this.Refresh();
            
            Application.DoEvents();
        }

        public void AllowClose()
        {
            _canClose = true;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (!_canClose)
            {
                e.Cancel = true;
            }
            base.OnFormClosing(e);
        }

        protected override CreateParams CreateParams
        {
            get
            {
                var cp = base.CreateParams;
                cp.ClassStyle |= 0x200; // 禁用关闭按钮
                return cp;
            }
        }
    }
}

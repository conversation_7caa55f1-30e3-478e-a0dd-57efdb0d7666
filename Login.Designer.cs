﻿namespace EasyWork.Honor
{
    partial class Login
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Login));
            this.lbpcname = new DevComponents.DotNetBar.LabelX();
            this.lbmac = new DevComponents.DotNetBar.LabelX();
            this.labelX4 = new DevComponents.DotNetBar.LabelX();
            this.lbip = new DevComponents.DotNetBar.LabelX();
            this.labelX3 = new DevComponents.DotNetBar.LabelX();
            this.Version = new DevComponents.DotNetBar.LabelX();
            this.tbexit = new DevComponents.DotNetBar.ButtonX();
            this.btlogin = new DevComponents.DotNetBar.ButtonX();
            this.tbpwd = new DevComponents.DotNetBar.Controls.TextBoxX();
            this.tbuser = new DevComponents.DotNetBar.Controls.TextBoxX();
            this.labelX2 = new DevComponents.DotNetBar.LabelX();
            this.labelX1 = new DevComponents.DotNetBar.LabelX();
            this.lbwinver = new DevComponents.DotNetBar.LabelX();
            this.lbcpu = new DevComponents.DotNetBar.LabelX();
            this.lbhdd = new DevComponents.DotNetBar.LabelX();
            this.lbmemory = new DevComponents.DotNetBar.LabelX();
            this.SuspendLayout();
            // 
            // lbpcname
            // 
            this.lbpcname.AutoSize = true;
            this.lbpcname.Location = new System.Drawing.Point(14, 246);
            this.lbpcname.Name = "lbpcname";
            this.lbpcname.Size = new System.Drawing.Size(50, 14);
            this.lbpcname.TabIndex = 23;
            this.lbpcname.Text = "PCName:";
            // 
            // lbmac
            // 
            this.lbmac.AutoSize = true;
            this.lbmac.Location = new System.Drawing.Point(309, 246);
            this.lbmac.Name = "lbmac";
            this.lbmac.Size = new System.Drawing.Size(31, 14);
            this.lbmac.TabIndex = 22;
            this.lbmac.Text = "MAC:";
            // 
            // labelX4
            // 
            this.labelX4.AutoSize = true;
            this.labelX4.Font = new System.Drawing.Font("Microsoft YaHei UI", 20.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelX4.Location = new System.Drawing.Point(118, 57);
            this.labelX4.Name = "labelX4";
            this.labelX4.Size = new System.Drawing.Size(233, 38);
            this.labelX4.TabIndex = 21;
            this.labelX4.Text = "EasyWork.Honor";
            // 
            // lbip
            // 
            this.lbip.AutoSize = true;
            this.lbip.Location = new System.Drawing.Point(309, 224);
            this.lbip.Name = "lbip";
            this.lbip.Size = new System.Drawing.Size(25, 14);
            this.lbip.TabIndex = 20;
            this.lbip.Text = "IP:";
            // 
            // labelX3
            // 
            this.labelX3.AutoSize = true;
            this.labelX3.Font = new System.Drawing.Font("宋体", 28F);
            this.labelX3.Location = new System.Drawing.Point(4, 15);
            this.labelX3.Name = "labelX3";
            this.labelX3.Size = new System.Drawing.Size(476, 48);
            this.labelX3.TabIndex = 19;
            this.labelX3.Text = "深圳百沃彰世科技有限公司";
            // 
            // Version
            // 
            this.Version.AutoSize = true;
            this.Version.Location = new System.Drawing.Point(309, 205);
            this.Version.Name = "Version";
            this.Version.Size = new System.Drawing.Size(62, 16);
            this.Version.TabIndex = 18;
            this.Version.Text = "软件版本:";
            // 
            // tbexit
            // 
            this.tbexit.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton;
            this.tbexit.Location = new System.Drawing.Point(355, 116);
            this.tbexit.Name = "tbexit";
            this.tbexit.Size = new System.Drawing.Size(58, 51);
            this.tbexit.TabIndex = 17;
            this.tbexit.Text = "退  出";
            this.tbexit.Click += new System.EventHandler(this.Tbexit_Click);
            // 
            // btlogin
            // 
            this.btlogin.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton;
            this.btlogin.Location = new System.Drawing.Point(282, 116);
            this.btlogin.Name = "btlogin";
            this.btlogin.Size = new System.Drawing.Size(58, 51);
            this.btlogin.TabIndex = 16;
            this.btlogin.Text = "登  陆";
            this.btlogin.Click += new System.EventHandler(this.Btlogin_Click);
            // 
            // tbpwd
            // 
            // 
            // 
            // 
            this.tbpwd.Border.Class = "TextBoxBorder";
            this.tbpwd.Location = new System.Drawing.Point(123, 150);
            this.tbpwd.Name = "tbpwd";
            this.tbpwd.PasswordChar = '*';
            this.tbpwd.Size = new System.Drawing.Size(150, 21);
            this.tbpwd.TabIndex = 15;
            // 
            // tbuser
            // 
            // 
            // 
            // 
            this.tbuser.Border.Class = "TextBoxBorder";
            this.tbuser.Location = new System.Drawing.Point(123, 110);
            this.tbuser.Name = "tbuser";
            this.tbuser.Size = new System.Drawing.Size(150, 21);
            this.tbuser.TabIndex = 14;
            // 
            // labelX2
            // 
            this.labelX2.AutoSize = true;
            this.labelX2.Location = new System.Drawing.Point(86, 155);
            this.labelX2.Name = "labelX2";
            this.labelX2.Size = new System.Drawing.Size(37, 16);
            this.labelX2.TabIndex = 13;
            this.labelX2.Text = "密码:";
            // 
            // labelX1
            // 
            this.labelX1.AutoSize = true;
            this.labelX1.Location = new System.Drawing.Point(72, 114);
            this.labelX1.Name = "labelX1";
            this.labelX1.Size = new System.Drawing.Size(50, 16);
            this.labelX1.TabIndex = 12;
            this.labelX1.Text = "登陆名:";
            // 
            // lbwinver
            // 
            this.lbwinver.AutoSize = true;
            this.lbwinver.Location = new System.Drawing.Point(14, 224);
            this.lbwinver.Name = "lbwinver";
            this.lbwinver.Size = new System.Drawing.Size(62, 16);
            this.lbwinver.TabIndex = 24;
            this.lbwinver.Text = "系统版本:";
            // 
            // lbcpu
            // 
            this.lbcpu.AutoSize = true;
            this.lbcpu.Location = new System.Drawing.Point(14, 205);
            this.lbcpu.Name = "lbcpu";
            this.lbcpu.Size = new System.Drawing.Size(31, 14);
            this.lbcpu.TabIndex = 25;
            this.lbcpu.Text = "CPU:";
            // 
            // lbhdd
            // 
            this.lbhdd.AutoSize = true;
            this.lbhdd.Location = new System.Drawing.Point(14, 184);
            this.lbhdd.Name = "lbhdd";
            this.lbhdd.Size = new System.Drawing.Size(37, 16);
            this.lbhdd.TabIndex = 26;
            this.lbhdd.Text = "硬盘:";
            // 
            // lbmemory
            // 
            this.lbmemory.AutoSize = true;
            this.lbmemory.Location = new System.Drawing.Point(308, 184);
            this.lbmemory.Name = "lbmemory";
            this.lbmemory.Size = new System.Drawing.Size(31, 14);
            this.lbmemory.TabIndex = 27;
            this.lbmemory.Text = "CPU:";
            // 
            // Login
            // 
            this.AcceptButton = this.btlogin;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(464, 278);
            this.Controls.Add(this.lbmemory);
            this.Controls.Add(this.lbhdd);
            this.Controls.Add(this.lbcpu);
            this.Controls.Add(this.lbwinver);
            this.Controls.Add(this.lbpcname);
            this.Controls.Add(this.lbmac);
            this.Controls.Add(this.labelX4);
            this.Controls.Add(this.lbip);
            this.Controls.Add(this.labelX3);
            this.Controls.Add(this.Version);
            this.Controls.Add(this.tbexit);
            this.Controls.Add(this.btlogin);
            this.Controls.Add(this.tbpwd);
            this.Controls.Add(this.tbuser);
            this.Controls.Add(this.labelX2);
            this.Controls.Add(this.labelX1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(480, 317);
            this.MinimumSize = new System.Drawing.Size(480, 317);
            this.Name = "Login";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "深圳百沃彰世科技有限公司-----一起荣耀";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Login_FormClosing);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevComponents.DotNetBar.LabelX lbpcname;
        private DevComponents.DotNetBar.LabelX lbmac;
        private DevComponents.DotNetBar.LabelX labelX4;
        private DevComponents.DotNetBar.LabelX lbip;
        private DevComponents.DotNetBar.LabelX labelX3;
        private DevComponents.DotNetBar.LabelX Version;
        private DevComponents.DotNetBar.ButtonX tbexit;
        private DevComponents.DotNetBar.ButtonX btlogin;
        private DevComponents.DotNetBar.Controls.TextBoxX tbpwd;
        private DevComponents.DotNetBar.Controls.TextBoxX tbuser;
        private DevComponents.DotNetBar.LabelX labelX2;
        private DevComponents.DotNetBar.LabelX labelX1;
        private DevComponents.DotNetBar.LabelX lbwinver;
        private DevComponents.DotNetBar.LabelX lbcpu;
        private DevComponents.DotNetBar.LabelX lbhdd;
        private DevComponents.DotNetBar.LabelX lbmemory;
    }
}
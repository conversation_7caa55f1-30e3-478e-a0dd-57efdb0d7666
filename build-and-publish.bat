@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo [INFO] Starting complete build and publish process...

echo [PHASE1] Building project and generating packages...
call build.bat
if errorlevel 1 (
    echo [ERROR] Build phase failed
    goto :end
)

echo.
echo [PHASE2] Publishing to NAS...
call publish.bat
if errorlevel 1 (
    echo [ERROR] Publish phase failed
    goto :end
)

echo [SUCCESS] Complete build and publish process finished!

:end
echo Press any key to exit...
pause >nul

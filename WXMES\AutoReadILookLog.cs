﻿using EasyWork.Honor.Service;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class AutoReadILookLog : Form
    {
        const int buffer_size = 25600;
        const int button_size = 102400;
        StringBuilder buffer = new StringBuilder(buffer_size);
        StringBuilder but_ton = new StringBuilder(button_size);
        private Thread th;
        private delegate void delInfoList(string text);//申明委托，防止不同线程设置richtextbox时出现错误
        public AutoReadILookLog()
        {
            InitializeComponent();
            th = new Thread(readdata);
            th.Start();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (!th.IsAlive)
            {
                th = new Thread(readdata);
                th.Start();
            }
            else
            {
                SetrichTextBox("正在读取中...请勿重复点击...." + DateTime.Now + "\n\n");
            }
        }

        void readdata()
        {
            SetrichTextBox("ILOOK监控软件已打开..........." + DateTime.Now + "\n\n");
            string bsn = "";
            while (true)
            {
                bsn = "";
                IntPtr maindHwnd = WinAPI.FindWindow(null, "iLook V200R003B230");//主页
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第二层
                IntPtr childHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第三层第1个窗口
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, childHwnd, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第三层第2个窗口
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第四层
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第五层
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.SysTabControl32.app.0.16fed80_r7_ad1", null);//第六层
                childHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第七层第1个窗口
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, childHwnd, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第七层第2个窗口
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第八层
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第九层
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十层
                maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十一层--日志文本框与开始读取日志按钮共同层
                childHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十二层1窗口----日志文本框
                IntPtr Top = WinAPI.FindWindowEx(maindHwnd, childHwnd, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null); //第十二层2窗口----日志文本框

                //读取日志按钮
                IntPtr button1 = WinAPI.FindWindowEx(Top, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);
                Top = WinAPI.FindWindowEx(Top, button1, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);
                IntPtr button = WinAPI.FindWindowEx(Top, IntPtr.Zero, "WindowsForms10.BUTTON.app.0.16fed80_r7_ad1", null);//读取日志按钮
                Thread.Sleep(1000);
                WinAPI.SendMessage(button, WinAPI.WM_GETTEXT, button_size, but_ton);
                if (but_ton.ToString() == "停止读取并解析日志")
                {
                    SetrichTextBox("ILOOK正准备读取串口数据..........." + DateTime.Now + "\n\n");
                    IntPtr edit = WinAPI.FindWindowEx(Top, IntPtr.Zero, "WindowsForms10.EDIT.app.0.16fed80_r7_ad1", null);//读取BSN信息
                    WinAPI.SendMessage(edit, WinAPI.WM_GETTEXT, button_size, but_ton);
                    bsn = but_ton.ToString();
                    while (true)
                    {
                        button = WinAPI.FindWindowEx(Top, IntPtr.Zero, "WindowsForms10.BUTTON.app.0.16fed80_r7_ad1", null);
                        WinAPI.SendMessage(button, WinAPI.WM_GETTEXT, button_size, but_ton);
                        if (but_ton.ToString() == "开始读取日志")
                        {
                            ////日志文本框
                            maindHwnd = WinAPI.FindWindowEx(childHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十三层
                            maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十四层
                            maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.EDIT.app.0.16fed80_r7_ad1", null);//第十五层---日志文本框

                            //string path = @"d:\123.txt";
                            //string Stext = System.IO.File.ReadAllText(path);
                            //WinAPI.SendMessage(maindHwnd, WinAPI.WM_SETTEXT, (int)IntPtr.Zero, Stext);


                            WinAPI.SendMessage(maindHwnd, WinAPI.WM_GETTEXT, button_size, but_ton);
                            string str = but_ton.ToString().IndexOf("Exit EBS").ToString();
                            if (str != "-1")
                            {
                                SaveIlooklog(bsn, but_ton.ToString());
                                SetrichTextBox("ILOOK已读取完成串口数据..........." + DateTime.Now + "\n\n");
                                break;
                            }

                            SetrichTextBox("ILOOK读取数据失败或读取数据不完整..........." + DateTime.Now + "\n\n");
                            break;
                        }
                        Thread.Sleep(1000);

                    }
                }
            }
















            ////日志文本框

            //maindHwnd = WinAPI.FindWindowEx(childHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十三层
            //maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.Window.8.app.0.16fed80_r7_ad1", null);//第十四层
            //maindHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "WindowsForms10.EDIT.app.0.16fed80_r7_ad1", null);//第十五层---日志文本框

            //WinAPI.SendMessage(maindHwnd, WinAPI.WM_SETTEXT, (int)IntPtr.Zero, "123");



            // WinAPI.SendMessage(button, WinAPI.WM_GETTEXT, button_size, but_ton);
            //  MessageBox.Show(but_ton.ToString());
            // MessageBox.Show(button.ToString("X6"));
        }


        void SaveIlooklog(string veneercode,string record)
        {
            string sql = "insert into MES_Honor.dbo.ilook_log(veneercode,record,createtime,createbyid) values('" + veneercode+"','"+record+"',getdate(),'"+Login.usercode+"')";
            SqlHelper.ExecuteSql(sql);
        }

        void SetrichTextBox(string value)
        {
            if (rt1.InvokeRequired)
            {
                delInfoList d = new
                delInfoList(SetrichTextBox);
                rt1.Invoke(d, value);
            }
            else
            {

                rt1.Focus();
                rt1.Select(rt1.TextLength, 0);
                rt1.ScrollToCaret();
                rt1.AppendText(value);
            }
        }

        private void AutoReadILookLog_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.ExitThread();
            Environment.Exit(0);
        }

        private void AutoReadILookLog_FormClosing(object sender, FormClosingEventArgs e)
        {
            Application.ExitThread();
            Environment.Exit(0);
        }
    }
}

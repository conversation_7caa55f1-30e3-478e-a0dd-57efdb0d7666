<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Diagnostics.DiagnosticSource</name>
    </assembly>
    <members>
        <member name="T:System.Diagnostics.DiagnosticSource">
             <summary>
             This is the basic API to 'hook' parts of the framework.   It is like an EventSource
             (which can also write object), but is intended to log complex objects that can't be serialized.
            
             Please See the DiagnosticSource Users Guide
             https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/DiagnosticSourceUsersGuide.md
             for instructions on its use.
             </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.Write(System.String,System.Object)">
             <summary>
             Write is a generic way of logging complex payloads.  Each notification
             is given a name, which identifies it as well as a object (typically an anonymous type)
             that gives the information to pass to the notification, which is arbitrary.
            
             The name should be short (so don't use fully qualified names unless you have to
             to avoid ambiguity), but you want the name to be globally unique.  Typically your componentName.eventName
             where component<PERSON>ame and event<PERSON><PERSON> are strings less than 10 characters are a good compromise.
             notification names should NOT have '.' in them because component names have dots and for them both
             to have dots would lead to ambiguity.   The suggestion is to use _ instead.  It is assumed
             that listeners will use string prefixing to filter groups, thus having hierarchy in component
             names is good.
             </summary>
             <param name="name">The name of the event being written.</param>
             <param name="value">An object that represent the value being passed as a payload for the event.
             This is often an anonymous type which contains several sub-values.</param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String)">
            <summary>
            Optional: if there is expensive setup for the notification, you can call IsEnabled
            before doing this setup.   Consumers should not be assuming that they only get notifications
            for which IsEnabled is true however, it is optional for producers to call this API.
            The name should be the same as what is passed to Write.
            </summary>
            <param name="name">The name of the event being written.</param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String,System.Object,System.Object)">
            <summary>
            Optional: if there is expensive setup for the notification, you can call IsEnabled
            before doing this setup with context
            </summary>
            <param name="name">The name of the event being written.</param>
            <param name="arg1">An object that represents the additional context for IsEnabled.
            Consumers should expect to receive null which may indicate that producer called pure
            IsEnabled(string)  to check if consumer wants to get notifications for such events at all.
            Based on it, producer may call IsEnabled(string, object, object) again with non-null context </param>
            <param name="arg2">Optional. An object that represents the additional context for IsEnabled.
            Null by default. Consumers should expect to receive null which may indicate that producer
            called pure IsEnabled(string) or producer passed all necessary context in arg1</param>
            <seealso cref="M:System.Diagnostics.DiagnosticSource.IsEnabled(System.String)"/>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.StartActivity(System.Diagnostics.Activity,System.Object)">
             <summary>
             Starts an Activity and writes start event.
            
             Activity describes logical operation, its context and parent relation;
             Current activity flows through the operation processing.
            
             This method starts given Activity (maintains global Current Activity
             and Parent for the given activity) and notifies consumers  that new Activity
             was started. Consumers could access <see cref="P:System.Diagnostics.Activity.Current"/>
             to add context and/or augment telemetry.
            
             Producers may pass additional details to the consumer in the payload.
             </summary>
             <param name="activity">Activity to be started</param>
             <param name="args">An object that represent the value being passed as a payload for the event.</param>
             <returns>Started Activity for convenient chaining</returns>
             <seealso cref="T:System.Diagnostics.Activity"/>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.StopActivity(System.Diagnostics.Activity,System.Object)">
             <summary>
             Stops given Activity: maintains global Current Activity and notifies consumers
             that Activity was stopped. Consumers could access <see cref="P:System.Diagnostics.Activity.Current"/>
             to add context and/or augment telemetry.
            
             Producers may pass additional details to the consumer in the payload.
             </summary>
             <param name="activity">Activity to be stopped</param>
             <param name="args">An object that represent the value being passed as a payload for the event.</param>
             <seealso cref="T:System.Diagnostics.Activity"/>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.OnActivityImport(System.Diagnostics.Activity,System.Object)">
             <summary>
             Optional: If an instrumentation site creating an new activity that was caused
             by something outside the process (e.g. an incomming HTTP request), then that site
             will want to make a new activity and transfer state from that incoming request
             to the activity.   To the extent possible this should be done by the instrumentation
             site (because it is a contract between Activity and the incomming request logic
             at the instrumentation site.   However the instrumentation site can't handle policy
             (for example if sampling is done exactly which requests should be sampled) For this
             the instrumentation site needs to call back out to the logging system and ask it to
             resolve policy (e.g. decide if the Activity's 'sampling' bit should be set)  This
             is what OnActivityImport is for.   It is given the activity as well as a payload
             object that represents the incomming request.   The DiagnosticSource's subscribers
             then have the opportunity to update this activity as desired.
            
             Note that this callout is rarely used at instrumentation sites (only those sites
             that are on the 'boundry' of the process), and the instrumentation site will implement
             some default policy (it sets the activity in SOME way), and so this method does not
             need to be overriden if that default policy is fine.   Thus this is call should
             be used rare (but often important) cases.
            
             Note that the type of 'payload' is typed as object here, but for any
             particular instrumentation site and the subscriber will know the type of
             the payload and thus cast it and decode it if it needs to.
             </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSource.OnActivityExport(System.Diagnostics.Activity,System.Object)">
             <summary>
             Optional: If an instrumentation site is at a location where activities leave the
             process (e.g. an outgoing HTTP request), then that site will want to transfer state
             from the activity to the outgoing request.    To the extent possible this should be
             done by the instrumentation site (because it is a contract between Activity and the
             outgoing request logic at the instrumentation site.   However the instrumentation site
             can't handle policy (for example whether activity information should be disabled,
             or written in a older format for compatibility reasons).   For this
             the instrumentation site needs to call back out to the logging system and ask it to
             resolve policy.  This is what OnActivityExport is for.   It is given the activity as
             well as a payloay object that represents the outgoing request.   The DiagnosticSource's
             subscriber then have the ability to update the outgoing request before it is sent.
            
             Note that this callout is rarely used at instrumentation sites (only those sites
             that are on an outgoing 'boundry' of the process).   Moreover typically the default
             policy that the instrumentation site performs (transfer all activity state in a
             particular outgoing convention), is likely to be fine.   This is only for cases
             where that is a problem.  Thus this is call should be used very rarely and is
             mostly here for symetry with OnActivityImport and future-proofing.
            
             Note that the type of 'payload' is typed as object here, but for any
             particular instrumentation site and the subscriber should know the type of
             the payload and thus cast it and decode it if it needs to.
             </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticListener">
             <summary>
             A DiagnosticListener is something that forwards on events written with DiagnosticSource.
             It is an IObservable (has Subscribe method), and it also has a Subscribe overloads that
             lets you specify a 'IsEnabled' predicate that users of DiagnosticSource will use for
             'quick checks'.
            
             The item in the stream is a KeyValuePair[string, object] where the string is the name
             of the diagnostic item and the object is the payload (typically an anonymous type).
            
             There may be many DiagnosticListeners in the system, but we encourage the use of
             The DiagnosticSource.DefaultSource which goes to the DiagnosticListener.DefaultListener.
            
             If you need to see 'everything' you can subscribe to the 'AllListeners' event that
             will fire for every live DiagnosticListener in the appdomain (past or present).
            
             Please See the DiagnosticSource Users Guide
             https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/DiagnosticSourceUsersGuide.md
             for instructions on its use.
             </summary>
        </member>
        <member name="P:System.Diagnostics.DiagnosticListener.AllListeners">
            <summary>
            When you subscribe to this you get callbacks for all NotificationListeners in the appdomain
            as well as those that occurred in the past, and all future Listeners created in the future.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Predicate{System.String})">
             <summary>
             Add a subscriber (Observer).  If the isEnabled parameter is non-null it indicates that some events are
             uninteresting and can be skipped for efficiency.
             </summary>
             <param name="observer">Subscriber (IObserver)</param>
             <param name="isEnabled">Filters events based on their name (string). Should return true if the event is enabled.
            
             Note that the isEnabled predicate is an OPTIONAL OPTIMIZATION to allow the instrumentation site to avoid
             setting up the payload and calling 'Write' when no subscriber cares about it. In particular the
             instrumentation site has the option of ignoring the IsEnabled() predicate (not calling it) and simply
             calling Write().   Thus if the subscriber requires the filtering, it needs to do it itself.
            
             If this parameter is null, no filtering is done (all overloads of DiagnosticSource.IsEnabled return true).
             </param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean})">
             <summary>
             Add a subscriber (Observer).  If the isEnabled parameter is non-null indicates that some events are
             uninteresting can be skipped for efficiency.
             </summary>
             <param name="observer">Subscriber (IObserver)</param>
             <param name="isEnabled">Filters events based on their name (string) and up to two context object (which can be null).
            
             A particular instrumentation site HAS THE OPTION of calling one or more 'IsEnabled' overloads  in which
             it passes the name of the event and up to two other (instrumentation site specific) objects as arguments.
             If any of these 'IsEnabled' calls are made then this 'isEnabled' predicate is invoked with passed values
             (if shorter overloads are used, null is passed for missing context objects).
            
             This gives any particular instrumentation site the ability to pass up to two pieces of information to the
             subscriber to do sophisticated, efficient filtering.  This requires more coupling between the instrumentation
             site and the subscriber code.
            
             It IS expected that a particular instrumentation site may call different overloads of IsEnabled for the
             same event, first calling IsEnable(string) which calls the filter with two null context objects and if
             'isEnabled' returns true calling again with context objects.   The isEnabled filter should be designed
             with this in mind.
            
             Note that the isEnabled predicate is an OPTIONAL OPTIMIZATION to allow the instrumentation site to avoid
             setting up the payload and calling 'Write' when no subscriber cares about it. In particular the
             instrumentation site has the option of ignoring the IsEnabled() predicate (not calling it) and simply
             calling Write().   Thus if the subscriber requires the filtering, it needs to do it itself.
            
             If this parameter is null, no filtering is done (all overloads of DiagnosticSource.IsEnabled return true).
             </param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Same as other Subscribe overload where the predicate is assumed to always return true.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.#ctor(System.String)">
            <summary>
            Make a new DiagnosticListener, it is a NotificationSource, which means the returned result can be used to
            log notifications, but it also has a Subscribe method so notifications can be forwarded
            arbitrarily.  Thus its job is to forward things from the producer to all the listeners
            (multi-casting).    Generally you should not be making your own DiagnosticListener but use the
            DiagnosticListener.Default, so that notifications are as 'public' as possible.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Dispose">
            <summary>
            Clean up the NotificationListeners.   Notification listeners do NOT DIE ON THEIR OWN
            because they are in a global list (for discoverability).  You must dispose them explicitly.
            Note that we do not do the Dispose(bool) pattern because we frankly don't want to support
            subclasses that have non-managed state.
            </summary>
        </member>
        <member name="P:System.Diagnostics.DiagnosticListener.Name">
            <summary>
            When a DiagnosticListener is created it is given a name.   Return this.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.ToString">
            <summary>
            Return the name for the ToString() to aid in debugging.
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled">
            <summary>
            Determines whether there are any registered subscribers
            </summary>
            <remarks> If there is an expensive setup for the notification,
            you may call IsEnabled() as the first and most efficient check before doing this setup.
            Producers may optionally use this check before IsEnabled(string) in the most performance-critical parts of the system
            to ensure somebody listens to the DiagnosticListener at all.</remarks>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String)">
            <summary>
            Override abstract method
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.IsEnabled(System.String,System.Object,System.Object)">
            <summary>
            Override abstract method
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Write(System.String,System.Object)">
            <summary>
            Override abstract method
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticListener.AllListenerObservable">
            <summary>
            Logically AllListenerObservable has a very simple task.  It has a linked list of subscribers that want
            a callback when a new listener gets created.   When a new DiagnosticListener gets created it should call
            OnNewDiagnosticListener so that AllListenerObservable can forward it on to all the subscribers.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.AllListenerObservable.OnNewDiagnosticListener(System.Diagnostics.DiagnosticListener)">
            <summary>
            Called when a new DiagnosticListener gets created to tell anyone who subscribed that this happened.
            </summary>
            <param name="diagnosticListener"></param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.AllListenerObservable.Remove(System.Diagnostics.DiagnosticListener.AllListenerObservable.AllListenerSubscription)">
            <summary>
            Remove 'subscription from the list of subscriptions that the observable has.   Called when
            subscriptions are disposed.   Returns true if the subscription was removed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticListener.AllListenerObservable.AllListenerSubscription">
            <summary>
            One node in the linked list of subscriptions that AllListenerObservable keeps.   It is
            IDisposable, and when that is called it removes itself from the list.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean},System.Action{System.Diagnostics.Activity,System.Object},System.Action{System.Diagnostics.Activity,System.Object})">
            <summary>
            Add a subscriber (Observer).  If the isEnabled parameter is non-null indicates that some events are
            uninteresting can be skipped for efficiency.  You can also supply an 'onActivityImport' and 'onActivityExport'
            methods that should be called when providers are 'importing' or 'exporting' activities from outside the
            process (e.g. from Http Requests).   These are called right after importing (exporting) the activity and
            can be used to modify the activity (or outgoing request) to add policy.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource">
             <summary>
             DiagnosticSourceEventSource serves two purposes
            
               1) It allows debuggers to inject code via Function evaluation. This is the purpose of the
               BreakPointWithDebuggerFuncEval function in the 'OnEventCommand' method. Basically even in
               release code, debuggers can place a breakpoint in this method and then trigger the
               DiagnosticSourceEventSource via ETW. Thus from outside the process you can get a hook that
               is guaranteed to happen BEFORE any DiagnosticSource events (if the process is just starting)
               or as soon as possible afterward if it is on attach.
            
               2) It provides a 'bridge' that allows DiagnosticSource messages to be forwarded to EventListers
               or ETW. You can do this by enabling the Microsoft-Diagnostics-DiagnosticSource with the
               'Events' keyword (for diagnostics purposes, you should also turn on the 'Messages' keyword.
            
               This EventSource defines a EventSource argument called 'FilterAndPayloadSpecs' that defines
               what DiagnosticSources to enable and what parts of the payload to serialize into the key-value
               list that will be forwarded to the EventSource. If it is empty, values of properties of the
               diagnostic source payload are dumped as strings (using ToString()) and forwarded to the EventSource.
               For what people think of as serializable object strings, primitives this gives you want you want.
               (the value of the property in string form) for what people think of as non-serializable objects
               (e.g. HttpContext) the ToString() method is typically not defined, so you get the Object.ToString()
               implementation that prints the type name. This is useful since this is the information you need
               (the type of the property) to discover the field names so you can create a transform specification
               that will pick off the properties you desire.
            
               Once you have the particular values you desire, the implicit payload elements are typically not needed
               anymore and you can prefix the Transform specification with a '-' which suppresses the implicit
               transform (you only get the values of the properties you specifically ask for.
            
               Logically a transform specification is simply a fetching specification X.Y.Z along with a name to give
               it in the output (which defaults to the last name in the fetch specification).
            
               The FilterAndPayloadSpecs is one long string with the following structures
            
               * It is a newline separated list of FILTER_AND_PAYLOAD_SPEC
               * a FILTER_AND_PAYLOAD_SPEC can be
                   * EVENT_NAME : TRANSFORM_SPECS
                   * EMPTY - turns on all sources with implicit payload elements.
               * an EVENTNAME can be
                   * DIAGNOSTIC_SOURCE_NAME / DIAGNOSTIC_EVENT_NAME @ EVENT_SOURCE_EVENTNAME - give the name as well as the EventSource event to log it under.
                   * DIAGNOSTIC_SOURCE_NAME / DIAGNOSTIC_EVENT_NAME
                   * DIAGNOSTIC_SOURCE_NAME    - which wildcards every event in the Diagnostic source or
                   * EMPTY                     - which turns on all sources
                 Or it can be "[AS] ACTIVITY_SOURCE_NAME + ACTIVITY_NAME / ACTIVITY_EVENT_NAME - SAMPLING_RESULT"
                   * All parts are optional and can be empty string.
                   * ACTIVITY_SOURCE_NAME can be "*" to listen to all ActivitySources
                   * ACTIVITY_SOURCE_NAME can be empty string which will listen to ActivitySource that create Activities using "new Activity(...)"
                   * ACTIVITY_NAME is the activity operation name to filter with.
                   * ACTIVITY_EVENT_NAME either "Start" to listen to Activity Start event, or "Stop" to listen to Activity Stop event, or empty string to listen to both Start and Stop Activity events.
                   * SAMPLING_RESULT either "Propagate" to create the Activity with PropagationData, or "Record" to create the Activity with AllData, or empty string to create the Activity with AllDataAndRecorded
               * TRANSFORM_SPEC is a semicolon separated list of TRANSFORM_SPEC, which can be
                   * - TRANSFORM_SPEC               - the '-' indicates that implicit payload elements should be suppressed
                   * VARIABLE_NAME = PROPERTY_SPEC  - indicates that a payload element 'VARIABLE_NAME' is created from PROPERTY_SPEC
                   * PROPERTY_SPEC                  - This is a shortcut where VARIABLE_NAME is the LAST property name
               * a PROPERTY_SPEC is basically a list of names separated by '.'
                   * PROPERTY_NAME                  - fetches a property from the DiagnosticSource payload object
                   * PROPERTY_NAME . PROPERTY NAME  - fetches a sub-property of the object.
            
                   * *Activity                      - fetches Activity.Current
                   * *Enumerate                     - enumerates all the items in an IEnumerable, calls ToString() on them, and joins the
                                                      strings in a comma separated list.
             Example1:
            
                "BridgeTestSource1/TestEvent1:cls_Point_X=cls.Point.X;cls_Point_Y=cls.Point.Y\r\n" +
                "BridgeTestSource2/TestEvent2:-cls.Url"
            
             This indicates that two events should be turned on, The 'TestEvent1' event in BridgeTestSource1 and the
             'TestEvent2' in BridgeTestSource2. In the first case, because the transform did not begin with a -
             any primitive type/string of 'TestEvent1's payload will be serialized into the output. In addition if
             there a property of the payload object called 'cls' which in turn has a property 'Point' which in turn
             has a property 'X' then that data is also put in the output with the name cls_Point_X. Similarly
             if cls.Point.Y exists, then that value will also be put in the output with the name cls_Point_Y.
            
             For the 'BridgeTestSource2/TestEvent2' event, because the - was specified NO implicit fields will be
             generated, but if there is a property call 'cls' which has a property 'Url' then that will be placed in
             the output with the name 'Url' (since that was the last property name used and no Variable= clause was
             specified.
            
             Example:
            
                 "BridgeTestSource1\r\n" +
                 "BridgeTestSource2"
            
             This will enable all events for the BridgeTestSource1 and BridgeTestSource2 sources. Any string/primitive
             properties of any of the events will be serialized into the output.
            
             Example:
            
                 ""
            
             This turns on all DiagnosticSources Any string/primitive properties of any of the events will be serialized
             into the output. This is not likely to be a good idea as it will be very verbose, but is useful to quickly
             discover what is available.
            
             Example:
                 "[AS]*"                      listen to all ActivitySources and all Activities events (Start/Stop). Activities will be created with AllDataAndRecorded sampling.
                 "[AS]"                       listen to default ActivitySource and Activities events (Start/Stop) while the Activity is created using "new Activity(...)". Such Activities will be created with AllDataAndRecorded sampling.
                 "[AS]MyLibrary/Start"        listen to `MyLibrary` ActivitySource and the 'Start' Activity event. The Activities will be created with AllDataAndRecorded sampling.
                 "[AS]MyLibrary/-Propagate"   listen to `MyLibrary` ActivitySource and the 'Start and Stop' Activity events. The Activities will be created with PropagationData sampling.
                 "[AS]MyLibrary/Stop-Record"  listen to `MyLibrary` ActivitySource and the 'Stop' Activity event. The Activities will be created with AllData sampling.
                 "[AS]*/-"                    listen to all ActivitySources and the Start and Stop Activity events. Activities will be created with AllDataAndRecorded sampling. this equivalent to "[AS]*" too.
                 "[AS]*+MyActivity"           listen to all activity sources when creating Activity with the operation name "MyActivity".
            
             * How data is logged in the EventSource
            
             By default all data from DiagnosticSources is logged to the DiagnosticEventSource event called 'Event'
             which has three fields
            
                 string SourceName,
                 string EventName,
                 IEnumerable[KeyValuePair[string, string]] Argument
            
             However to support start-stop activity tracking, there are six other events that can be used
            
                 Activity1Start
                 Activity1Stop
                 Activity2Start
                 Activity2Stop
                 RecursiveActivity1Start
                 RecursiveActivity1Stop
            
             By using the SourceName/EventName@EventSourceName syntax, you can force particular DiagnosticSource events to
             be logged with one of these EventSource events. This is useful because the events above have start-stop semantics
             which means that they create activity IDs that are attached to all logging messages between the start and
             the stop (see https://blogs.msdn.microsoft.com/vancem/2015/09/14/exploring-eventsource-activity-correlation-and-causation-features/)
            
             For example the specification
            
                 "MyDiagnosticSource/RequestStart@Activity1Start\r\n" +
                 "MyDiagnosticSource/RequestStop@Activity1Stop\r\n" +
                 "MyDiagnosticSource/SecurityStart@Activity2Start\r\n" +
                 "MyDiagnosticSource/SecurityStop@Activity2Stop\r\n"
            
             Defines that RequestStart will be logged with the EventSource Event Activity1Start (and the corresponding stop) which
             means that all events caused between these two markers will have an activity ID associated with this start event.
             Similarly SecurityStart is mapped to Activity2Start.
            
             Note you can map many DiagnosticSource events to the same EventSource Event (e.g. Activity1Start). As long as the
             activities don't nest, you can reuse the same event name (since the payloads have the DiagnosticSource name which can
             disambiguate). However if they nest you need to use another EventSource event because the rules of EventSource
             activities state that a start of the same event terminates any existing activity of the same name.
            
             As its name suggests RecursiveActivity1Start, is marked as recursive and thus can be used when the activity can nest with
             itself. This should not be a 'top most' activity because it is not 'self healing' (if you miss a stop, then the
             activity NEVER ends).
            
             See the DiagnosticSourceEventSourceBridgeTest.cs for more explicit examples of using this bridge.
             </summary>
        </member>
        <member name="F:System.Diagnostics.DiagnosticSourceEventSource.Keywords.Messages">
            <summary>
            Indicates diagnostics messages from DiagnosticSourceEventSource should be included.
            </summary>
        </member>
        <member name="F:System.Diagnostics.DiagnosticSourceEventSource.Keywords.Events">
            <summary>
            Indicates that all events from all diagnostic sources should be forwarded to the EventSource using the 'Event' event.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Message(System.String)">
            <summary>
            Used to send ad-hoc diagnostics to humans.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Event(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Events from DiagnosticSource can be forwarded to EventSource using this event.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.EventJson(System.String,System.String,System.String)">
            <summary>
            This is only used on V4.5 systems that don't have the ability to log KeyValuePairs directly.
            It will eventually go away, but we should always reserve the ID for this.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Activity1Start(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the beginning of an activity
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Activity1Stop(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the end of an activity
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Activity2Start(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the beginning of an activity
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.Activity2Stop(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the end of an activity that can be recursive.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.RecursiveActivity1Start(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the beginning of an activity
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.RecursiveActivity1Stop(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Used to mark the end of an activity that can be recursive.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.NewDiagnosticListener(System.String)">
            <summary>
            Fires when a new DiagnosticSource becomes available.
            </summary>
            <param name="SourceName"></param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.ActivityStart(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Fires when the Activity start.
            </summary>
            <param name="SourceName">The ActivitySource name</param>
            <param name="ActivityName">The Activity name</param>
            <param name="Arguments">Name and value pairs of the Activity properties</param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.ActivityStop(System.String,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Fires when the Activity stop.
            </summary>
            <param name="SourceName">The ActivitySource name</param>
            <param name="ActivityName">The Activity name</param>
            <param name="Arguments">Name and value pairs of the Activity properties</param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
            <summary>
            Called when the EventSource gets a command from a EventListener or ETW.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.BreakPointWithDebuggerFuncEval">
            <summary>
            A function which is fully interruptible even in release code so we can stop here and
            do function evaluation in the debugger. Thus this is just a place that is useful
            for the debugger to place a breakpoint where it can inject code with function evaluation
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform">
             <summary>
             FilterAndTransform represents on transformation specification from a DiagnosticsSource
             to EventSource's 'Event' method. (e.g. MySource/MyEvent:out=prop1.prop2.prop3).
             Its main method is 'Morph' which takes a DiagnosticSource object and morphs it into
             a list of string,string key value pairs.
            
             This method also contains that static 'Create/Destroy FilterAndTransformList, which
             simply parse a series of transformation specifications.
             </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform.CreateFilterAndTransformList(System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform@,System.String,System.Diagnostics.DiagnosticSourceEventSource)">
             <summary>
             Parses filterAndPayloadSpecs which is a list of lines each of which has the from
            
                DiagnosticSourceName/EventName:PAYLOAD_SPEC
            
             where PAYLOADSPEC is a semicolon separated list of specifications of the form
            
                OutputName=Prop1.Prop2.PropN
            
             Into linked list of FilterAndTransform that together forward events from the given
             DiagnosticSource's to 'eventSource'. Sets the 'specList' variable to this value
             (destroying anything that was there previously).
            
             By default any serializable properties of the payload object are also included
             in the output payload, however this feature and be tuned off by prefixing the
             PAYLOADSPEC with a '-'.
             </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform.DestroyFilterAndTransformList(System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform@,System.Diagnostics.DiagnosticSourceEventSource)">
            <summary>
            This destroys (turns off) the FilterAndTransform stopping the forwarding started with CreateFilterAndTransformList
            </summary>
            <param name="specList"></param>
            <param name="eventSource"></param>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform.#ctor(System.String,System.Int32,System.Int32,System.Diagnostics.DiagnosticSourceEventSource,System.Diagnostics.DiagnosticSourceEventSource.FilterAndTransform)">
            <summary>
            Creates one FilterAndTransform specification from filterAndPayloadSpec starting at 'startIdx' and ending just before 'endIdx'.
            This FilterAndTransform will subscribe to DiagnosticSources specified by the specification and forward them to 'eventSource.
            For convenience, the 'Next' field is set to the 'next' parameter, so you can easily form linked lists.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec">
            <summary>
            Transform spec represents a string that describes how to extract a piece of data from
            the DiagnosticSource payload. An example string is OUTSTR=EVENT_VALUE.PROP1.PROP2.PROP3
            It has a Next field so they can be chained together in a linked list.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.#ctor(System.String,System.Int32,System.Int32,System.Diagnostics.DiagnosticSourceEventSource.TransformSpec)">
            <summary>
            parse the strings 'spec' from startIdx to endIdx (points just beyond the last considered char)
            The syntax is ID1=ID2.ID3.ID4 .... Where ID1= is optional.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.Morph(System.Object)">
            <summary>
            Given the DiagnosticSourcePayload 'obj', compute a key-value pair from it. For example
            if the spec is OUTSTR=EVENT_VALUE.PROP1.PROP2.PROP3 and the ultimate value of PROP3 is
            10 then the return key value pair is  KeyValuePair("OUTSTR","10")
            </summary>
        </member>
        <member name="F:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.Next">
            <summary>
            A public field that can be used to form a linked list.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec">
            <summary>
            A PropertySpec represents information needed to fetch a property from
            and efficiently. Thus it represents a '.PROP' in a TransformSpec
            (and a transformSpec has a list of these).
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.#ctor(System.String,System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec)">
            <summary>
            Make a new PropertySpec for a property named 'propertyName'.
            For convenience you can set he 'next' field to form a linked
            list of PropertySpecs.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.Fetch(System.Object)">
            <summary>
            Given an object fetch the property that this PropertySpec represents.
            obj may be null when IsStatic is true, otherwise it must be non-null.
            </summary>
        </member>
        <member name="F:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.Next">
            <summary>
            A public field that can be used to form a linked list.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch">
            <summary>
            PropertyFetch is a helper class. It takes a PropertyInfo and then knows how
            to efficiently fetch that property from a .NET object (See Fetch method).
            It hides some slightly complex generic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch.Type">
            <summary>
            The type of the object that the property is fetched from. For well-known static methods that
            aren't actually property getters this will return null.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch.FetcherForProperty(System.Type,System.String)">
            <summary>
            Create a property fetcher for a propertyName
            </summary>
        </member>
        <member name="M:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch.Fetch(System.Object)">
            <summary>
            Given an object, fetch the property that this propertyFech represents.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch.CurrentActivityPropertyFetch">
            <summary>
            A fetcher that returns the result of Activity.Current
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.TransformSpec.PropertySpec.PropertyFetch.EnumeratePropertyFetch`1">
            <summary>
            A fetcher that enumerates and formats an IEnumerable
            </summary>
        </member>
        <member name="T:System.Diagnostics.DiagnosticSourceEventSource.CallbackObserver`1">
            <summary>
            CallbackObserver is an adapter class that creates an observer (which you can pass
            to IObservable.Subscribe), and calls the given callback every time the 'next'
            operation on the IObserver happens.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute">
            <summary>
            States a dependency that one member has on another.
            </summary>
            <remarks>
            This can be used to inform tooling of a dependency that is otherwise not evident purely from
            metadata and IL, for example a member relied on via reflection.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on the same type as the consumer.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on a <see cref="T:System.Type"/>.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="type">The <see cref="T:System.Type"/> containing <paramref name="memberSignature"/>.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on a type in an assembly.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="typeName">The full name of the type containing the specified member.</param>
            <param name="assemblyName">The assembly name of the type containing the specified member.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified types of members on a <see cref="T:System.Type"/>.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="type">The <see cref="T:System.Type"/> containing the specified members.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified types of members on a type in an assembly.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="typeName">The full name of the type containing the specified members.</param>
            <param name="assemblyName">The assembly name of the type containing the specified members.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature">
            <summary>
            Gets the signature of the member depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature"/> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes"/>
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None"/>, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature"/> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes"/>
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None"/>, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type">
            <summary>
            Gets the <see cref="T:System.Type"/> containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type"/> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName">
            <summary>
            Gets the full name of the type containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type"/> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName">
            <summary>
            Gets the assembly name of the specified type.
            </summary>
            <remarks>
            <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName"/> is only valid when <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> is specified.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Condition">
            <summary>
            Gets or sets the condition in which the dependency is applicable, e.g. "DEBUG".
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requries unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.Activity">
             <summary>
             Activity represents operation with context to be used for logging.
             Activity has operation name, Id, start time and duration, tags and baggage.
            
             Current activity can be accessed with static AsyncLocal variable Activity.Current.
            
             Activities should be created with constructor, configured as necessary
             and then started with Activity.Start method which maintains parent-child
             relationships for the activities and sets Activity.Current.
            
             When activity is finished, it should be stopped with static Activity.Stop method.
            
             No methods on Activity allow exceptions to escape as a response to bad inputs.
             They are thrown and caught (that allows Debuggers and Monitors to see the error)
             but the exception is suppressed, and the operation does something reasonable (typically
             doing nothing).
             </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.ForceDefaultIdFormat">
            <summary>
            Normally if the ParentID is defined, the format of that is used to determine the
            format used by the Activity. However if ForceDefaultFormat is set to true, the
            ID format will always be the DefaultIdFormat even if the ParentID is define and is
            a different format.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Status">
            <summary>
            Gets status code of the current activity object.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.StatusDescription">
            <summary>
            Gets the status description of the current activity object.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.SetStatus(System.Diagnostics.ActivityStatusCode,System.String)">
            <summary>
            Sets the status code and description on the current activity object.
            </summary>
            <param name="code">The status code</param>
            <param name="description">The error status description</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
            <remarks>
            When passing code value different than ActivityStatusCode.Error, the Activity.StatusDescription will reset to null value.
            The description paramater will be respected only when passing ActivityStatusCode.Error value.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.Activity.Kind">
            <summary>
            Gets the relationship between the Activity, its parents, and its children in a Trace.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.OperationName">
            <summary>
            An operation name is a COARSEST name that is useful grouping/filtering.
            The name is typically a compile-time constant.   Names of Rest APIs are
            reasonable, but arguments (e.g. specific accounts etc), should not be in
            the name but rather in the tags.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.DisplayName">
            <summary>Gets or sets the display name of the Activity</summary>
            <remarks>
            DisplayName is intended to be used in a user interface and need not be the same as OperationName.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.Activity.Source">
            <summary>Get the ActivitySource object associated with this Activity.</summary>
            <remarks>
            All Activities created from public constructors will have a singleton source where the source name is an empty string.
            Otherwise, the source will hold the object that created the Activity through ActivitySource.StartActivity.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.Activity.Parent">
            <summary>
            If the Activity that created this activity is from the same process you can get
            that Activity with Parent.  However, this can be null if the Activity has no
            parent (a root activity) or if the Parent is from outside the process.
            </summary>
            <seealso cref="P:System.Diagnostics.Activity.ParentId"/>
        </member>
        <member name="P:System.Diagnostics.Activity.Duration">
            <summary>
            If the Activity has ended (<see cref="M:System.Diagnostics.Activity.Stop"/> or <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)"/> was called) then this is the delta
            between <see cref="P:System.Diagnostics.Activity.StartTimeUtc"/> and end.   If Activity is not ended and <see cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)"/> was not called then this is
            <see cref="F:System.TimeSpan.Zero"/>.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.StartTimeUtc">
            <summary>
            The time that operation started.  It will typically be initialized when <see cref="M:System.Diagnostics.Activity.Start"/>
            is called, but you can set at any time via <see cref="M:System.Diagnostics.Activity.SetStartTime(System.DateTime)"/>.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Id">
            <summary>
            This is an ID that is specific to a particular request.   Filtering
            to a particular ID insures that you get only one request that matches.
            Id has a hierarchical structure: '|root-id.id1_id2.id3_' Id is generated when
            <see cref="M:System.Diagnostics.Activity.Start"/> is called by appending suffix to Parent.Id
            or ParentId; Activity has no Id until it started
            <para/>
            See <see href="https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/ActivityUserGuide.md#id-format"/> for more details
            </summary>
            <example>
            Id looks like '|a000b421-5d183ab6.1.8e2d4c28_1.':<para />
             - '|a000b421-5d183ab6.' - Id of the first, top-most, Activity created<para />
             - '|a000b421-5d183ab6.1.' - Id of a child activity. It was started in the same process as the first activity and ends with '.'<para />
             - '|a000b421-5d183ab6.1.8e2d4c28_' - Id of the grand child activity. It was started in another process and ends with '_'<para />
            'a000b421-5d183ab6' is a <see cref="P:System.Diagnostics.Activity.RootId"/> for the first Activity and all its children
            </example>
        </member>
        <member name="P:System.Diagnostics.Activity.ParentId">
            <summary>
            If the parent for this activity comes from outside the process, the activity
            does not have a Parent Activity but MAY have a ParentId (which was deserialized from
            from the parent).   This accessor fetches the parent ID if it exists at all.
            Note this can be null if this is a root Activity (it has no parent)
            <para/>
            See <see href="https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/ActivityUserGuide.md#id-format"/> for more details
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.RootId">
            <summary>
            Root Id is substring from Activity.Id (or ParentId) between '|' (or beginning) and first '.'.
            Filtering by root Id allows to find all Activities involved in operation processing.
            RootId may be null if Activity has neither ParentId nor Id.
            See <see href="https://github.com/dotnet/corefx/blob/master/src/System.Diagnostics.DiagnosticSource/src/ActivityUserGuide.md#id-format"/> for more details
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Tags">
            <summary>
            Tags are string-string key-value pairs that represent information that will
            be logged along with the Activity to the logging system. This information
            however is NOT passed on to the children of this activity.
            </summary>
            <seealso cref="P:System.Diagnostics.Activity.Baggage"/>
        </member>
        <member name="P:System.Diagnostics.Activity.TagObjects">
            <summary>
            List of the tags which represent information that will be logged along with the Activity to the logging system.
            This information however is NOT passed on to the children of this activity.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Events">
            <summary>
            Events is the list of all <see cref="T:System.Diagnostics.ActivityEvent" /> objects attached to this Activity object.
            If there is not any <see cref="T:System.Diagnostics.ActivityEvent" /> object attached to the Activity object, Events will return empty list.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Links">
            <summary>
            Links is the list of all <see cref="T:System.Diagnostics.ActivityLink" /> objects attached to this Activity object.
            If there is no any <see cref="T:System.Diagnostics.ActivityLink" /> object attached to the Activity object, Links will return empty list.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Baggage">
            <summary>
            Baggage is string-string key-value pairs that represent information that will
            be passed along to children of this activity.   Baggage is serialized
            when requests leave the process (along with the ID).   Typically Baggage is
            used to do fine-grained control over logging of the activity and any children.
            In general, if you are not using the data at runtime, you should be using Tags
            instead.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.GetBaggageItem(System.String)">
            <summary>
            Returns the value of the key-value pair added to the activity with <see cref="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)"/>.
            Returns null if that key does not exist.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.GetTagItem(System.String)">
            <summary>
            Returns the value of the Activity tag mapped to the input key/>.
            Returns null if that key does not exist.
            </summary>
            <param name="key">The tag key string.</param>
            <returns>The tag value mapped to the input key.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.#ctor(System.String)">
            <summary>
            Note that Activity has a 'builder' pattern, where you call the constructor, a number of 'Set*' and 'Add*' APIs and then
            call <see cref="M:System.Diagnostics.Activity.Start"/> to build the activity. You MUST call <see cref="M:System.Diagnostics.Activity.Start"/> before using it.
            </summary>
            <param name="operationName">Operation's name <see cref="P:System.Diagnostics.Activity.OperationName"/></param>
        </member>
        <member name="M:System.Diagnostics.Activity.AddTag(System.String,System.String)">
            <summary>
            Update the Activity to have a tag with an additional 'key' and value 'value'.
            This shows up in the <see cref="P:System.Diagnostics.Activity.Tags"/>  enumeration. It is meant for information that
            is useful to log but not needed for runtime control (for the latter, <see cref="P:System.Diagnostics.Activity.Baggage"/>)
            </summary>
            <returns><see langword="this" /> for convenient chaining.</returns>
            <param name="key">The tag key name</param>
            <param name="value">The tag value mapped to the input key</param>
        </member>
        <member name="M:System.Diagnostics.Activity.AddTag(System.String,System.Object)">
            <summary>
            Update the Activity to have a tag with an additional 'key' and value 'value'.
            This shows up in the <see cref="P:System.Diagnostics.Activity.TagObjects"/> enumeration. It is meant for information that
            is useful to log but not needed for runtime control (for the latter, <see cref="P:System.Diagnostics.Activity.Baggage"/>)
            </summary>
            <returns><see langword="this" /> for convenient chaining.</returns>
            <param name="key">The tag key name</param>
            <param name="value">The tag value mapped to the input key</param>
        </member>
        <member name="M:System.Diagnostics.Activity.SetTag(System.String,System.Object)">
            <summary>
            Add or update the Activity tag with the input key and value.
            If the input value is null
                - if the collection has any tag with the same key, then this tag will get removed from the collection.
                - otherwise, nothing will happen and the collection will not change.
            If the input value is not null
                - if the collection has any tag with the same key, then the value mapped to this key will get updated with the new input value.
                - otherwise, the key and value will get added as a new tag to the collection.
            </summary>
            <param name="key">The tag key name</param>
            <param name="value">The tag value mapped to the input key</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.AddEvent(System.Diagnostics.ActivityEvent)">
            <summary>
            Add <see cref="T:System.Diagnostics.ActivityEvent" /> object to the <see cref="P:System.Diagnostics.Activity.Events" /> list.
            </summary>
            <param name="e"> object of <see cref="T:System.Diagnostics.ActivityEvent"/> to add to the attached events list.</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.AddBaggage(System.String,System.String)">
            <summary>
            Update the Activity to have baggage with an additional 'key' and value 'value'.
            This shows up in the <see cref="P:System.Diagnostics.Activity.Baggage"/> enumeration as well as the <see cref="M:System.Diagnostics.Activity.GetBaggageItem(System.String)"/>
            method.
            Baggage is meant for information that is needed for runtime control.   For information
            that is simply useful to show up in the log with the activity use <see cref="P:System.Diagnostics.Activity.Tags"/>.
            Returns 'this' for convenient chaining.
            </summary>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.SetBaggage(System.String,System.String)">
            <summary>
            Add or update the Activity baggage with the input key and value.
            If the input value is null
                - if the collection has any baggage with the same key, then this baggage will get removed from the collection.
                - otherwise, nothing will happen and the collection will not change.
            If the input value is not null
                - if the collection has any baggage with the same key, then the value mapped to this key will get updated with the new input value.
                - otherwise, the key and value will get added as a new baggage to the collection.
            </summary>
            <param name="key">The baggage key name</param>
            <param name="value">The baggage value mapped to the input key</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.SetParentId(System.String)">
            <summary>
            Updates the Activity To indicate that the activity with ID <paramref name="parentId"/>
            caused this activity.   This is intended to be used only at 'boundary'
            scenarios where an activity from another process logically started
            this activity. The Parent ID shows up the Tags (as well as the ParentID
            property), and can be used to reconstruct the causal tree.
            Returns 'this' for convenient chaining.
            </summary>
            <param name="parentId">The id of the parent operation.</param>
        </member>
        <member name="M:System.Diagnostics.Activity.SetParentId(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivityTraceFlags)">
            <summary>
            Set the parent ID using the W3C convention using a TraceId and a SpanId. This
            constructor has the advantage that no string manipulation is needed to set the ID.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.SetStartTime(System.DateTime)">
            <summary>
            Update the Activity to set start time
            </summary>
            <param name="startTimeUtc">Activity start time in UTC (Greenwich Mean Time)</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)">
            <summary>
            Update the Activity to set <see cref="P:System.Diagnostics.Activity.Duration"/>
            as a difference between <see cref="P:System.Diagnostics.Activity.StartTimeUtc"/>
            and <paramref name="endTimeUtc"/>.
            </summary>
            <param name="endTimeUtc">Activity stop time in UTC (Greenwich Mean Time)</param>
            <returns><see langword="this" /> for convenient chaining.</returns>
        </member>
        <member name="P:System.Diagnostics.Activity.Context">
            <summary>
            Get the context of the activity. Context becomes valid only if the activity has been started.
            otherwise will default context.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.Start">
            <summary>
            Starts activity
            <list type="bullet">
            <item>Sets <see cref="P:System.Diagnostics.Activity.Parent"/> to hold <see cref="P:System.Diagnostics.Activity.Current"/>.</item>
            <item>Sets <see cref="P:System.Diagnostics.Activity.Current"/> to this activity.</item>
            <item>If <see cref="P:System.Diagnostics.Activity.StartTimeUtc"/> was not set previously, sets it to <see cref="P:System.DateTime.UtcNow"/>.</item>
            <item>Generates a unique <see cref="P:System.Diagnostics.Activity.Id"/> for this activity.</item>
            </list>
            Use <see cref="M:System.Diagnostics.DiagnosticSource.StartActivity(System.Diagnostics.Activity,System.Object)"/> to start activity and write start event.
            </summary>
            <seealso cref="M:System.Diagnostics.DiagnosticSource.StartActivity(System.Diagnostics.Activity,System.Object)"/>
            <seealso cref="M:System.Diagnostics.Activity.SetStartTime(System.DateTime)"/>
        </member>
        <member name="M:System.Diagnostics.Activity.Stop">
            <summary>
            Stops activity: sets <see cref="P:System.Diagnostics.Activity.Current"/> to <see cref="P:System.Diagnostics.Activity.Parent"/>.
            If end time was not set previously, sets <see cref="P:System.Diagnostics.Activity.Duration"/> as a difference between <see cref="P:System.DateTime.UtcNow"/> and <see cref="P:System.Diagnostics.Activity.StartTimeUtc"/>
            Use <see cref="M:System.Diagnostics.DiagnosticSource.StopActivity(System.Diagnostics.Activity,System.Object)"/>  to stop activity and write stop event.
            </summary>
            <seealso cref="M:System.Diagnostics.DiagnosticSource.StopActivity(System.Diagnostics.Activity,System.Object)"/>
            <seealso cref="M:System.Diagnostics.Activity.SetEndTime(System.DateTime)"/>
        </member>
        <member name="P:System.Diagnostics.Activity.TraceStateString">
             <summary>
             Holds the W3C 'tracestate' header as a string.
            
             Tracestate is intended to carry information supplemental to trace identity contained
             in traceparent. List of key value pairs carried by tracestate convey information
             about request position in multiple distributed tracing graphs. It is typically used
             by distributed tracing systems and should not be used as a general purpose baggage
             as this use may break correlation of a distributed trace.
            
             Logically it is just a kind of baggage (if flows just like baggage), but because
             it is expected to be special cased (it has its own HTTP header), it is more
             convenient/efficient if it is not lumped in with other baggage.
             </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.SpanId">
            <summary>
            If the Activity has the W3C format, this returns the ID for the SPAN part of the Id.
            Otherwise it returns a zero SpanId.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.TraceId">
            <summary>
            If the Activity has the W3C format, this returns the ID for the TraceId part of the Id.
            Otherwise it returns a zero TraceId.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Recorded">
            <summary>
            True if the W3CIdFlags.Recorded flag is set.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.IsAllDataRequested">
            <summary>
            Indicate if the this Activity object should be populated with all the propagation info and also all other
            properties such as Links, Tags, and Events.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.ActivityTraceFlags">
            <summary>
            Return the flags (defined by the W3C ID specification) associated with the activity.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.ParentSpanId">
            <summary>
            If the parent Activity ID has the W3C format, this returns the ID for the SpanId part of the ParentId.
            Otherwise it returns a zero SpanId.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.TraceIdGenerator">
            <summary>
            When starting an Activity which does not have a parent context, the Trace Id will automatically be generated using random numbers.
            TraceIdGenerator can be used to override the runtime's default Trace Id generation algorithm.
            </summary>
            <remarks>
            - TraceIdGenerator needs to be set only if the default Trace Id generation is not enough for the app scenario.
            - When setting TraceIdGenerator, ensure it is performant enough to avoid any slowness in the Activity starting operation.
            - If TraceIdGenerator is set multiple times, the last set will be the one used for the Trace Id generation.
            - Setting TraceIdGenerator to null will re-enable the default Trace Id generation algorithm.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.Activity.DefaultIdFormat">
            <summary>
            Activity tries to use the same format for IDs as its parent.
            However if the activity has no parent, it has to do something.
            This determines the default format we use.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.SetIdFormat(System.Diagnostics.ActivityIdFormat)">
            <summary>
            Sets IdFormat on the Activity before it is started.  It takes precedence over
            Parent.IdFormat, ParentId format, DefaultIdFormat and ForceDefaultIdFormat.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.IsW3CId(System.String)">
            <summary>
            Returns true if 'id' has the format of a WC3 id see https://w3c.github.io/trace-context
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.Dispose">
            <summary>
            Dispose will stop the Activity if it is already started and notify any event listeners. Nothing will happen otherwise.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.SetCustomProperty(System.String,System.Object)">
            <summary>
            SetCustomProperty allow attaching any custom object to this Activity object.
            If the property name was previously associated with other object, SetCustomProperty will update to use the new propert value instead.
            </summary>
            <param name="propertyName"> The name to associate the value with.<see cref="P:System.Diagnostics.Activity.OperationName"/></param>
            <param name="propertyValue">The object to attach and map to the property name.</param>
        </member>
        <member name="M:System.Diagnostics.Activity.GetCustomProperty(System.String)">
            <summary>
            GetCustomProperty retrieve previously attached object mapped to the property name.
            </summary>
            <param name="propertyName"> The name to get the associated object with.</param>
            <returns>The object mapped to the property name. Or null if there is no mapping previously done with this property name.</returns>
        </member>
        <member name="M:System.Diagnostics.Activity.GenerateW3CId">
            <summary>
            Set the ID (lazily, avoiding strings if possible) to a W3C ID (using the
            traceId from the parent if possible
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.GenerateHierarchicalId">
            <summary>
            Returns a new ID using the Hierarchical Id
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.IdFormat">
            <summary>
            Returns the format for the ID.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Activity.Current">
            <summary>
            Gets or sets the current operation (Activity) for the current thread.  This flows
            across async calls.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Activity.GetUtcNow">
            <summary>
            Returns high resolution (1 DateTime tick) current UTC DateTime.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityTraceFlags">
            <summary>
            These flags are defined by the W3C standard along with the ID for the activity.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityIdFormat">
            <summary>
            The possibilities for the format of the ID
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityTraceId">
            <summary>
            A TraceId is the format the W3C standard requires for its ID for the entire trace.
            It represents 16 binary bytes of information, typically displayed as 32 characters
            of Hexadecimal.  A TraceId is a STRUCT, and does contain the 16 bytes of binary information
            so there is value in passing it by reference.   It does know how to convert to and
            from its Hexadecimal string representation, tries to avoid changing formats until
            it has to, and caches the string representation after it was created.
            It is mostly useful as an exchange type.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.CreateRandom">
            <summary>
            Create a new TraceId with at random number in it (very likely to be unique)
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.ToHexString">
            <summary>
            Returns the TraceId as a 32 character hexadecimal string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.ToString">
            <summary>
            Returns the TraceId as a 32 character hexadecimal string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.#ctor(System.ReadOnlySpan{System.Byte})">
            <summary>
            This is exposed as CreateFromUtf8String, but we are modifying fields, so the code needs to be in a constructor.
            </summary>
            <param name="idData"></param>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.CopyTo(System.Span{System.Byte})">
            <summary>
            Copy the bytes of the TraceId (16 total) into the 'destination' span.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.SetToRandomBytes(System.Span{System.Byte})">
            <summary>
            Sets the bytes in 'outBytes' to be random values. outBytes.Length must be either 8 or 16 bytes.
            </summary>
            <param name="outBytes"></param>
        </member>
        <member name="M:System.Diagnostics.ActivityTraceId.SetSpanFromHexChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte})">
            <summary>
            Converts 'idData' which is assumed to be HEX Unicode characters to binary
            puts it in 'outBytes'
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivitySpanId">
            <summary>
            A SpanId is the format the W3C standard requires for its ID for a single span in a trace.
            It represents 8 binary bytes of information, typically displayed as 16 characters
            of Hexadecimal.  A SpanId is a STRUCT, and does contain the 8 bytes of binary information
            so there is value in passing it by reference.  It does know how to convert to and
            from its Hexadecimal string representation, tries to avoid changing formats until
            it has to, and caches the string representation after it was created.
            It is mostly useful as an exchange type.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySpanId.CreateRandom">
            <summary>
            Create a new SpanId with at random number in it (very likely to be unique)
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySpanId.ToHexString">
            <summary>
            Returns the SpanId as a 16 character hexadecimal string.
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Diagnostics.ActivitySpanId.ToString">
            <summary>
            Returns SpanId as a hex string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySpanId.CopyTo(System.Span{System.Byte})">
            <summary>
            Copy the bytes of the TraceId (8 bytes total) into the 'destination' span.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityStatusCode">
            <summary>
            Define the status code of the Activity which indicate the status of the instrumented operation.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityStatusCode.Unset">
            <summary>
            Unset status code is the default value indicating the status code is not initialized.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityStatusCode.Ok">
            <summary>
            Status code indicating the operation has been validated and completed successfully.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityStatusCode.Error">
            <summary>
            Status code indicating an error is encountered during the operation.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityTagsCollection">
            <summary>
            ActivityTagsCollection is a collection class used to store tracing tags.
            This collection will be used with classes like <see cref="T:System.Diagnostics.ActivityEvent"/> and <see cref="T:System.Diagnostics.ActivityLink"/>.
            This collection behaves as follows:
                - The collection items will be ordered according to how they are added.
                - Don't allow duplication of items with the same key.
                - When using the indexer to store an item in the collection:
                    - If the item has a key that previously existed in the collection and the value is null, the collection item matching the key will be removed from the collection.
                    - If the item has a key that previously existed in the collection and the value is not null, the new item value will replace the old value stored in the collection.
                    - Otherwise, the item will be added to the collection.
                - Add method will add a new item to the collection if an item doesn't already exist with the same key. Otherwise, it will throw an exception.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.#ctor">
            <summary>
            Create a new instance of the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Create a new instance of the collection and store the input list items in the collection.
            </summary>
            <param name="list">Initial list to store in the collection.</param>
        </member>
        <member name="P:System.Diagnostics.ActivityTagsCollection.Item(System.String)">
            <summary>
            Get or set collection item
            When setting a value to this indexer property, the following behavior will be observed:
                - If the key previously existed in the collection and the value is null, the collection item matching the key will get removed from the collection.
                - If the key previously existed in the collection and the value is not null, the value will replace the old value stored in the collection.
                - Otherwise, a new item will get added to the collection.
            </summary>
            <value>Object mapped to the key</value>
        </member>
        <member name="P:System.Diagnostics.ActivityTagsCollection.Keys">
            <summary>
            Get the list of the keys of all stored tags.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityTagsCollection.Values">
            <summary>
            Get the list of the values of all stored tags.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityTagsCollection.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityTagsCollection.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.Add(System.String,System.Object)">
            <summary>
            Adds a tag with the provided key and value to the collection.
            This collection doesn't allow adding two tags with the same key.
            </summary>
            <param name="key">The tag key.</param>
            <param name="value">The tag value.</param>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Adds an item to the collection
            </summary>
            <param name="item">Key and value pair of the tag to add to the collection.</param>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.ContainsKey(System.String)">
            <summary>
            Determines whether the collection contains an element with the specified key.
            </summary>
            <param name="key"></param>
            <returns>True if the collection contains tag with that key. False otherwise.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
            <summary>
            Copies the elements of the collection to an array, starting at a particular array index.
            </summary>
            <param name="array">The array that is the destination of the elements copied from collection.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,System#Object}}#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.Remove(System.String)">
            <summary>
            Removes the tag with the specified key from the collection.
            </summary>
            <param name="key">The tag key</param>
            <returns>True if the item existed and removed. False otherwise.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Removes the first occurrence of a specific item from the collection.
            </summary>
            <param name="item">The tag key value pair to remove.</param>
            <returns>True if item was successfully removed from the collection; otherwise, false. This method also returns false if item is not found in the original collection.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.TryGetValue(System.String,System.Object@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The tag key.</param>
            <param name="value">The tag value.</param>
            <returns>When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivityTagsCollection.FindIndex(System.String)">
            <summary>
            FindIndex finds the index of item in the list having a key matching the input key.
            We didn't use List.FindIndex to avoid the extra allocation caused by the closure when calling the Predicate delegate.
            </summary>
            <param name="key">The key to search the item in the list</param>
            <returns>The index of the found item, or -1 if the item not found.</returns>
        </member>
        <member name="T:System.Diagnostics.ActivityContext">
            <summary>
            ActivityContext representation conforms to the w3c TraceContext specification. It contains two identifiers
            a TraceId and a SpanId - along with a set of common TraceFlags and system-specific TraceState values.
            </summary>
            <summary>
            ActivityContext representation conforms to the w3c TraceContext specification. It contains two identifiers
            a TraceId and a SpanId - along with a set of common TraceFlags and system-specific TraceState values.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityContext.#ctor(System.Diagnostics.ActivityTraceId,System.Diagnostics.ActivitySpanId,System.Diagnostics.ActivityTraceFlags,System.String,System.Boolean)">
            <summary>
            Construct a new object of ActivityContext.
            </summary>
            <param name="traceId">A trace identifier.</param>
            <param name="spanId">A span identifier.</param>
            <param name="traceFlags">Contain details about the trace.</param>
            <param name="traceState">Carries system-specific configuration data.</param>
            <param name="isRemote">Indicate the context is propagated from remote parent.</param>
            <remarks>
            isRemote is not a part of W3C specification. It is needed for the OpenTelemetry scenarios.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.ActivityContext.TraceId">
            <summary>
            The trace identifier
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityContext.SpanId">
            <summary>
            The span identifier
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityContext.TraceFlags">
            <summary>
            These flags are defined by the W3C standard along with the ID for the activity.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityContext.TraceState">
            <summary>
            Holds the W3C 'tracestate' header as a string.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityContext.IsRemote">
            <summary>
            IsRemote indicates if the ActivityContext was propagated from a remote parent.
            </summary>
            <remarks>
            IsRemote is not a part of W3C specification. It is needed for the OpenTelemetry scenarios.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.ActivityContext.TryParse(System.String,System.String,System.Diagnostics.ActivityContext@)">
            <summary>
            Parse W3C trace context headers to ActivityContext object.
            </summary>
            <param name="traceParent">W3C trace parent header.</param>
            <param name="traceState">W3C trace state.</param>
            <param name="context">The ActivityContext object created from the parsing operation.</param>
        </member>
        <member name="M:System.Diagnostics.ActivityContext.Parse(System.String,System.String)">
            <summary>
            Parse W3C trace context headers to ActivityContext object.
            </summary>
            <param name="traceParent">W3C trace parent header.</param>
            <param name="traceState">Trace state.</param>
            <returns>
            The ActivityContext object created from the parsing operation.
            </returns>
        </member>
        <member name="T:System.Diagnostics.ActivityCreationOptions`1">
            <summary>
            ActivityCreationOptions is encapsulating all needed information which will be sent to the ActivityListener to decide about creating the Activity object and with what state.
            The possible generic type parameters is <see cref="T:System.Diagnostics.ActivityContext"/> or <see cref="T:System.String"/>
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityCreationOptions`1.#ctor(System.Diagnostics.ActivitySource,System.String,`0,System.Diagnostics.ActivityKind,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.Diagnostics.ActivityIdFormat)">
            <summary>
            Construct a new <see cref="T:System.Diagnostics.ActivityCreationOptions`1"/> object.
            </summary>
            <param name="source">The trace Activity source<see cref="T:System.Diagnostics.ActivitySource"/> used to request creating the Activity object.</param>
            <param name="name">The operation name of the Activity.</param>
            <param name="parent">The requested parent to create the Activity object with. The parent either be a parent Id represented as string or it can be a parent context <see cref="T:System.Diagnostics.ActivityContext"/>.</param>
            <param name="kind"><see cref="T:System.Diagnostics.ActivityKind"/> to create the Activity object with.</param>
            <param name="tags">Key-value pairs list for the tags to create the Activity object with.<see cref="T:System.Diagnostics.ActivityContext"/></param>
            <param name="links"><see cref="T:System.Diagnostics.ActivityLink"/> list to create the Activity object with.</param>
            <param name="idFormat">The default Id format to use.</param>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Source">
            <summary>
            Retrieve the <see cref="T:System.Diagnostics.ActivitySource"/> object.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Name">
            <summary>
            Retrieve the name which requested to create the Activity object with.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Kind">
            <summary>
            Retrieve the <see cref="T:System.Diagnostics.ActivityKind"/> which requested to create the Activity object with.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Parent">
            <summary>
            Retrieve the parent which requested to create the Activity object with. Parent will be either in form of string or <see cref="T:System.Diagnostics.ActivityContext"/>.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Tags">
            <summary>
            Retrieve the tags which requested to create the Activity object with.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.Links">
            <summary>
            Retrieve the list of <see cref="T:System.Diagnostics.ActivityLink"/> which requested to create the Activity object with.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityCreationOptions`1.IdFormat">
            <summary>
            Retrieve Id format of to use for the Activity we may create.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivitySamplingResult">
            <summary>
            Used by ActivityListener to indicate what amount of data should be collected for this Activity
            Requesting more data causes greater performance overhead to collect it.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivitySamplingResult.None">
            <summary>
            The Activity object doesn't need to be created
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivitySamplingResult.PropagationData">
            <summary>
            The Activity object needs to be created. It will have Name, Source, Id and Baggage.
            Other properties are unnecessary and will be ignored by this listener.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivitySamplingResult.AllData">
            <summary>
            The activity object should be populated with all the propagation info and also all other
            properties such as Links, Tags, and Events. Activity.IsAllDataRequested will return true.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivitySamplingResult.AllDataAndRecorded">
            <summary>
            The activity object should be populated the same as the AllData case and additionally
            Activity.IsRecorded is set true. For activities using W3C trace ids this sets a flag bit in the
            ID that will be propagated downstream requesting that trace is recorded everywhere.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityEvent">
            <summary>
            A text annotation associated with a collection of tags.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityEvent.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.ActivityEvent"/> class.
            </summary>
            <param name="name">Event name.</param>
        </member>
        <member name="M:System.Diagnostics.ActivityEvent.#ctor(System.String,System.DateTimeOffset,System.Diagnostics.ActivityTagsCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.ActivityEvent"/> class.
            </summary>
            <param name="name">Event name.</param>
            <param name="timestamp">Event timestamp. Timestamp MUST only be used for the events that happened in the past, not at the moment of this call.</param>
            <param name="tags">Event Tags.</param>
        </member>
        <member name="P:System.Diagnostics.ActivityEvent.Name">
            <summary>
            Gets the <see cref="T:System.Diagnostics.ActivityEvent"/> name.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityEvent.Timestamp">
            <summary>
            Gets the <see cref="T:System.Diagnostics.ActivityEvent"/> timestamp.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityEvent.Tags">
            <summary>
            Gets the collection of tags associated with the event.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityKind">
            <summary>
            Kind describes the relationship between the Activity, its parents, and its children in a Trace.
                --------------------------------------------------------------------------------
               ActivityKind    Synchronous  Asynchronous    Remote Incoming    Remote Outgoing
                --------------------------------------------------------------------------------
                  Internal
                  Client          yes                                               yes
                  Server          yes                            yes
                  Producer                     yes                                  maybe
                  Consumer                     yes               maybe
                --------------------------------------------------------------------------------
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityKind.Internal">
            <summary>
            Default value.
            Indicates that the Activity represents an internal operation within an application, as opposed to an operations with remote parents or children.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityKind.Server">
            <summary>
            Server activity represents request incoming from external component.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityKind.Client">
            <summary>
            Client activity represents outgoing request to the external component.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityKind.Producer">
            <summary>
            Producer activity represents output provided to external components.
            </summary>
        </member>
        <member name="F:System.Diagnostics.ActivityKind.Consumer">
            <summary>
            Consumer activity represents output received from an external component.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityLink">
            <summary>
            Activity may be linked to zero or more other <see cref="T:System.Diagnostics.ActivityContext"/> that are causally related.
            Links can point to ActivityContexts inside a single Trace or across different Traces.
            Links can be used to represent batched operations where a Activity was initiated by multiple initiating Activities,
            each representing a single incoming item being processed in the batch.
            </summary>
            <summary>
            Activity may be linked to zero or more other <see cref="T:System.Diagnostics.ActivityContext"/> that are causally related.
            Links can point to ActivityContexts inside a single Trace or across different Traces.
            Links can be used to represent batched operations where a Activity was initiated by multiple initiating Activities,
            each representing a single incoming item being processed in the batch.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityLink.#ctor(System.Diagnostics.ActivityContext,System.Diagnostics.ActivityTagsCollection)">
            <summary>
            Construct a new <see cref="T:System.Diagnostics.ActivityLink"/> object which can be linked to an Activity object.
            </summary>
            <param name="context">The trace Activity context<see cref="T:System.Diagnostics.ActivityContext"/></param>
            <param name="tags">The key-value pair list of tags which associated to the <see cref="T:System.Diagnostics.ActivityContext"/></param>
        </member>
        <member name="P:System.Diagnostics.ActivityLink.Context">
            <summary>
            Retrieve the <see cref="T:System.Diagnostics.ActivityContext"/> object inside this <see cref="T:System.Diagnostics.ActivityLink"/> object.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityLink.Tags">
            <summary>
            Retrieve the key-value pair list of tags attached with the <see cref="T:System.Diagnostics.ActivityContext"/>.
            </summary>
        </member>
        <member name="T:System.Diagnostics.SampleActivity`1">
            <summary>
            Define the callback that can be used in <see cref="T:System.Diagnostics.ActivityListener"/> to allow deciding to create the Activity objects and with what data state.
            </summary>
        </member>
        <member name="T:System.Diagnostics.ActivityListener">
            <summary>
            ActivityListener allows listening to the start and stop Activity events and give the oppertunity to decide creating the Activity for sampling scenarios.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityListener.#ctor">
            <summary>
            Construct a new <see cref="T:System.Diagnostics.ActivityListener"/> object to start listening to the <see cref="T:System.Diagnostics.Activity"/> events.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityListener.ActivityStarted">
            <summary>
            Set or get the callback used to listen to the <see cref="T:System.Diagnostics.Activity"/> start event.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityListener.ActivityStopped">
            <summary>
            Set or get the callback used to listen to the <see cref="T:System.Diagnostics.Activity"/> stop event.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityListener.ShouldListenTo">
            <summary>
            Set or get the callback used to decide if want to listen to <see cref="T:System.Diagnostics.Activity"/> objects events which created using <see cref="T:System.Diagnostics.ActivitySource"/> object.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityListener.SampleUsingParentId">
            <summary>
            Set or get the callback used to decide allowing creating <see cref="T:System.Diagnostics.Activity"/> objects with specific data state.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivityListener.Sample">
            <summary>
            Set or get the callback used to decide allowing creating <see cref="T:System.Diagnostics.Activity"/> objects with specific data state.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivityListener.Dispose">
            <summary>
            Dispose will unregister this <see cref="T:System.Diagnostics.ActivityListener"/> object from listening to <see cref="T:System.Diagnostics.Activity"/> events.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.#ctor(System.String,System.String)">
            <summary>
            Construct an ActivitySource object with the input name
            </summary>
            <param name="name">The name of the ActivitySource object</param>
            <param name="version">The version of the component publishing the tracing info.</param>
        </member>
        <member name="P:System.Diagnostics.ActivitySource.Name">
            <summary>
            Returns the ActivitySource name.
            </summary>
        </member>
        <member name="P:System.Diagnostics.ActivitySource.Version">
            <summary>
            Returns the ActivitySource version.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.HasListeners">
            <summary>
            Check if there is any listeners for this ActivitySource.
            This property can be helpful to tell if there is no listener, then no need to create Activity object
            and avoid creating the objects needed to create Activity (e.g. ActivityContext)
            Example of that is http scenario which can avoid reading the context data from the wire.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.CreateActivity(System.String,System.Diagnostics.ActivityKind)">
            <summary>
            Creates a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity, returns null otherwise.
            </summary>
            <param name="name">The operation name of the Activity</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any event listener.</returns>
            <remarks>
            If the Activity object is created, it will not start automatically. Callers need to call <see cref="M:System.Diagnostics.Activity.Start"/> to start it.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.CreateActivity(System.String,System.Diagnostics.ActivityKind,System.Diagnostics.ActivityContext,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.Diagnostics.ActivityIdFormat)">
            <summary>
            Creates a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity, returns null otherwise.
            If the Activity object is created, it will not automatically start. Callers will need to call <see cref="M:System.Diagnostics.Activity.Start"/> to start it.
            </summary>
            <param name="name">The operation name of the Activity.</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <param name="parentContext">The parent <see cref="T:System.Diagnostics.ActivityContext"/> object to initialize the created Activity object with.</param>
            <param name="tags">The optional tags list to initialize the created Activity object with.</param>
            <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink"/> list to initialize the created Activity object with.</param>
            <param name="idFormat">The default Id format to use.</param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any listener.</returns>
            <remarks>
            If the Activity object is created, it will not start automatically. Callers need to call <see cref="M:System.Diagnostics.Activity.Start"/> to start it.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.CreateActivity(System.String,System.Diagnostics.ActivityKind,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.Diagnostics.ActivityIdFormat)">
            <summary>
            Creates a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity, returns null otherwise.
            </summary>
            <param name="name">The operation name of the Activity.</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <param name="parentId">The parent Id to initialize the created Activity object with.</param>
            <param name="tags">The optional tags list to initialize the created Activity object with.</param>
            <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink"/> list to initialize the created Activity object with.</param>
            <param name="idFormat">The default Id format to use.</param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any listener.</returns>
            <remarks>
            If the Activity object is created, it will not start automatically. Callers need to call <see cref="M:System.Diagnostics.Activity.Start"/> to start it.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind)">
            <summary>
            Creates and starts a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity, returns null otherwise.
            </summary>
            <param name="name">The operation name of the Activity</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any event listener.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind,System.Diagnostics.ActivityContext,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.DateTimeOffset)">
            <summary>
            Creates and starts a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity events, returns null otherwise.
            </summary>
            <param name="name">The operation name of the Activity.</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <param name="parentContext">The parent <see cref="T:System.Diagnostics.ActivityContext"/> object to initialize the created Activity object with.</param>
            <param name="tags">The optional tags list to initialize the created Activity object with.</param>
            <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink"/> list to initialize the created Activity object with.</param>
            <param name="startTime">The optional start timestamp to set on the created Activity object.</param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any listener.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.String,System.Diagnostics.ActivityKind,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.DateTimeOffset)">
            <summary>
            Creates and starts a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity events, returns null otherwise.
            </summary>
            <param name="name">The operation name of the Activity.</param>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <param name="parentId">The parent Id to initialize the created Activity object with.</param>
            <param name="tags">The optional tags list to initialize the created Activity object with.</param>
            <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink"/> list to initialize the created Activity object with.</param>
            <param name="startTime">The optional start timestamp to set on the created Activity object.</param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any listener.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.StartActivity(System.Diagnostics.ActivityKind,System.Diagnostics.ActivityContext,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Collections.Generic.IEnumerable{System.Diagnostics.ActivityLink},System.DateTimeOffset,System.String)">
            <summary>
            Creates and starts a new <see cref="T:System.Diagnostics.Activity"/> object if there is any listener to the Activity events, returns null otherwise.
            </summary>
            <param name="kind">The <see cref="T:System.Diagnostics.ActivityKind"/></param>
            <param name="parentContext">The parent <see cref="T:System.Diagnostics.ActivityContext"/> object to initialize the created Activity object with.</param>
            <param name="tags">The optional tags list to initialize the created Activity object with.</param>
            <param name="links">The optional <see cref="T:System.Diagnostics.ActivityLink"/> list to initialize the created Activity object with.</param>
            <param name="startTime">The optional start timestamp to set on the created Activity object.</param>
            <param name="name">The operation name of the Activity.</param>
            <returns>The created <see cref="T:System.Diagnostics.Activity"/> object or null if there is no any listener.</returns>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.Dispose">
            <summary>
            Dispose the ActivitySource object and remove the current instance from the global list. empty the listeners list too.
            </summary>
        </member>
        <member name="M:System.Diagnostics.ActivitySource.AddActivityListener(System.Diagnostics.ActivityListener)">
            <summary>
            Add a listener to the <see cref="T:System.Diagnostics.Activity"/> starting and stopping events.
            </summary>
            <param name="listener"> The <see cref="T:System.Diagnostics.ActivityListener"/> object to use for listening to the <see cref="T:System.Diagnostics.Activity"/> events.</param>
        </member>
        <member name="T:System.Diagnostics.DistributedContextPropagator">
            <summary>
            An implementation of DistributedContextPropagator determines if and how distributed context information is encoded and decoded as it traverses the network.
            The encoding can be transported over any network protocol that supports string key-value pairs. For example when using HTTP, each key value pair is an HTTP header.
            DistributedContextPropagator inject values into and extracts values from carriers as string key/value pairs.
            </summary>
        </member>
        <member name="T:System.Diagnostics.DistributedContextPropagator.PropagatorGetterCallback">
            <summary>
            The callback that is used in propagators' extract methods. The callback is invoked to lookup the value of a named field.
            </summary>
            <param name="carrier">Carrier is the medium used by Propagators to read values from.</param>
            <param name="fieldName">The propagation field name.</param>
            <param name="fieldValue">An output string to receive the value corresponds to the input fieldName. This should return non null value if there is only one value for the input field name.</param>
            <param name="fieldValues">An output collection of strings to receive the values corresponds to the input fieldName. This should return non null value if there are more than one value for the input field name.</param>
        </member>
        <member name="T:System.Diagnostics.DistributedContextPropagator.PropagatorSetterCallback">
            <summary>
            The callback that is used in propagators' inject methods. This callback is invoked to set the value of a named field.
            Propagators may invoke it multiple times in order to set multiple fields.
            </summary>
            <param name="carrier">Carrier is the medium used by Propagators to write values to.</param>
            <param name="fieldName">The propagation field name.</param>
            <param name="fieldValue">The value corresponds to the input fieldName. </param>
        </member>
        <member name="P:System.Diagnostics.DistributedContextPropagator.Fields">
            <summary>
            The set of field names this propagator is likely to read or write.
            </summary>
            <returns>Returns list of fields that will be used by the DistributedContextPropagator.</returns>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.Inject(System.Diagnostics.Activity,System.Object,System.Diagnostics.DistributedContextPropagator.PropagatorSetterCallback)">
            <summary>
            Injects the trace values stroed in the <see cref="T:System.Diagnostics.Activity"/> object into a carrier. For example, into the headers of an HTTP request.
            </summary>
            <param name="activity">The Activity object has the distributed context to inject to the carrier.</param>
            <param name="carrier">Carrier is the medium in which the distributed context will be stored.</param>
            <param name="setter">The callback will be invoked to set a named key/value pair on the carrier.</param>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.ExtractTraceIdAndState(System.Object,System.Diagnostics.DistributedContextPropagator.PropagatorGetterCallback,System.String@,System.String@)">
            <summary>
            Extracts trace Id and trace state from an incoming request represented by the carrier. For example, from the headers of an HTTP request.
            </summary>
            <param name="carrier">Carrier is the medium from which values will be read.</param>
            <param name="getter">The callback will be invoked to get the propagation trace Id and trace state from carrier.</param>
            <param name="traceId">The extracted trace Id from the carrier.</param>
            <param name="traceState">The extracted trace state from the carrier.</param>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.ExtractBaggage(System.Object,System.Diagnostics.DistributedContextPropagator.PropagatorGetterCallback)">
            <summary>
            Extracts the baggage key-value pair list from an incoming request represented by the carrier. For example, from the headers of an HTTP request.
            </summary>
            <param name="carrier">Carrier is the medium from which values will be read.</param>
            <param name="getter">The callback will be invoked to get the propagation baggage list from carrier.</param>
            <returns>Returns the extracted key-value pair list from the carrier.</returns>
        </member>
        <member name="P:System.Diagnostics.DistributedContextPropagator.Current">
            <summary>
            Get or set the process wide propagator object which used as the current selected propagator.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.CreateDefaultPropagator">
            <summary>
            returns the default propagator object which Current property will be initialized with.
            </summary>
            <remarks>
            CreateDefaultPropagator will create a propagator instance that can inject and extract the headers with field names "tracestate",
            "traceparent" of the identifiers which are formatted as W3C trace parent, "Request-Id" of the identifiers which are formatted as a hierarchical identifier.
            The returned propagator can inject the baggage key-value pair list with header name "Correlation-Context" and it can extract the baggage values mapped to header names "Correlation-Context" and "baggage".
            </remarks>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.CreatePassThroughPropagator">
            <summary>
            Returns a propagator which attempts to act transparently, emitting the same data on outbound network requests that was received on the in-bound request.
            When encoding the outbound message, this propagator uses information from the request's root Activity, ignoring any intermediate Activities that may have been created while processing the request.
            </summary>
        </member>
        <member name="M:System.Diagnostics.DistributedContextPropagator.CreateNoOutputPropagator">
            <summary>
            Returns a propagator which does not transmit any distributed context information in outbound network messages.
            </summary>
        </member>
        <member name="T:System.Diagnostics.RandomNumberGenerator">
            <summary>
            RandomNumberGenerator implementation is the 64-bit random number generator based on the Xoshiro256StarStar algorithm (known as shift-register generators).
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.AggregatorStore`1">
            <summary>
            AggregatorStore is a high performance map from an unordered list of labels (KeyValuePairs) to an instance of TAggregator
            </summary>
            <typeparam name="TAggregator">The type of Aggregator returned by the store</typeparam>
        </member>
        <member name="T:System.Diagnostics.Metrics.Counter`1">
            <summary>
            The counter is an instrument that supports adding non-negative values. For example you might call
            counter.Add(1) each time a request is processed to track the total number of requests. Most metric viewers
            will display counters using a rate by default (requests/sec) but can also display a cumulative total.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0)">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
            <param name="tag">A key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
            <param name="tag3">A third key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.ReadOnlySpan{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
            <param name="tags">A span of key-value pair tags associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The increment measurement.</param>
            <param name="tags">A list of key-value pair tags associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Counter`1.Add(`0,System.Diagnostics.TagList@)">
            <summary>
            Record the increment value of the measurement.
            </summary>
            <param name="delta">The measurement value.</param>
            <param name="tagList">A <see cref="T:System.Diagnostics.TagList" /> of tags associated with the measurement.</param>
        </member>
        <member name="T:System.Diagnostics.Metrics.Histogram`1">
            <summary>
            The histogram is a metrics Instrument which can be used to report arbitrary values that are likely to be statistically meaningful.
            e.g. the request duration.
            Use <see cref="M:System.Diagnostics.Metrics.Meter.CreateHistogram``1(System.String,System.String,System.String)" /> method to create the Histogram object.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0)">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tag">A key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
            <param name="tag3">A third key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.ReadOnlySpan{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tags">A span of key-value pair tags associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tags">A list of key-value pair tags associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Histogram`1.Record(`0,System.Diagnostics.TagList@)">
            <summary>
            Record a measurement value.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tagList">A <see cref="T:System.Diagnostics.TagList" /> of tags associated with the measurement.</param>
        </member>
        <member name="T:System.Diagnostics.Metrics.Instrument">
            <summary>
            Base class of all Metrics Instrument classes
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument.#ctor(System.Diagnostics.Metrics.Meter,System.String,System.String,System.String)">
            <summary>
            Protected constructor to initialize the common instrument properties like the meter, name, description, and unit.
            All classes extending Instrument need to call this constructor when constructing object of the extended class.
            </summary>
            <param name="meter">The meter that created the instrument.</param>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument.Publish">
            <summary>
            Publish is activating the instrument to start recording measurements and to allow listeners to start listening to such measurements.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.Meter">
            <summary>
            Gets the Meter which created the instrument.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.Name">
            <summary>
            Gets the instrument name.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.Description">
            <summary>
            Gets the instrument description.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.Unit">
            <summary>
            Gets the instrument unit of measurements.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.Enabled">
            <summary>
            Checks if there is any listeners for this instrument.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Instrument.IsObservable">
            <summary>
            A property tells if the instrument is an observable instrument.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.Instrument`1">
            <summary>
            Instrument{T} is the base class for all non-observable instruments.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
            <summary>
            Instrument{T} is the base class from which all non-observable instruments will inherit from.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.#ctor(System.Diagnostics.Metrics.Meter,System.String,System.String,System.String)">
            <summary>
            Create the metrics instrument using the properties meter, name, description, and unit.
            All classes extending Instrument{T} need to call this constructor when constructing object of the extended class.
            </summary>
            <param name="meter">The meter that created the instrument.</param>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0)">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0,System.ReadOnlySpan{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
            <param name="tags">A span of key-value pair tags associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
            <param name="tag">A key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object},System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
            <param name="tag1">A first key-value pair tag associated with the measurement.</param>
            <param name="tag2">A second key-value pair tag associated with the measurement.</param>
            <param name="tag3">A third key-value pair tag associated with the measurement.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Instrument`1.RecordMeasurement(`0,System.Diagnostics.TagList@)">
            <summary>
            Record the measurement by notifying all <see cref="T:System.Diagnostics.Metrics.MeterListener" /> objects which listening to this instrument.
            </summary>
            <param name="measurement">The measurement value.</param>
            <param name="tagList">A <see cref="T:System.Diagnostics.TagList" /> of tags associated with the measurement.</param>
        </member>
        <member name="T:System.Diagnostics.Metrics.Measurement`1">
            <summary>
            Measurement stores one observed metrics value and its associated tags. This type is used by Observable instruments' Observe() method when reporting current measurements.
            with the associated tags.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.Measurement`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the Measurement using the value and the list of tags.
            </summary>
            <param name="value">The measurement value.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Measurement`1.#ctor(`0,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Initializes a new instance of the Measurement using the value and the list of tags.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tags">The measurement associated tags list.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Measurement`1.#ctor(`0,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Initializes a new instance of the Measurement using the value and the list of tags.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tags">The measurement associated tags list.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Measurement`1.#ctor(`0,System.ReadOnlySpan{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Initializes a new instance of the Measurement using the value and the list of tags.
            </summary>
            <param name="value">The measurement value.</param>
            <param name="tags">The measurement associated tags list.</param>
        </member>
        <member name="P:System.Diagnostics.Metrics.Measurement`1.Tags">
            <summary>
            Gets the measurement tags list.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Measurement`1.Value">
            <summary>
            Gets the measurement value.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.Meter">
            <summary>
            Meter is the class responsible for creating and tracking the Instruments.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.#ctor(System.String)">
            <summary>
            Initializes a new instance of the Meter using the meter name.
            </summary>
            <param name="name">The Meter name.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the Meter using the meter name and version.
            </summary>
            <param name="name">The Meter name.</param>
            <param name="version">The optional Meter version.</param>
        </member>
        <member name="P:System.Diagnostics.Metrics.Meter.Name">
            <summary>
            Returns the Meter name.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.Meter.Version">
            <summary>
            Returns the Meter Version.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateCounter``1(System.String,System.String,System.String)">
            <summary>
            Create a metrics Counter object.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
            <remarks>
            Counter is an Instrument which supports non-negative increments.
            Example uses for Counter: count the number of bytes received, count the number of requests completed, count the number of accounts created, count the number of checkpoints run, and count the number of HTTP 5xx errors.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateHistogram``1(System.String,System.String,System.String)">
            <summary>
            Histogram is an Instrument which can be used to report arbitrary values that are likely to be statistically meaningful. It is intended for statistics such as histograms, summaries, and percentile.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
            <remarks>
            Example uses for Histogram: the request duration and the size of the response payload.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableCounter``1(System.String,System.Func{``0},System.String,System.String)">
            <summary>
            ObservableCounter is an Instrument which reports monotonically increasing value(s) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValue">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" />.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
            <remarks>
            Example uses for ObservableCounter: The number of page faults for each process.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableCounter``1(System.String,System.Func{System.Diagnostics.Metrics.Measurement{``0}},System.String,System.String)">
            <summary>
            ObservableCounter is an Instrument which reports monotonically increasing value(s) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValue">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" /></param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
            <remarks>
            Example uses for ObservableCounter: The number of page faults for each process.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableCounter``1(System.String,System.Func{System.Collections.Generic.IEnumerable{System.Diagnostics.Metrics.Measurement{``0}}},System.String,System.String)">
            <summary>
            ObservableCounter is an Instrument which reports monotonically increasing value(s) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValues">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" />.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
            <remarks>
            Example uses for ObservableCounter: The number of page faults for each process.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableGauge``1(System.String,System.Func{``0},System.String,System.String)">
            <summary>
            ObservableGauge is an asynchronous Instrument which reports non-additive value(s) (e.g. the room temperature - it makes no sense to report the temperature value from multiple rooms and sum them up) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValue">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" />.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableGauge``1(System.String,System.Func{System.Diagnostics.Metrics.Measurement{``0}},System.String,System.String)">
            <summary>
            ObservableGauge is an asynchronous Instrument which reports non-additive value(s) (e.g. the room temperature - it makes no sense to report the temperature value from multiple rooms and sum them up) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValue">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" />.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.CreateObservableGauge``1(System.String,System.Func{System.Collections.Generic.IEnumerable{System.Diagnostics.Metrics.Measurement{``0}}},System.String,System.String)">
            <summary>
            ObservableGauge is an asynchronous Instrument which reports non-additive value(s) (e.g. the room temperature - it makes no sense to report the temperature value from multiple rooms and sum them up) when the instrument is being observed.
            </summary>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="observeValues">The callback to call to get the measurements when the <see cref="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe" /> is called by <see cref="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments" />.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.Meter.Dispose">
            <summary>
            Dispose the Meter which will disable all instruments created by this meter.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.MeasurementCallback`1">
            <summary>
            A delegate to represent the Meterlistener callbacks used in measurements recording operation.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.MeterListener">
            <summary>
            MeterListener is class used to listen to the metrics instrument measurements recording.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.#ctor">
            <summary>
            Creates a MeterListener object.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.MeterListener.InstrumentPublished">
            <summary>
            Callbacks to get notification when an instrument is published.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.MeterListener.MeasurementsCompleted">
            <summary>
            Callbacks to get notification when stopping the measurement on some instrument.
            This can happen when the Meter or the Listener is disposed or calling <see cref="M:System.Diagnostics.Metrics.MeterListener.Dispose" /> on the listener.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.EnableMeasurementEvents(System.Diagnostics.Metrics.Instrument,System.Object)">
            <summary>
            Start listening to a specific instrument measurement recording.
            </summary>
            <param name="instrument">The instrument to listen to.</param>
            <param name="state">A state object which will be passed back to the callback getting measurements events.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.DisableMeasurementEvents(System.Diagnostics.Metrics.Instrument)">
            <summary>
            Stop listening to a specific instrument measurement recording.
            </summary>
            <param name="instrument">The instrument to stop listening to.</param>
            <returns>The state object originally passed to <see cref="M:System.Diagnostics.Metrics.MeterListener.EnableMeasurementEvents(System.Diagnostics.Metrics.Instrument,System.Object)" /> method.</returns>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.SetMeasurementEventCallback``1(System.Diagnostics.Metrics.MeasurementCallback{``0})">
            <summary>
            Sets a callback for a specific numeric type to get the measurement recording notification from all instruments which enabled listening and was created with the same specified numeric type.
            If a measurement of type T is recorded and a callback of type T is registered, that callback will be used.
            </summary>
            <param name="measurementCallback">The callback which can be used to get measurement recording of numeric type T.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.Start">
            <summary>
            Enable the listener to start listening to instruments measurement recording.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.RecordObservableInstruments">
            <summary>
            Calls all Observable instruments which the listener is listening to then calls <see cref="M:System.Diagnostics.Metrics.MeterListener.SetMeasurementEventCallback``1(System.Diagnostics.Metrics.MeasurementCallback{``0})" /> with every collected measurement.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MeterListener.Dispose">
            <summary>
            Disposes the listeners which will stop it from listening to any instrument.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.MetricsEventSource">
             <summary>
             This EventSource is intended to let out-of-process tools (such as dotnet-counters) do
             ad-hoc monitoring for the new Instrument APIs. This source only supports one listener
             at a time. Each new listener will overwrite the configuration about which metrics
             are being collected and the time interval for the collection. In the future it would
             be nice to have support for multiple concurrent out-of-proc tools but EventSource's
             handling of filter arguments doesn't make that easy right now.
            
             Configuration - The EventSource accepts the following filter arguments:
               - SessionId - An arbitrary opaque string that will be sent back to the listener in
               many event payloads. If listener B reconfigures the EventSource while listener A
               is still running it is possible that each of them will observe some of the events
               that were generated using the other's requested configuration. Filtering on sessionId
               allows each listener to ignore those events.
               - RefreshInterval - The frequency in seconds for sending the metric time series data.
               The format is anything parsable using double.TryParse(). Any
               value less than AggregationManager.MinCollectionTimeSecs (currently 0.1 sec) is rounded
               up to the minimum. If not specified the default interval is 1 second.
               - Metrics - A semicolon separated list. Each item in the list is either the name of a
               Meter or 'meter_name\instrument_name'. For example "Foo;System.Runtime\gc-gen0-size"
               would include all instruments in the 'Foo' meter and the single 'gc-gen0-size' instrument
               in the 'System.Runtime' meter.
               - MaxTimeSeries - An integer that sets an upper bound on the number of time series
               this event source will track. Because instruments can have unbounded sets of tags
               even specifying a single metric could create unbounded load without this limit.
               - MaxHistograms - An integer that sets an upper bound on the number of histograms
               this event source will track. This allows setting a tighter bound on histograms
               than time series in general given that histograms use considerably more memory.
             </summary>
        </member>
        <member name="F:System.Diagnostics.Metrics.MetricsEventSource.Keywords.Messages">
            <summary>
            Indicates diagnostics messages from MetricsEventSource should be included.
            </summary>
        </member>
        <member name="F:System.Diagnostics.Metrics.MetricsEventSource.Keywords.TimeSeriesValues">
            <summary>
            Indicates that all the time series data points should be included
            </summary>
        </member>
        <member name="F:System.Diagnostics.Metrics.MetricsEventSource.Keywords.InstrumentPublishing">
            <summary>
            Indicates that instrument published notifications should be included
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MetricsEventSource.Message(System.String)">
            <summary>
            Used to send ad-hoc diagnostics to humans.
            </summary>
        </member>
        <member name="M:System.Diagnostics.Metrics.MetricsEventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
            <summary>
            Called when the EventSource gets a command from a EventListener or ETW.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.ObservableCounter`1">
            <summary>
            ObservableCounter is a metrics observable Instrument which reports monotonically increasing value(s) when the instrument is being observed.
            e.g. CPU time (for different processes, threads, user mode or kernel mode).
            Use Meter.CreateObservableCounter methods to create the observable counter object.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.ObservableCounter`1.Observe">
            <summary>
            Observe() fetches the current measurements being tracked by this observable counter.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.ObservableGauge`1">
            <summary>
            ObservableGauge is an observable Instrument that reports non-additive value(s) when the instrument is being observed.
            e.g. the current room temperature
            Use Meter.CreateObservableGauge methods to create the observable counter object.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.ObservableGauge`1.Observe">
            <summary>
            Observe() fetches the current measurements being tracked by this observable counter.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Metrics.ObservableInstrument`1">
            <summary>
            ObservableInstrument{T} is the base class from which all metrics observable instruments will inherit from.
            </summary>
            <remarks>
            This class supports only the following generic parameter types: <see cref="T:System.Byte" />, <see cref="T:System.Int16" />, <see cref="T:System.Int32" />, <see cref="T:System.Int64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, and <see cref="T:System.Decimal" />
            </remarks>
        </member>
        <member name="M:System.Diagnostics.Metrics.ObservableInstrument`1.#ctor(System.Diagnostics.Metrics.Meter,System.String,System.String,System.String)">
            <summary>
            Create the metrics observable instrument using the properties meter, name, description, and unit.
            All classes extending ObservableInstrument{T} need to call this constructor when constructing object of the extended class.
            </summary>
            <param name="meter">The meter that created the instrument.</param>
            <param name="name">The instrument name. cannot be null.</param>
            <param name="unit">Optional instrument unit of measurements.</param>
            <param name="description">Optional instrument description.</param>
        </member>
        <member name="M:System.Diagnostics.Metrics.ObservableInstrument`1.Observe">
            <summary>
            Observe() fetches the current measurements being tracked by this instrument. All classes extending ObservableInstrument{T} need to implement this method.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Metrics.ObservableInstrument`1.IsObservable">
            <summary>
            A property tells if the instrument is an observable instrument. This property will return true for all metrics observable instruments.
            </summary>
        </member>
        <member name="T:System.Diagnostics.TagList">
            <summary>
            Represents a list of tags that can be accessed by index. Provides methods to search, sort, and manipulate lists.
            </summary>
            <remarks>
            TagList can be used in the scenarios which need to optimize for memory allocations. TagList will avoid allocating any memory when using up to eight tags.
            Using more than eight tags will cause allocating memory to store the tags.
            Public static (Shared in Visual Basic) members of this type are thread safe. Any instance members are not guaranteed to be thread safe.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.TagList.#ctor(System.ReadOnlySpan{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Initializes a new instance of the TagList structure using the specified <paramref name="tagList" />.
            </summary>
            <param name="tagList">A span of tags to initialize the list with.</param>
        </member>
        <member name="P:System.Diagnostics.TagList.Count">
            <summary>
            Gets the number of tags contained in the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
        </member>
        <member name="P:System.Diagnostics.TagList.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Diagnostics.TagList" /> is read-only. This property will always return <see langword="false" />.
            </summary>
        </member>
        <member name="P:System.Diagnostics.TagList.Item(System.Int32)">
            <summary>
            Gets or sets the tags at the specified index.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index" /> is not a valid index in the <see cref="T:System.Diagnostics.TagList" />.</exception>
        </member>
        <member name="M:System.Diagnostics.TagList.Add(System.String,System.Object)">
            <summary>
            Adds a tag with the provided <paramref name="key" /> and <paramref name="value" /> to the list.
            </summary>
            <param name="key">The tag key.</param>
            <param name="value">The tag value.</param>
        </member>
        <member name="M:System.Diagnostics.TagList.Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Adds a tag to the list.
            </summary>
            <param name="tag">Key and value pair of the tag to add to the list.</param>
        </member>
        <member name="M:System.Diagnostics.TagList.CopyTo(System.Span{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Copies the contents of this  into a destination <paramref name="tags" /> span.
            Inserts an element into this <see cref="T:System.Diagnostics.TagList" /> at the specified index.
            </summary>
            <param name="tags">The destination <see cref="T:System.Span`1" /> object.</param>
            <exception cref="T:System.ArgumentException"> <paramref name="tags" /> The number of elements in the source <see cref="T:System.Diagnostics.TagList" /> is greater than the number of elements that the destination span.</exception>
        </member>
        <member name="M:System.Diagnostics.TagList.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
            <summary>
            Copies the entire <see cref="T:System.Diagnostics.TagList" /> to a compatible one-dimensional array, starting at the specified index of the target array.
            </summary>
            <param name="array">The one-dimensional Array that is the destination of the elements copied from <see cref="T:System.Diagnostics.TagList" />. The Array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException"> <paramref name="array" /> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"> <paramref name="arrayIndex " /> is less than 0 or greater that or equal the <paramref name="array" /> length.</exception>
        </member>
        <member name="M:System.Diagnostics.TagList.Insert(System.Int32,System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Inserts an element into the <see cref="T:System.Diagnostics.TagList" /> at the specified index.
            </summary>
            <param name="index">The zero-based index at which item should be inserted.</param>
            <param name="item">The tag to insert.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"> <paramref name="index" /> index is less than 0 or <paramref name="index" /> is greater than <see cref="M:System.Diagnostics.TagList.Count" />.</exception>
        </member>
        <member name="M:System.Diagnostics.TagList.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the specified index of the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <param name="index">The zero-based index of the element to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"> <paramref name="index" /> index is less than 0 or <paramref name="index" /> is greater than <see cref="M:System.Diagnostics.TagList.Count" />.</exception>
        </member>
        <member name="M:System.Diagnostics.TagList.Clear">
            <summary>
            Removes all elements from the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
        </member>
        <member name="M:System.Diagnostics.TagList.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Determines whether an tag is in the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <param name="item">The tag to locate in the <see cref="T:System.Diagnostics.TagList" />.</param>
            <returns><see langword="true" /> if item is found in the <see cref="T:System.Diagnostics.TagList" />; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:System.Diagnostics.TagList.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <param name="item">The tag to remove from the <see cref="T:System.Diagnostics.TagList" />.</param>
            <returns><see langword="true" /> if item is successfully removed; otherwise, <see langword="false" />. This method also returns <see langword="false" /> if item was not found in the <see cref="T:System.Diagnostics.TagList" />.</returns>
        </member>
        <member name="M:System.Diagnostics.TagList.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <returns>Returns an enumerator that iterates through the <see cref="T:System.Diagnostics.TagList" />.</returns>
        </member>
        <member name="M:System.Diagnostics.TagList.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <returns>Returns an enumerator that iterates through the <see cref="T:System.Diagnostics.TagList" />.</returns>
        </member>
        <member name="M:System.Diagnostics.TagList.IndexOf(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Searches for the specified tag and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Diagnostics.TagList" />.
            </summary>
            <param name="item">The tag to locate in the <see cref="T:System.Diagnostics.TagList" />.</param>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener">
            <summary>
            A HttpHandlerDiagnosticListener is a DiagnosticListener for .NET 4.6 and above where
            HttpClient doesn't have a DiagnosticListener built in. This class is not used for .NET Core
            because HttpClient in .NET Core already emits DiagnosticSource events. This class compensates for
            that in .NET 4.6 and above. HttpHandlerDiagnosticListener has no public constructor. To use this,
            the application just needs to call <see cref="P:System.Diagnostics.DiagnosticListener.AllListeners" /> and
            <see cref="M:System.Diagnostics.DiagnosticListener.AllListenerObservable.Subscribe(System.IObserver{System.Diagnostics.DiagnosticListener})"/>,
            then in the <see cref="M:System.IObserver`1.OnNext(`0)"/> method,
            when it sees the System.Net.Http.Desktop source, subscribe to it. This will trigger the
            initialization of this DiagnosticListener.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Predicate{System.String})">
            <summary>
            Overriding base class implementation just to give us a chance to initialize.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}},System.Func{System.String,System.Object,System.Object,System.Boolean})">
            <summary>
            Overriding base class implementation just to give us a chance to initialize.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.Subscribe(System.IObserver{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
            Overriding base class implementation just to give us a chance to initialize.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.Initialize">
            <summary>
            Initializes all the reflection objects it will ever need. Reflection is costly, but it's better to take
            this one time performance hit than to get it multiple times later, or do it lazily and have to worry about
            threading issues. If Initialize has been called before, it will not doing anything.
            </summary>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener.ServicePointHashtable">
            <summary>
            Helper class used for ServicePointManager.s_ServicePointTable. The goal here is to
            intercept each new ServicePoint object being added to ServicePointManager.s_ServicePointTable
            and replace its ConnectionGroupList hashtable field.
            </summary>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener.ConnectionGroupHashtable">
            <summary>
            Helper class used for ServicePoint.m_ConnectionGroupList. The goal here is to
            intercept each new ConnectionGroup object being added to ServicePoint.m_ConnectionGroupList
            and replace its m_ConnectionList arraylist field.
            </summary>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener.ArrayListWrapper">
            <summary>
            Helper class used to wrap the array list object. This class itself doesn't actually
            have the array elements, but rather access another array list that's given at
            construction time.
            </summary>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener.ConnectionArrayList">
            <summary>
            Helper class used for ConnectionGroup.m_ConnectionList. The goal here is to
            intercept each new Connection object being added to ConnectionGroup.m_ConnectionList
            and replace its m_WriteList arraylist field.
            </summary>
        </member>
        <member name="T:System.Diagnostics.HttpHandlerDiagnosticListener.HttpWebRequestArrayList">
            <summary>
            Helper class used for Connection.m_WriteList. The goal here is to
            intercept all new HttpWebRequest objects being added to Connection.m_WriteList
            and notify the listener about the HttpWebRequest that's about to send a request.
            It also intercepts all HttpWebRequest objects that are about to get removed from
            Connection.m_WriteList as they have completed the request.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.#ctor">
            <summary>
            Private constructor. This class implements a singleton pattern and only this class is allowed to create an instance.
            </summary>
        </member>
        <member name="M:System.Diagnostics.HttpHandlerDiagnosticListener.CreateFieldGetter``1(System.Type,System.String,System.Reflection.BindingFlags)">
            <summary>
            Creates getter for a field defined in private or internal type
            repesented with classType variable
            </summary>
        </member>
        <member name="P:System.HexConverter.CharToHexLookup">
            <summary>Map from an ASCII char to its hex value, e.g. arr['b'] == 11. 0xFF means it's not a hex digit.</summary>
        </member>
        <member name="P:System.SR.ActivityIdFormatInvalid">
            <summary>"Value must be a valid ActivityIdFormat value"</summary>
        </member>
        <member name="P:System.SR.ActivityNotRunning">
            <summary>Trying to set an Activity that is not running</summary>
        </member>
        <member name="P:System.SR.ActivityNotStarted">
            <summary>"Can not stop an Activity that was not started"</summary>
        </member>
        <member name="P:System.SR.ActivityStartAlreadyStarted">
            <summary>"Can not start an Activity that was already started"</summary>
        </member>
        <member name="P:System.SR.EndTimeNotUtc">
            <summary>"EndTime is not UTC"</summary>
        </member>
        <member name="P:System.SR.OperationNameInvalid">
            <summary>"OperationName must not be null or empty"</summary>
        </member>
        <member name="P:System.SR.ParentIdAlreadySet">
            <summary>"ParentId is already set"</summary>
        </member>
        <member name="P:System.SR.ParentIdInvalid">
            <summary>"ParentId must not be null or empty"</summary>
        </member>
        <member name="P:System.SR.SetFormatOnStartedActivity">
            <summary>"Can not change format for an activity that was already started"</summary>
        </member>
        <member name="P:System.SR.SetLinkInvalid">
            <summary>"Can not add link to activity after it has been started"</summary>
        </member>
        <member name="P:System.SR.SetParentIdOnActivityWithParent">
            <summary>"Can not set ParentId on activity which has parent"</summary>
        </member>
        <member name="P:System.SR.StartTimeNotUtc">
            <summary>"StartTime is not UTC"</summary>
        </member>
        <member name="P:System.SR.KeyAlreadyExist">
            <summary>"The collection already contains item with same key '{0}''"</summary>
        </member>
        <member name="P:System.SR.InvalidTraceParent">
            <summary>"Invalid trace parent."</summary>
        </member>
        <member name="P:System.SR.UnableAccessServicePointTable">
            <summary>Unable to access the ServicePointTable field</summary>
        </member>
        <member name="P:System.SR.UnableToInitialize">
            <summary>Unable to initialize all required reflection objects</summary>
        </member>
        <member name="P:System.SR.UnsupportedType">
            <summary>{0} is unsupported type for this operation. The only supported types are byte, short, int, long, float, double, and decimal.</summary>
        </member>
        <member name="P:System.SR.Arg_BufferTooSmall">
            <summary>Destination buffer is not long enough to copy all the items in the list.</summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>

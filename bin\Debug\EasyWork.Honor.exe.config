<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
    </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encoding.CodePages" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <appSettings>
    <!-- 更新服务器配置 -->
    <!-- 推荐使用UNC路径方式访问NAS -->
    <add key="UpdateURL" value="\\172.20.0.20\public\00A-IT信息化\小狗呀\install\"/>

    <!-- 备用HTTP方式（如果UNC不工作） -->
    <!--<add key="UpdateURL" value="http://172.20.0.20:5000/sharing/public/00A-IT信息化/小狗呀/install/"/>-->

    <!-- NAS网络认证配置 -->
    <add key="NasUsername" value="read"/>
    <add key="NasPassword" value="123456"/>
    <add key="NasDomain" value=""/>

    <!-- 更新行为配置 -->
    <add key="SilentUpdate" value="false"/>
    <add key="AutoCheckUpdate" value="true"/>
    <add key="UpdateCheckInterval" value="24"/>
  </appSettings>
</configuration>

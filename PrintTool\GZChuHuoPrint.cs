﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class GZChuHuoPrint : Form
    {
        SerialPort myComPort;
        string ScriptText_zb;
        string ScriptText_zj;
        bool shop = false;
        string text;
        ZPLPrint zb;
        DataTable dt, wdt;
        public GZChuHuoPrint()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            string[] str = SerialPort.GetPortNames();
            if (str != null)
            {
                //添加串口项目  
                foreach (string s in SerialPort.GetPortNames())
                {//获取有多少个COM口  
                    cb_weight_port.Items.Add(s);
                }
                if (cb_weight_port.Items.Count > 0)
                {
                    cb_weight_port.SelectedIndex = 0;
                }
            }
            ScriptText_zb = PrintService.Get_ZPLScript("主板盒标", "ALL");
            ScriptText_zj = PrintService.Get_ZPLScript("主机盒标", "ALL");
        }

        string psid;
        private void bt_Print_Click(object sender, EventArgs e)
        {
            lb_show.Text = "";
            if (bt_weight_openport.Enabled)
            {
                lb_show.Text = "请先打开称的端口";
                return;
            }
            if (tb_PhoneCode.Enabled)
            {
                if (tb_PhoneCode.Text.Trim()=="")
                {
                    tb_PhoneCode.Focus();
                    return;
                }
                if (tb_PhoneCode.Text.Trim().Length != 8 && tb_PhoneCode.Text.Trim().Length != 12)
                {
                    lb_show.Text = "编码必须为8位或12位";
                    tb_PhoneCode.Focus();
                    return;
                }
                if (cb_PhoneCode.Checked)
                {
                    tb_PhoneCode.Enabled = false;
                }
            }

            if (tb_SN.Enabled)
            {
                if (tb_SN.Text.Trim() == "")
                {
                    tb_SN.Focus();
                    return;
                }
                if (!new[] { 12, 14, 15, 16 }.Contains(tb_SN.Text.Trim().Length))
                {
                    lb_show.Text = "MEID必须为12/14/15/16位字符";
                    tb_SN.Focus();
                    return;
                }          

                string wsql = "select *  from MES_Honor.dbo.codingmanage where code='" + tb_PhoneCode.Text.Trim() + "' and codetype='改制'";
                wdt = SqlHelper.Query(wsql).Tables[0];

                tb_SN.Enabled = false;
            }                    

        
            string SqlReprint = string.Format(" select * from mes_honor.dbo.gzmes_log where SN='{0}' and targetcode = '{1}' and approve ='1' ", tb_SN.Text.Trim(), tb_PhoneCode.Text.Trim().ToUpper()), flag = "";
            if (wdt.Rows.Count==0)
            {
                lb_show.Text = "系统没有该编码的重量数据";
                insertChuHuo("",tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                return;
            }     
            if (!shop)
            {
                if (!cb_offline.Checked)
                {
                    string sql = "select * from MES_Honor.dbo.CELT_Data where Product_Barcode='" + tb_SN.Text.Trim() + "'";
                    DataTable celt = SqlHelper.Query(sql).Tables[0];
                    if (celt.Rows.Count == 0)
                    {
                        lb_show.Text = "该SN没有CELT的数据文件";
                        insertChuHuo("", tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }

                    string gzsql = "select * from mes_honor.dbo.gzmes_psid where psid='" + celt.Rows[0]["workorder"].ToString() + "'";
                    DataTable gzpsid = SqlHelper.Query(gzsql).Tables[0];
                    if (gzpsid.Rows.Count == 0)
                    {
                        lb_show.Text = "改制任务令系统没有该数据";
                        insertChuHuo("", tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }

                    if (gzpsid.Rows[0]["targetcode"].ToString().ToUpper() != tb_PhoneCode.Text.Trim().ToUpper())
                    {
                        lb_show.Text = "编码与改制任务令系统不匹配";
                        insertChuHuo(celt.Rows[0]["workorder"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }


                    if (tb_PhoneCode.Text.Trim().ToUpper() != celt.Rows[0]["Item_Sales"].ToString().ToUpper())
                    {
                        lb_show.Text = "编码与CELT系统数据不匹配";
                        insertChuHuo(celt.Rows[0]["workorder"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }

                    text = gzpsid.Rows[0]["targetcode"].ToString().Trim().Substring(0, 1) == "5"
                        ? ScriptText_zj
                        : ScriptText_zb;
                    for (int j = 0; j < 5; j++)
                    {
                        text = text.Replace("$MEID_HEX_14$", celt.Rows[0]["Product_Barcode"].ToString().ToUpper().Trim());
                        text = text.Replace("$Item_BOM$", celt.Rows[0]["Item_Sales"].ToString().ToUpper().Trim());
                        text = text.Replace("$PhoneModel$", wdt.Rows[0]["PhoneModel"].ToString().ToUpper().Trim());
                    }

                    insertChuHuo(celt.Rows[0]["workorder"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "1", "标1", tb_SN.Text.Trim().ToUpper(), "");
                    psid = celt.Rows[0]["workorder"].ToString();
                }
                else
                {
                    if (tb_offline.Text.Trim()=="")
                    {
                        lb_show.Text = "请输入任务令号";
                    }
                    string gzsql = "select * from mes_honor.dbo.gzmes_psid where psid='" + tb_offline.Text.Trim() + "'";
                    DataTable gzpsid = SqlHelper.Query(gzsql).Tables[0];
                    if (gzpsid.Rows.Count == 0)
                    {
                        lb_show.Text = "改制任务令系统没有该数据";
                        insertChuHuo("", tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }
                    string sql = "select * from MES_Honor.dbo.CELT_Data where Product_Barcode='" + tb_offline.Text.Trim() + "'";
                    DataTable celt = SqlHelper.Query(sql).Tables[0];
                    if (gzpsid.Rows[0]["online"].ToString()=="是"||celt.Rows.Count>0)
                    {
                        lb_show.Text = "该任务令为在线任务令";
                        insertChuHuo("", tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }
                    if (gzpsid.Rows[0]["targetcode"].ToString().ToUpper() != tb_PhoneCode.Text.Trim().ToUpper())
                    {
                        lb_show.Text = "编码与改制任务令系统不匹配";
                        insertChuHuo(gzpsid.Rows[0]["psid"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                        return;
                    }

                    text = gzpsid.Rows[0]["targetcode"].ToString().Trim().Substring(0, 1) == "5"
                  ? ScriptText_zj
                  : ScriptText_zb;
                    for (int j = 0; j < 5; j++)
                    {
                        text = text.Replace("$MEID_HEX_14$", tb_SN.Text.ToUpper().Trim());
                        text = text.Replace("$Item_BOM$", tb_PhoneCode.Text.ToUpper().Trim());
                        text = text.Replace("$PhoneModel$", wdt.Rows[0]["PhoneModel"].ToString().ToUpper().Trim());
                    }

                    //if (SqlHelper.Query(SqlReprint + "and OriginalFault = '标1'").Tables[0].Rows.Count > 0)
                    //{
                    //    PrintVerify window = new PrintVerify(this);
                    //    DialogResult result = window.ShowDialog();
                    //    if (result == DialogResult.OK && window.IsPasswordCorrect)
                    //    {
                    //        flag = "[密码打印]";
                    //    }
                    //    else
                    //    {
                    //        lb_show.Text = "打印密码错误！";
                    //        insertChuHuo(gzpsid.Rows[0]["psid"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "0", "[重复打印]"+lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                    //        return;
                    //    }
                    //}

                    insertChuHuo(gzpsid.Rows[0]["psid"].ToString(), tb_PhoneCode.Text.Trim().ToUpper(), "1", flag+"标1", tb_SN.Text.Trim().ToUpper(), "");
                    psid = gzpsid.Rows[0]["psid"].ToString();
                }
                zb = new ZPLPrint();
                zb.ZPL_Print(text);
                shop = true;
            }

            if (tb_Weight.Text.Trim() == ""&&tb_SN.Text.Trim()!="")
            {
                tb_Weight.Focus();
                return;
            }    

            if (wdt.Rows[0]["error"].ToString()=="")
            {
                wdt.Rows[0]["error"] = "0";
            }
            if (decimal.Parse(tb_Weight.Text.Trim()) > decimal.Parse(wdt.Rows[0]["weight"].ToString()) + decimal.Parse(wdt.Rows[0]["error"].ToString()) || decimal.Parse(tb_Weight.Text.Trim()) < decimal.Parse(wdt.Rows[0]["weight"].ToString()) - decimal.Parse(wdt.Rows[0]["error"].ToString()))
            {
                lb_show.Text = string.Format("你的重量为{0},标准重量为{1},误差±{2}", tb_Weight.Text.Trim(), wdt.Rows[0]["weight"].ToString(), wdt.Rows[0]["error"].ToString()); ;
                insertChuHuo(psid,tb_PhoneCode.Text.Trim().ToUpper(), "0", lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                return;
            }
            lb_show.Text = "验证成功";
            if (SqlHelper.Query(SqlReprint + "and OriginalFault = '标2'").Tables[0].Rows.Count > 0)
            {
                PrintVerify window = new PrintVerify(this);
                DialogResult result = window.ShowDialog();
                if (result == DialogResult.OK && window.IsPasswordCorrect)
                {
                    flag = "[密码打印]";
                }
                else
                {
                    lb_show.Text = "打印密码错误！";
                    insertChuHuo(psid, tb_PhoneCode.Text.Trim().ToUpper(), "0", "[重复打印]"+lb_show.Text, tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
                    return;
                }
            }

            insertChuHuo(psid,tb_PhoneCode.Text.Trim().ToUpper(), "1", flag+"标2", tb_SN.Text.Trim().ToUpper(), tb_Weight.Text);
            if (!GZMES_sn(tb_SN.Text.Trim()))
            {
                insertGZMES(psid,tb_PhoneCode.Text.Trim().ToUpper(), tb_SN.Text.Trim().ToUpper());
            }
            else
            {
                updatemes(tb_SN.Text.Trim()) ;
            }
            
            zb = new ZPLPrint();
            zb.ZPL_Print(text);
            bt_clear_Click(null, null);

        }

        public void updatemes(string sn)
        {
            string sql = "update mes_honor.dbo.gzmes set state='Complete',location='Complete' where sn='"+sn+"' ";
            SqlHelper.ExecuteSql(sql);
        }

        public void insertGZMES(string psid,string targetcode, string sn)
        {
            string sql = "insert into mes_honor.dbo.gzmes(targetcode,location,createbyid,createtime,sn,state,psid) values('" + targetcode  + "','Complete','" + Login.usercode + "',getdate(),'" + sn + "','Complete','"+psid+"')";
            SqlHelper.ExecuteSql(sql);
        }

        public void insertChuHuo(string psid,string targetcode,string approve,string OriginalFault,string sn,string weight)
        {
            string sql = "insert into mes_honor.dbo.gzmes_log(targetcode,approve,location,OriginalFault,remarks,operatetype,createbyid,createtime,sn,psid) values('" + targetcode +"','"+approve+"','ChuHuo','"+OriginalFault+"','"+ weight + "','Job','"+Login.usercode+"',getdate(),'"+sn+"','"+psid+"')";
            SqlHelper.ExecuteSql(sql);
        }

        public bool  GZMES_sn(string sn)
        {
            string sql = "select * from  mes_honor.dbo.gzmes where sn='" + sn+"'";
            if (SqlHelper.Query(sql).Tables[0].Rows.Count==0)
            {
                return false;
            }
            return true;
        }

        

        private void tb_PhoneCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {                     
                bt_Print_Click(null, null);
            }
        }

        private void tb_MEID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {     
                bt_Print_Click(null, null);               
            }
        }

        private void tb_VeneerCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {         
                bt_Print_Click(null, null);
            }
        }

        private void tb_Weight_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {             
                bt_Print_Click(null, null);
            }
        }

        private void tb_Weight_KeyPress(object sender, KeyPressEventArgs e)
        {
            string s = "1234567890." + (char)8;
            if (s.IndexOf(e.KeyChar.ToString()) < 0)
            {
                e.Handled = true;
            }
        }

    

        private void bt_weight_openport_Click(object sender, EventArgs e)
        {
            try
            {
                myComPort = new SerialPort(cb_weight_port.Text, 9600, Parity.None);
                myComPort.DataReceived += ReceiveData;
                myComPort.Open();
                bt_weight_openport.Text = "端口已打开";
                bt_weight_openport.Enabled = false;
            }
            catch
            {

            }
        }

        private void ReceiveData(object sender, SerialDataReceivedEventArgs e)
        {
            int n = myComPort.BytesToRead;
            byte[] buf = new byte[n];
            myComPort.Read(buf, 0, n);
            tb_Weight.Invoke
             (new EventHandler(delegate
               {
                   if (!tb_SN.Enabled)
                   {
                       tb_Weight.Text += Regex.Replace(Encoding.ASCII.GetString(buf), @"[^\d.\d]", "");
                   }            
               }));
            if (tb_Weight.Text.Trim().Length==5)
            {
                bt_Print_Click(null, null);
            }
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {
            tb_SN.Text = "";
            tb_Weight.Text = "";
            tb_SN.Enabled = true;
            shop = false;
            tb_SN.Focus();
            if (!cb_PhoneCode.Checked)
            {
                tb_PhoneCode.Text = "";
                tb_offline.Text = "";
                tb_PhoneCode.Enabled = true;
                tb_PhoneCode.Focus();
                return;
            }
          
        }

        private void cb_PhoneCode_CheckedChanged(object sender, EventArgs e)
        {
            if (!cb_PhoneCode.Checked)
            {
                tb_PhoneCode.Enabled = true;
            }
        }

        private void cb_offline_CheckedChanged(object sender, EventArgs e)
        {
            if (cb_offline.Checked)
            {
                tb_offline.Enabled = true;
            }
            else
            {
                tb_offline.Enabled = false;
            }
        }

        private void cb_MD_print_SelectedIndexChanged(object sender, EventArgs e)
        {
            ScriptText_zb = ScriptText_zb.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim()) ;
            ScriptText_zj = ScriptText_zj.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim());
        }
    }
}

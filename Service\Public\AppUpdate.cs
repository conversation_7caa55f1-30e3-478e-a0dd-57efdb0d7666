using System;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Squirrel;

namespace EasyWork.Honor.Service
{
    ///<summary>
    ///客户端自动更新服务 wonder.chia 2025-6
    ///</summary>
    public static class AppUpdate
    {
        #region Windows API for Network Authentication
        [DllImport("mpr.dll")]
        private static extern int WNetAddConnection2(NetResource netResource, string password, string username, int flags);

        [StructLayout(LayoutKind.Sequential)]
        public class NetResource
        {
            public int Scope = 0;
            public int Type = 0;
            public int DisplayType = 0;
            public int Usage = 0;
            public string LocalName = "";
            public string RemoteName = "";
            public string Comment = "";
            public string Provider = "";
        }
        #endregion


        #region this app update methods and properties  

        ///<summary>
        ///检查并执行自动更新
        ///</summary>
        ///<returns></returns>
        public static async Task CheckAndUpdateAsync()
        {
            ProgressForm progressForm = null;
            CancellationTokenSource cancellationTokenSource = null;

            try
            {
                var updateURL = ConfigurationManager.AppSettings["UpdateURL"];
                if (string.IsNullOrEmpty(updateURL))
                {
                    System.Diagnostics.Debug.WriteLine("UpdateURL 未配置");
                    return;
                }

                //设置NAS服务器网络超时
                ServicePointManager.DefaultConnectionLimit = 10;
                ServicePointManager.Expect100Continue = false;

                //处理NAS服务器的身份验证
                await HandleNasAuthentication(updateURL);

                // 验证网络连接
                System.Diagnostics.Debug.WriteLine("验证网络连接...");
                await VerifyNetworkConnection(updateURL);

                using (var mgr = new UpdateManager(updateURL, "EasyWork.Honor"))
                {
                    var updateInfo = await mgr.CheckForUpdate();

                    if (!updateInfo.ReleasesToApply.Any())
                    {
                        System.Diagnostics.Debug.WriteLine("没有可用的更新");
                        return;
                    }

                    var result = MessageBox.Show("发现新版本，是否立即更新？", "更新提示",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;

                    //创建进度窗体和取消令牌
                    cancellationTokenSource = new CancellationTokenSource();
                    progressForm = new ProgressForm("正在下载更新...")
                    {
                        TopMost = true,
                        ShowInTaskbar = false
                    };
                    progressForm.Show();
                    progressForm.BringToFront();

                    //进度更新委托
                    var progress = new Progress<int>(percent =>
                    {
                        UpdateProgressSafely(progressForm, percent);
                    });

                    //启动模拟进度任务
                    var fakeProgressTask = StartFakeProgressTask(progress, cancellationTokenSource.Token);

                    System.Diagnostics.Debug.WriteLine("开始下载更新...");

                    //下载更新包
                    System.Diagnostics.Debug.WriteLine("开始下载更新包...");
                    System.Diagnostics.Debug.WriteLine($"要下载的包数量: {updateInfo.ReleasesToApply.Count()}");

                    foreach (var release in updateInfo.ReleasesToApply)
                    {
                        System.Diagnostics.Debug.WriteLine($"包信息: {release.Filename}, 版本: {release.Version}, 大小: {release.Filesize}");
                    }

                    var downloadTask = mgr.DownloadReleases(
                        updateInfo.ReleasesToApply,
                        percent =>
                        {
                            //真实进度开始时取消模拟进度
                            cancellationTokenSource?.Cancel();
                            System.Diagnostics.Debug.WriteLine($"下载进度: {percent}%");
                            UpdateProgressSafely(progressForm, percent);
                        });

                    // 添加超时处理
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(10)); // 20秒超时
                    var completedTask = await Task.WhenAny(downloadTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        System.Diagnostics.Debug.WriteLine("下载超时！");
                        throw new TimeoutException("下载更新包超时，请检查网络连接");
                    }

                    await downloadTask; // 确保下载任务完成

                    //显示下载完成进度
                    UpdateProgressSafely(progressForm, 100);
                    await Task.Delay(500);

                    System.Diagnostics.Debug.WriteLine("下载完成，开始应用更新...");

                    //关闭进度窗体
                    CloseProgressFormSafely(progressForm);
                    progressForm = null;

                    //应用更新
                    await mgr.ApplyReleases(updateInfo);

                    MessageBox.Show("更新完成，程序将自动重启。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    UpdateManager.RestartApp("-silent");
                }
            }
            catch (WebException webEx)
            {
                System.Diagnostics.Debug.WriteLine($"网络异常: {webEx.Message}");
                ShowErrorMessage($"网络连接失败，请检查网络或NAS连接状态：{webEx.Message}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                ShowErrorMessage($"自动更新失败：{ex.Message}");
            }
            finally
            {
                //资源释放
                cancellationTokenSource?.Cancel();
                cancellationTokenSource?.Dispose();

                if (progressForm != null && !progressForm.IsDisposed)
                {
                    CloseProgressFormSafely(progressForm);
                }
            }
        }

        ///<summary>
        ///启动进度条缓冲任务
        ///</summary>
        private static async Task StartFakeProgressTask(IProgress<int> progress, CancellationToken cancellationToken)
        {
            try
            {
                int fakeProgress = 0;
                int increment = 3;

                while (fakeProgress < 85 && !cancellationToken.IsCancellationRequested)
                {
                    await Task.Delay(800, cancellationToken);
                    fakeProgress += increment;

                    if (fakeProgress > 50)
                        increment = 2;
                    if (fakeProgress > 70)
                        increment = 1;

                    progress?.Report(Math.Min(fakeProgress, 85));
                    System.Diagnostics.Debug.WriteLine($"缓冲进度: {fakeProgress}%");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"进度条缓冲任务异常: {ex.Message}");
            }
        }

        ///<summary>
        ///更新进度条
        ///</summary>
        private static void UpdateProgressSafely(ProgressForm form, int percent)
        {
            if (form?.IsDisposed == false && form.IsHandleCreated)
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.BeginInvoke(new Action(() =>
                        {
                            if (!form.IsDisposed)
                                form.UpdateProgress(percent);
                        }));
                    }
                    else
                    {
                        form.UpdateProgress(percent);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"更新进度异常: {ex.Message}");
                }
            }
        }

        ///<summary>
        ///线程内安全关闭进度窗体
        ///</summary>
        private static void CloseProgressFormSafely(ProgressForm form)
        {
            if (form?.IsDisposed == false)
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.Invoke(new Action(() =>
                        {
                            if (!form.IsDisposed)
                            {
                                form.AllowClose();
                                form.Close();
                                form.Dispose();
                            }
                        }));
                    }
                    else
                    {
                        form.AllowClose();
                        form.Close();
                        form.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"关闭窗体异常: {ex.Message}");
                }
            }
        }

        ///<summary>
        ///显示错误消息
        ///</summary>
        private static void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        ///<summary>
        ///处理 NAS 身份验证
        ///</summary>
        private static async Task VerifyNetworkConnection(string updateURL)
        {
            try
            {
                string uncPath = updateURL.Replace("file://", "").Replace("/", "\\");
                if (!uncPath.StartsWith("\\\\"))
                    uncPath = "\\\\" + uncPath;

                // 检查 RELEASES 文件是否可访问
                string releasesPath = System.IO.Path.Combine(uncPath, "RELEASES");
                System.Diagnostics.Debug.WriteLine($"检查 RELEASES 文件: {releasesPath}");

                if (System.IO.File.Exists(releasesPath))
                {
                    var content = System.IO.File.ReadAllText(releasesPath);
                    System.Diagnostics.Debug.WriteLine($"RELEASES 文件内容: {content}");

                    // 检查引用的包文件是否存在
                    var lines = content.Split('\n');
                    foreach (var line in lines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            var parts = line.Trim().Split(' ');
                            if (parts.Length >= 2)
                            {
                                string packagePath = System.IO.Path.Combine(uncPath, parts[1]);
                                System.Diagnostics.Debug.WriteLine($"检查包文件: {packagePath}");
                                if (System.IO.File.Exists(packagePath))
                                {
                                    System.Diagnostics.Debug.WriteLine($"包文件存在，大小: {new System.IO.FileInfo(packagePath).Length} 字节");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"警告: 包文件不存在: {packagePath}");
                                }
                            }
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"错误: RELEASES 文件不存在: {releasesPath}");
                    throw new Exception($"RELEASES 文件不存在: {releasesPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"网络连接验证失败: {ex.Message}");
                throw;
            }
        }

        private static async Task HandleNasAuthentication(string updateURL)
        {
            try
            {
                //检查是否有效
                if (updateURL.StartsWith("\\\\") || updateURL.StartsWith("file://"))
                {
                    string uncPath = updateURL.Replace("file://", "").Replace("/", "\\");
                    if (!uncPath.StartsWith("\\\\"))
                        uncPath = "\\\\" + uncPath;

                    //从配置文件读取NAS凭据
                    var nasUsername = ConfigurationManager.AppSettings["NasUsername"];
                    var nasPassword = ConfigurationManager.AppSettings["NasPassword"];

                    if (!string.IsNullOrEmpty(nasUsername) && !string.IsNullOrEmpty(nasPassword))
                    {
                        System.Diagnostics.Debug.WriteLine($"尝试连接到NAS服务器: {uncPath}");

                        //建立网络连接
                        var result = ConnectToNetworkPath(uncPath, nasUsername, nasPassword);
                        if (result == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("NAS服务器身份验证成功");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"NAS服务器身份验证失败，错误代码: {result}");
                        }
                    }
                }
                else if (updateURL.StartsWith("http://") || updateURL.StartsWith("https://"))
                {
                    //HTTP 基本身份验证
                    var nasUsername = ConfigurationManager.AppSettings["NasUsername"];
                    var nasPassword = ConfigurationManager.AppSettings["NasPassword"];

                    if (!string.IsNullOrEmpty(nasUsername) && !string.IsNullOrEmpty(nasPassword))
                    {
                        System.Diagnostics.Debug.WriteLine("设置 HTTP 基本身份验证");

                        //设置默认网络凭据
                        var credentials = new NetworkCredential(nasUsername, nasPassword);
                        ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

                        //为 HTTP 请求设置默认凭据和基本认证
                        WebRequest.DefaultWebProxy.Credentials = credentials;

                        //设置全局默认凭据
                        CredentialCache credentialCache = new CredentialCache();
                        Uri uri = new Uri(updateURL);
                        credentialCache.Add(uri, "Basic", credentials);
                        WebRequest.DefaultWebProxy.Credentials = credentialCache;

                        //为HttpWebRequest设置默认凭据
                        WebRequest.DefaultWebProxy.Credentials = credentials;
                    }
                }

                await Task.Delay(100); //给连接一点时间建立
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"NAS服务器身份验证异常: {ex.Message}");
                //不抛出异常，让程序继续尝试更新
            }
        }

        ///<summary>
        ///连接到网络路径
        ///</summary>
        private static int ConnectToNetworkPath(string networkPath, string username, string password)
        {
            try
            {
                //提取服务器路径
                var parts = networkPath.Split(new char[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);
                string serverPath = parts.Length >= 2 ? $"\\\\{parts[0]}\\{parts[1]}" : networkPath;

                var netResource = new NetResource
                {
                    Scope = 2,
                    Type = 1,
                    DisplayType = 3,
                    Usage = 1,
                    RemoteName = serverPath
                };

                return WNetAddConnection2(netResource, password, username, 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"连接网络路径异常: {ex.Message}");
                return -1;
            }
        }
        #endregion
    }
}

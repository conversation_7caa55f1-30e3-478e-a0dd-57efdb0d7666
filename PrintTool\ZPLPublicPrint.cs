﻿using EasyWork.Honor.Service;
using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class ZPLPublicPrint : Form
    {
        public ZPLPublicPrint()
        {
            InitializeComponent();
            cb_time.SelectedIndex = 3;
        }       

        private void bt_AllPrint_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Title = "txt文件";
            ofd.FileName = "";
            ofd.Filter = "文本文件(*.txt)|*.txt";
            ofd.ValidateNames = true;    
            ofd.CheckFileExists = true; 
            ofd.CheckPathExists = true;
            string strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择脚本文件");
                return;
            }
            string ScriptText = System.IO.File.ReadAllText(strName);

            ofd = new OpenFileDialog();
            ofd.Title = "Excel文件";
            ofd.FileName = "";
            ofd.Filter = "Excel文件(*.xlsx)|*.xlsx|Excel低版本文件(*.xls)|*.xls";
            ofd.ValidateNames = true;     //文件有效性验证ValidateNames，验证用户输入是否是一个有效的Windows文件名
            ofd.CheckFileExists = true;  //验证路径有效性
            ofd.CheckPathExists = true; //验证文件有效性

            strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择Excel文件！无法进行数据导入");
                return;
            }

            DataTable dt = PublicService.LoadDataFromExcel(strName);
            if (dt.Rows.Count == 0)
            {
                MessageBox.Show("无数据可导入");
                return;
            }

            foreach (DataRow dr in dt.Rows)
            {
                string text = ScriptText;
                bool shop = false;
                foreach (DataColumn dc in dt.Columns)
                {
                    if (!shop && dr[dc.ColumnName].ToString().ToUpper().Trim()!="")
                    {
                        shop = true;
                    }
                    for (int j = 0; j < 5; j++)
                    {
                        text = text.Replace("$" + dc.ColumnName.Trim() + "$", dr[dc.ColumnName].ToString().ToUpper().Trim());
                    }
                }
                if (!shop)
                {
                    return;
                }
                if (rb_USB.Checked)
                {
                    ZPLPrint zb = new ZPLPrint();
                    zb.ZPL_Print(text);
                }
                else if (rb_LPT.Checked)
                {
                    ZebraGesigner zb = new ZebraGesigner();
                    bool str = zb.Open();
                    zb.Write(text);
                    zb.Close();
                    if (!str)                 
                    {
                        MessageBox.Show("没有连接打机!", "提示");
                        return;
                    }
                }
                if (cb_time.Text!="不限速")
                {
                    Thread.Sleep(int.Parse(cb_time.Text));
                }
            }
        }
        DataTable Sdt;
        string Stext;
        private void bt_InExcel_Click(object sender, EventArgs e)
        {
            cb_PrintName.Items.Clear();
            tb_url.Text = "";
            Sdt = null;

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Title = "txt文件";
            ofd.FileName = "";
            ofd.Filter = "文本文件(*.txt)|*.txt";
            ofd.ValidateNames = true;
            ofd.CheckFileExists = true;
            ofd.CheckPathExists = true;
            string strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择脚本文件");
                return;
            }
            Stext = System.IO.File.ReadAllText(strName);

            ofd = new OpenFileDialog();
            ofd.Title = "Excel文件";
            ofd.FileName = "";
            ofd.Filter = "Excel文件(*.xlsx)|*.xlsx|Excel低版本文件(*.xls)|*.xls";
            ofd.ValidateNames = true;     //文件有效性验证ValidateNames，验证用户输入是否是一个有效的Windows文件名
            ofd.CheckFileExists = true;  //验证路径有效性
            ofd.CheckPathExists = true; //验证文件有效性
            strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择数据文件");
                return;
            }
            tb_url.Text = ofd.FileName;

            Sdt = PublicService.LoadDataFromExcel(strName);
            if (Sdt.Rows.Count == 0)
            {
                MessageBox.Show("无数据可导入");
                return;
            }
            foreach (DataColumn dc in Sdt.Columns)
            {
                cb_PrintName.Items.Add(dc.ColumnName);                
            }
            cb_PrintName.SelectedIndex = 0;
        }

        private void textBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_single_Print_Click(null, null);
            }
        }

        private void bt_single_Print_Click(object sender, EventArgs e)
        {
            lb_show.Text = "";
            DataRow[] drr = Sdt.Select("" + cb_PrintName.Text + "='" + textBox1.Text.Trim() + "'");
            if (drr.Length == 0)
            {
                MessageBox.Show("Excel表里无该" + cb_PrintName.Text + "的" + textBox1.Text.Trim() + "数据");
              //  textBox1.Focus();
                textBox1.SelectAll();
                return;
            }

            foreach (DataRow dr in drr)
            {
                string text = Stext;
                bool shop = false;
                foreach (DataColumn dc in Sdt.Columns)
                {
                    if (!shop && dr[dc.ColumnName].ToString().ToUpper().Trim() != "")
                    {
                        shop = true;
                    }
                    for (int j = 0; j < 5; j++)
                    {
                        text = text.Replace("$" + dc.ColumnName.Trim() + "$", dr[dc.ColumnName].ToString().ToUpper().Trim());
                    }
                }
                if (!shop)
                {
                    return;
                }
                if (rb_USB.Checked)
                {
                    ZPLPrint zb = new ZPLPrint();
                    zb.ZPL_Print(text);
                }
                else if (rb_LPT.Checked)
                {
                    ZebraGesigner zb = new ZebraGesigner();
                    bool str = zb.Open();
                    zb.Write(text);
                    zb.Close();
                    if (!str)
                    {
                        MessageBox.Show("没有连接打机!", "提示");
                        return;
                    }
                }
                lb_show.Text = "打印成功";
                textBox1.Text = "";
                textBox1.Focus();
            }
        }
    }
}

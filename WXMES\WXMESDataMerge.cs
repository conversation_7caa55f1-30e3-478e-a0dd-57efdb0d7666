﻿using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.ServiceModel.Dispatcher;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class WXMESDataMerge : Form
    {
        DataTable wxmes = null;
        public WXMESDataMerge()
        {
            InitializeComponent();
        }

        private void btAddData_Click(object sender, EventArgs e)
        {
            //打开一个文件选择框
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Title = "Excel文件";
            ofd.FileName = "";
            ofd.Filter = "Excel文件(*.xlsx)|*.xlsx|Excel低版本文件(*.xls)|*.xls";
            ofd.ValidateNames = true;     //文件有效性验证ValidateNames，验证用户输入是否是一个有效的Windows文件名
            ofd.CheckFileExists = true;  //验证路径有效性
            ofd.CheckPathExists = true; //验证文件有效性

            string strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择Excel文件！无法进行数据导入");
                return;
            }

            DataTable dt = PublicService.LoadDataFromExcel(strName);
            if (dt.Rows.Count == 0)
            {
                MessageBox.Show("无数据可导入");
                return;
            }

            if (wxmes == null)
            {
                wxmes = dt;
            }
            else
            {
                wxmes.Merge(dt, false, MissingSchemaAction.AddWithKey);
            }


        }

        private void btOutExcelWXMES_Click(object sender, EventArgs e)
        {
            DataTable dtt = wxmes;            
            string psid = null;
            foreach (DataRow item in dtt.Rows)
            {
                if (item["用户描述故障"].ToString().Trim()== "")
                {
                    item["用户描述故障"] = "其他";
                }
                if (item["目标编码类型"].ToString().Trim() == "备板")
                {
                    item["目标编码类型"] = "主板";
                }
                if (psid == null)
                {
                    psid = "'"+item["任务令"].ToString()+"'";
                    continue;
                }

                string[] st = psid.Split(',');
                bool shop = false;
                foreach (object o in st)
                {
                    if ("'"+item["任务令"].ToString().ToUpper()+ "'" == o.ToString().ToUpper())
                    {
                        shop = true;
                        break;
                    }                    
                }
                if (!shop)
                {
                    psid += ",'" + item["任务令"].ToString()+ "'";
                }
            }

            string sql = "select * from MES_Honor.dbo.PsidTracking where psid in("+psid+")";
            DataTable dt = SqlHelper.Query(sql).Tables[0];


            dtt.Columns["产品型号"].ColumnName = "机型";
            dtt.Columns["目标编码类型"].ColumnName = "出货类型";
            dtt.Columns["业务类别"].ColumnName = "类型";
            dtt.Columns["原始SN"].ColumnName = "SN";
            dtt.Columns["原单板SN"].ColumnName = "单板号";
            dtt.Columns["用户描述故障"].ColumnName = "来料故障";
            dtt.Columns["任务令备注信息"].ColumnName = "备注";
            dtt.Columns["原物理号"].ColumnName = "旧主物理号";
            dtt.Columns["最后打印时间"].ColumnName = "下达日期";
            dtt.Columns["注册时间"].ColumnName = "荣耀排产日期";
            dtt.Columns["最后打印人"].ColumnName = "荣耀完成日期";
            dtt.Columns["新单板条码"].ColumnName = "来料编码";
            dtt.Columns.Remove("来料故障信息"); 
            dtt.Columns.Remove("注册状态");
            dtt.Columns.Remove("物品状态");
            dtt.Columns.Remove("注册失败备注");
            dtt.Columns.Remove("打印次数");
            foreach (DataRow item in dtt.Rows)
            {
                DataRow[] drr = dt.Select("psid='"+item["任务令"].ToString()+"'");
                if (drr.Length==0)
                {
                    continue;
                }
                item["来料编码"] = drr[0]["InitialCode"].ToString();
                item["下达日期"] = drr[0]["IssuedTime"].ToString();
                item["荣耀排产日期"] = drr[0]["PutTime"].ToString();
                item["荣耀完成日期"] = drr[0]["CompleteTime"].ToString();
                item["类型"] = drr[0]["type"].ToString();
            }

            dataGridView1.DataSource = dtt;
            ExportToExcel p = new ExportToExcel();
            p.OutputAsExcelFile(dataGridView1);
            
        }

        private void btclear_Click(object sender, EventArgs e)
        {
            wxmes = null;
            dataGridView1.DataSource = null;
        }
    }
}

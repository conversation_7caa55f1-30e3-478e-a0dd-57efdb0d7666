﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Honor.Service.WXMES
{
    public class WXMESQuery
    {



        /// <summary>
        /// 查询基础数据
        /// </summary>
        /// <param name="oldimei">新IMEI</param>
        /// <param name="veneercode">单板号</param>
        /// <returns>dt</returns>
        public static DataTable QueryBasicData_New(string newimei, string veneercode)
        {
            string sql = "select top 1 * from WXMES where new_imei='" + newimei + "' and veneercode='" + veneercode + "' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询基础数据
        /// </summary>
        /// <param name="oldimei">新IMEI</param>
        /// <returns>dt</returns>
        public static DataTable QueryBasicData_New(string newimei)
        {
            string sql = "select top 1 * from WXMES where new_imei='" + newimei + "' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询编码信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static DataTable QueryCodeData(string code,string type)
        {
            string sql = "select * from CodingManage  where Code='" + code + "' and codetype='"+type+"'";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询错误代码
        /// </summary>
        /// <param name="errnumber">错误代码</param>
        /// <returns></returns>
        public static DataTable QueryFaultCode(string errnumber)
        {
            string sql2 = "select * from WXMES_FaultCode where code='" + errnumber + "'";
            return SqlHelperMES.Query(sql2).Tables[0];
        }

        /// <summary>
        /// 查询CELT的数据
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        public static DataTable QueryCELT(string sn)
        {
            string sql = "select * from MES_Honor.dbo.CELT_Data where Product_Barcode='"+sn+"'";
            return SqlHelper.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询BOM信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static DataTable QueryWXBOM(string code, string type)
        {
            string sql = "select * from bom  where Code='" + code + "' and type='" + type + "'";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询基础数据
        /// </summary>
        /// <param name="psid">PSID</param>
        /// <returns>dt</returns>
        public static DataTable GetPsid(string psid)
        {
            string sql = "select * from WXMES where psid='" + psid + "' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询基础数据
        /// </summary>
        /// <param name="psid">PSID</param>
        /// <returns>dt</returns>
        public static DataTable GetPsidVeneerCode(string psid,string veneercode)
        {
            string sql = "select * from WXReport where psid='" + psid + "' and veneercode='"+ veneercode + "' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询维修报表最后导入的结单时间
        /// </summary>
        /// <returns>dt</returns>
        public static DateTime GetWXReportLastTime()
        {
            string sql = "select top 1 completetime from WXReport order by CompleteTime desc";
            return DateTime.Parse(SqlHelperMES.Query(sql).Tables[0].Rows[0]["CompleteTime"].ToString());
        }


        /// <summary>
        /// 查询CELT
        /// </summary>
        /// <param name="psid">psid</param>
        /// <returns>dt</returns>
        public static DataTable GetCELT(string psid)
        {
            string sql = "select * from celt_data where workorder='" + psid + "' and state='Normal' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询CELT
        /// </summary>
        /// <param name="meid">meid</param>
        /// <returns>dt</returns>
        public static DataTable GetCELT_Meid(string meid)
        {
            string sql = "select  * from celt_data where PHYSICSNO='" + meid + "' and state='Normal' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

        /// <summary>
        /// 查询申请单, approve:1号可使用,2为已使用,0为取消使用
        /// </summary>
        /// <param name="code">code</param>
        /// <returns>dt</returns>
        public static DataTable GetOrderApply(string code,string type)
        {
            string sql = "select  * from OrderApply where code='" + code + "' and approve='1' order by id desc";
            return SqlHelperMES.Query(sql).Tables[0];
        }

    }
}

@echo off
setlocal enabledelayedexpansion

REM Configuration
set VERSION=1.2.2
set PROJECT_NAME=EasyWork.Honor
set PROJECT_FILE=EasyWork.Honor.csproj
set NUSPEC_FILE=EasyWork.Honor.nuspec
set RELEASE_DIR=bin\Release
set SQUIRREL_TOOLS=..\packages\squirrel.windows.2.0.1\tools

echo [INFO] Starting build and package process for v%VERSION%

echo [STEP1] Clean and build project...
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR% >nul 2>&1

echo [INFO] Building project...
dotnet build %PROJECT_FILE% -c Release
if errorlevel 1 (
    echo [WARNING] dotnet build failed, trying alternative method...
    echo [INFO] Please build the project manually in Visual Studio first
    echo [INFO] Then run this script again
    goto :end
)

echo [STEP2] Verify build output...
if not exist %RELEASE_DIR%\%PROJECT_NAME%.exe (
    echo [ERROR] Main executable not found after build
    goto :end
)
echo [INFO] Build output verified

echo [STEP3] Create NuGet package...
where nuget >nul 2>&1
if errorlevel 1 (
    echo [WARNING] nuget.exe not found in PATH
    echo [INFO] Trying to download nuget.exe...
    powershell -Command "Invoke-WebRequest -Uri 'https://dist.nuget.org/win-x86-commandline/latest/nuget.exe' -OutFile 'nuget.exe'"
    if not exist nuget.exe (
        echo [ERROR] Failed to download nuget.exe
        echo [INFO] Please download nuget.exe manually and add to PATH
        goto :end
    )
    set NUGET_CMD=nuget.exe
) else (
    set NUGET_CMD=nuget
)

echo [INFO] Creating NuGet package...
%NUGET_CMD% pack %NUSPEC_FILE% -Version %VERSION% -OutputDirectory %RELEASE_DIR%
if errorlevel 1 (
    echo [ERROR] NuGet pack failed
    goto :end
)

set PACKAGE_FILE=%RELEASE_DIR%\%PROJECT_NAME%.%VERSION%.nupkg
if not exist %PACKAGE_FILE% (
    echo [ERROR] NuGet package not created: %PACKAGE_FILE%
    goto :end
)
echo [INFO] NuGet package created: %PACKAGE_FILE%

echo [STEP4] Generate Squirrel installer...
pushd %RELEASE_DIR%

if exist Setup.exe del Setup.exe >nul 2>&1
if exist RELEASES del RELEASES >nul 2>&1
if exist %PROJECT_NAME%-*.delta del %PROJECT_NAME%-*.delta >nul 2>&1

echo [INFO] Running Squirrel releasify...
..\..\%SQUIRREL_TOOLS%\Squirrel.exe --releasify %PROJECT_NAME%.%VERSION%.nupkg --releaseDir=.
if errorlevel 1 (
    echo [ERROR] Squirrel releasify failed
    popd
    goto :end
)

if not exist Setup.exe (
    echo [ERROR] Setup.exe not generated
    popd
    goto :end
)

if not exist RELEASES (
    echo [ERROR] RELEASES file not generated
    popd
    goto :end
)

echo [INFO] Squirrel installer generated successfully
popd

echo [STEP5] Verify final output...
echo [INFO] Generated files:
if exist %RELEASE_DIR%\%PROJECT_NAME%.%VERSION%.nupkg echo   - %PROJECT_NAME%.%VERSION%.nupkg
if exist %RELEASE_DIR%\Setup.exe echo   - Setup.exe
if exist %RELEASE_DIR%\RELEASES echo   - RELEASES

echo.
echo [SUCCESS] Build and package process completed!
echo [INFO] Files are ready for publishing in: %RELEASE_DIR%
echo.
echo [NEXT] You can now run: publish.bat

goto :end

:end
echo Press any key to exit...
pause >nul

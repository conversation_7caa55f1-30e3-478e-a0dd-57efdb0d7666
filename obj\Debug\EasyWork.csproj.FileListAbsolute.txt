C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\EasyWork.exe.config
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\EasyWork.exe
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\EasyWork.pdb
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.MDI.resources
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.Properties.Resources.resources
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.GenerateResource.cache
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.exe
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.pdb
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.csprojAssemblyReference.cache
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\SideBar.dll
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.CopyComplete
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\DevComponents.DotNetBar2.dll
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.Login.resources
C:\Users\<USER>\source\repos\EasyWork\EasyWork\obj\Debug\EasyWork.Manage.ChangePWD.resources
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.dll
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\WebDriver.dll
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\WebDriver.Support.dll
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.xml
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\WebDriver.xml
C:\Users\<USER>\source\repos\EasyWork\EasyWork\bin\Debug\WebDriver.Support.xml
E:\workspace\EasyWork\EasyWork\bin\Debug\EasyWork.exe.config
E:\workspace\EasyWork\EasyWork\bin\Debug\EasyWork.exe
E:\workspace\EasyWork\EasyWork\bin\Debug\EasyWork.pdb
E:\workspace\EasyWork\EasyWork\bin\Debug\DevComponents.DotNetBar2.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\SideBar.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\WebDriver.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\WebDriver.Support.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.xml
E:\workspace\EasyWork\EasyWork\bin\Debug\WebDriver.xml
E:\workspace\EasyWork\EasyWork\bin\Debug\WebDriver.Support.xml
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.csprojAssemblyReference.cache
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Login.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Manage.ChangePWD.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.MDI.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Properties.Resources.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.GenerateResource.cache
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.CopyComplete
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.exe
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.pdb
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMessage.resources
E:\workspace\EasyWork\EasyWork\bin\Debug\Newtonsoft.Json.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\Newtonsoft.Json.xml
E:\workspace\EasyWork\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\System.Text.Encoding.CodePages.dll
E:\workspace\EasyWork\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Message_log.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.DataValJob.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMsg.HPD.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.others.HiPhoneDoctor.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Public.Assessment.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.Public.HRtraining_log.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\Bravo.others.ShowHRtraining_log.resources
E:\workspace\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMsg.ShowText.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.csprojAssemblyReference.cache
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Login.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Manage.ChangePWD.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.MDI.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Message_log.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Public.Assessment.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Public.HRtraining_log.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\Bravo.others.ShowHRtraining_log.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.ShowMessage.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Properties.Resources.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.ShowMsg.HPD.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.ShowMsg.ShowText.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.WXMES.DataValJob.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.csproj.GenerateResource.cache
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.exe
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.pdb
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\EasyWork.exe.config
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\EasyWork.exe
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\EasyWork.pdb
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\DevComponents.DotNetBar2.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Newtonsoft.Json.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\SideBar.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\System.Text.Encoding.CodePages.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\WebDriver.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\WebDriver.Support.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Newtonsoft.Json.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\WebDriver.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\WebDriver.Support.xml
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.csproj.CopyComplete
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.GZMES.DataValJob.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.Form1.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.csproj.CoreCompileInputs.cache
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.PrintTool.CartonMerge.resources
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Aspose.Cells.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\Aspose.Cells.xml
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.GZMES.GZLogVal.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.GZMES.ReadPhoneVal.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.others.HiPhoneDoctor_old.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.WXMES.HiPhoneDoctor.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.WXMES.ISeeTerminalTestJob.resources
E:\workspace\EasyWork\trunk\EasyWork\obj\Debug\EasyWork.DataProcessing.BreakMaterial.resources
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Controls.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Imaging.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Math.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Video.DirectShow.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Video.dll
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Controls.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Imaging.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Math.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Video.xml
E:\workspace\EasyWork\trunk\EasyWork\bin\Debug\AForge.Video.DirectShow.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\EasyWork.exe.config
E:\SVN\EasyWork\EasyWork\bin\Debug\EasyWork.exe
E:\SVN\EasyWork\EasyWork\bin\Debug\EasyWork.pdb
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Controls.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Imaging.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Math.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Video.DirectShow.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Video.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\Aspose.Cells.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\DevComponents.DotNetBar2.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\Newtonsoft.Json.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\SideBar.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\System.Text.Encoding.CodePages.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\WebDriver.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\WebDriver.Support.dll
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Controls.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Imaging.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Math.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Video.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\AForge.Video.DirectShow.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\Aspose.Cells.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\Newtonsoft.Json.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\WebDriver.xml
E:\SVN\EasyWork\EasyWork\bin\Debug\WebDriver.Support.xml
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.csprojAssemblyReference.cache
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.DataProcessing.BreakMaterial.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Form1.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.GZMES.GZLogVal.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.GZMES.ReadPhoneVal.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Login.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Manage.ChangePWD.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.MDI.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Message_log.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.PrintTool.CartonMerge.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Public.Assessment.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Public.HRtraining_log.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\Bravo.others.ShowHRtraining_log.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMessage.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.Properties.Resources.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMsg.HPD.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.ShowMsg.ShowText.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.GZMES.DataValJob.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.DataValJob.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.others.HiPhoneDoctor_old.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.HiPhoneDoctor.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.ISeeTerminalTestJob.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.GenerateResource.cache
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.CoreCompileInputs.cache
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.csproj.CopyComplete
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.exe
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.pdb
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.BSNValJob.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.OQCJob.resources
E:\SVN\EasyWork\EasyWork\obj\Debug\EasyWork.WXMES.BFConfirmJob.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.csprojAssemblyReference.cache
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.Login.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.Manage.ChangePWD.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.MDI.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.PrintTool.CartonMerge.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.Properties.Resources.resources
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.csproj.GenerateResource.cache
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.csproj.CoreCompileInputs.cache
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.exe
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.pdb
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\EasyWork.exe.config
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\EasyWork.exe
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\EasyWork.pdb
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Controls.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Imaging.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Math.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Video.DirectShow.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Video.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Aspose.Cells.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\DevComponents.DotNetBar2.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Newtonsoft.Json.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\SideBar.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\System.Text.Encoding.CodePages.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\WebDriver.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\WebDriver.Support.dll
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Controls.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Imaging.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Math.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Video.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\AForge.Video.DirectShow.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Aspose.Cells.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Newtonsoft.Json.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\Selenium.WebDriverBackedSelenium.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\WebDriver.xml
E:\SVN\EasyWork.Honor\EasyWork\bin\Debug\WebDriver.Support.xml
E:\SVN\EasyWork.Honor\EasyWork\obj\Debug\EasyWork.csproj.CopyComplete

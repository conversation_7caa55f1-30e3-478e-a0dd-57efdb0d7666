﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Honor.Service
{
    public class SqlHelperMES
    {
        //private static string connectionStr2 = "Server=172.20.0.15;uid=mes_honor;password=**************;database=MES_Honor;Trusted_Connection=False;Connect Timeout=100;Min Pool Size=16;Max Pool Size=50";

        private static string connectionStr = "Server=10.10.10.14;uid=SA;password=*****;database=MES_Honor;Trusted_Connection=False;Connect Timeout=100;Min Pool Size=16;Max Pool Size=50";

        //private static string connectionStr = "Server=.;uid=sa;password=**********;database=Ework_honor;Trusted_Connection=False;Connect Timeout=100;Min Pool Size=16;Max Pool Size=50";
        public static int executeNonQuery(string sqlStr, CommandType commandType, SqlParameter[] parameters)
        {
            SqlConnection connection = new SqlConnection(connectionStr);
            SqlCommand command = new SqlCommand();
            prepareCommand(connection, command, sqlStr, commandType, parameters);
            return command.ExecuteNonQuery();
        }

        public static SqlDataReader executeReader(string sqlStr, CommandType commandType, SqlParameter[] parameters)
        {
            SqlConnection connection = new SqlConnection(connectionStr);
            SqlCommand command = new SqlCommand();
            prepareCommand(connection, command, sqlStr, commandType, parameters);
            return command.ExecuteReader(CommandBehavior.CloseConnection);
        }

        private static SqlCommand prepareCommand(SqlConnection connection, SqlCommand command, string sqlStr, CommandType commandType, SqlParameter[] parameters)
        {
            if (connection.State != ConnectionState.Open)
                connection.Open();
            command.Connection = connection;
            command.CommandText = sqlStr;
            command.CommandType = commandType;
            if (parameters != null)
            {
                foreach (SqlParameter parameter in parameters)
                {
                    command.Parameters.Add(parameter);
                }
            }
            return command;
        }

        public static string RunProcedurestring(string storedProcName, IDataParameter[] parameters)
        {
            return RunProcedure(storedProcName, parameters);
        }

        public static string RunProcedure(string storedProcName, IDataParameter[] parameters)
        {
            using (SqlConnection connection = new SqlConnection(connectionStr))
            {

                connection.Open();
                SqlCommand MyCommand = BuildQueryCommand2(connection, storedProcName, parameters);
                MyCommand.ExecuteNonQuery();
                connection.Close();
                return MyCommand.Parameters["@return"].Value.ToString();
            }
        }

        private static SqlCommand BuildQueryCommand2(SqlConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            SqlCommand command = new SqlCommand(storedProcName, connection);
            command.CommandType = CommandType.StoredProcedure;
            foreach (SqlParameter parameter in parameters)
            {
                if (parameter != null)
                {
                    // 检查未分配值的输出参数,将其分配以DBNull.Value.
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    command.Parameters.Add(parameter);
                }
            }
            command.Parameters.Add(new SqlParameter("@return", SqlDbType.Int));
            command.Parameters["@return"].Direction = ParameterDirection.ReturnValue;
            return command;
        }

        public static DataSet Query(string SQLString)
        {
            return SelectQuery(SQLString);
        }

        public static DataSet SelectQuery(string SQLString)
        {
            using (SqlConnection connection = new SqlConnection(connectionStr))
            {
                DataSet ds = new DataSet();
                try
                {
                    connection.Open();
                    SqlDataAdapter command = new SqlDataAdapter(SQLString, connection);
                    command.Fill(ds, "ds");
                }
                catch (System.Data.SqlClient.SqlException ex)
                {
                    throw new Exception(ex.Message);
                }
                return ds;
            }
        }
        /// <summary>
        /// 执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数</returns>
        public static int ExecuteSql(string SQLString)
        {
            using (SqlConnection connection = new SqlConnection(connectionStr))
            {
                using (SqlCommand cmd = new SqlCommand(SQLString, connection))
                {
                    try
                    {
                        connection.Open();
                        int rows = cmd.ExecuteNonQuery();
                        return rows;
                    }
                    catch (System.Data.SqlClient.SqlException e)
                    {
                        connection.Close();
                        throw e;
                    }
                }
            }
        }

        /// <summary>
        /// 批量执行SQL语句，返回影响的总记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的总记录数</returns>
        public static int PLExecuteSql(ArrayList list)
        {
            using (SqlConnection connection = new SqlConnection(connectionStr))
            {
                connection.Open();
                int rows = 0;
                foreach (object o in list)
                {
                    using (SqlCommand cmd = new SqlCommand(o.ToString(), connection))
                    {
                        try
                        {

                            rows = rows + cmd.ExecuteNonQuery();

                        }
                        catch
                        {

                        }
                    }
                }
                connection.Close();
                return rows;
            }
        }

        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="tableName">DataSet结果中的表名</param>
        /// <returns>DataSet</returns>
        public static DataSet RunProcedure(string storedProcName, IDataParameter[] parameters, string tableName)
        {
            using (SqlConnection connection = new SqlConnection(connectionStr))
            {
                DataSet dataSet = new DataSet();
                connection.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter();
                sqlDA.SelectCommand = BuildQueryCommand(connection, storedProcName, parameters);
                sqlDA.Fill(dataSet, tableName);
                connection.Close();
                return dataSet;
            }
        }

        /// <summary>
        /// 构建 SqlCommand 对象(用来返回一个结果集，而不是一个整数值)
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>SqlCommand</returns>
        private static SqlCommand BuildQueryCommand(SqlConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            SqlCommand command = new SqlCommand(storedProcName, connection);
            command.CommandType = CommandType.StoredProcedure;
            foreach (SqlParameter parameter in parameters)
            {
                if (parameter != null)
                {
                    // 检查未分配值的输出参数,将其分配以DBNull.Value.
                    if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                        (parameter.Value == null))
                    {
                        parameter.Value = DBNull.Value;
                    }
                    command.Parameters.Add(parameter);
                }
            }

            return command;
        }

        /// <summary>
        /// bulk插入数据表
        /// </summary>
        /// <param name="dt">数据表</param>
        /// <param name="TableName">表名</param>
        /// <returns></returns>
        public static int BulkInsert(DataTable dt, string TableName)
        {
            SqlConnection sqlcon = new SqlConnection(connectionStr);
            sqlcon.Open();
            using (SqlBulkCopy bulk = new SqlBulkCopy(connectionStr))
            {
                bulk.BatchSize = dt.Rows.Count;
                bulk.DestinationTableName = TableName;
                try
                {
                    for (int i = 0; i < dt.Columns.Count; i++)
                    {
                        bulk.ColumnMappings.Add(dt.Columns[i].ColumnName, dt.Columns[i].ColumnName);
                    }
                    bulk.WriteToServer(dt);
                }
                catch (System.Exception ex)
                {
                    throw ex;
                }

            }
            sqlcon.Close();
            sqlcon.Dispose();
            return 1;
        }


    }
}

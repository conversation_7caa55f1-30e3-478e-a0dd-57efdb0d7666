<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Bcl.Memory</name>
    </assembly>
    <members>
        <member name="M:System.Buffers.Text.Base64Url.GetMaxDecodedLength(System.Int32)">
            <summary>
            Returns the maximum length (in bytes) of the result if you were to decode base 64 encoded text from a span of size <paramref name="base64Length"/>.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The specified <paramref name="base64Length"/> is less than 0.
            </exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Decodes the span of UTF-8 encoded text represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains UTF-8 encoded text in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <param name="bytesConsumed">When this method returns, contains the number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="isFinalBlock"><see langword="true"/> when the input span contains the entirety of data to encode; <see langword="false"/> when more data may follow,
            such as when calling in a loop. Calls with <see langword="false"/> should be followed up with another call where this parameter is <see langword="true"/> call. The default is <see langword="true" />.</param>
            <returns>One of the enumeration values that indicates the success or failure of the operation.</returns>
            <remarks>
            As padding is optional for Base64Url the <paramref name="source"/> length not required to be a multiple of 4 even if <paramref name="isFinalBlock"/> is <see langword="true"/>.
            If the <paramref name="source"/> length is not a multiple of 4 and <paramref name="isFinalBlock"/> is <see langword="true"/> the remainders decoded accordingly:
            - Remainder of 3 bytes - decoded into 2 bytes data, decoding succeeds.
            - Remainder of 2 bytes - decoded into 1 byte data. decoding succeeds.
            - Remainder of 1 byte - will cause OperationStatus.InvalidData result.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromUtf8InPlace(System.Span{System.Byte})">
            <summary>
            Decodes the span of UTF-8 encoded text in Base64Url into binary data, in-place.
            The decoded binary output is smaller than the text data contained in the input (the operation deflates the data).
            </summary>
            <param name="buffer">The input span which contains the base 64 text data that needs to be decoded.</param>
            <returns>The number of bytes written into <paramref name="buffer"/>. This can be used to slice the output for subsequent calls, if necessary.</returns>
            <exception cref="T:System.FormatException"><paramref name="buffer"/> contains an invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
            <remarks>
            As padding is optional for Base64Url the <paramref name="buffer"/> length not required to be a multiple of 4.
            If the <paramref name="buffer"/> length is not a multiple of 4 the remainders decoded accordingly:
            - Remainder of 3 bytes - decoded into 2 bytes data, decoding succeeds.
            - Remainder of 2 bytes - decoded into 1 byte data. decoding succeeds.
            - Remainder of 1 byte - is invalid input, causes FormatException.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
            <summary>
            Decodes the span of UTF-8 encoded text represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains UTF-8 encoded text in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <returns>The number of bytes written into <paramref name="destination"/>. This can be used to slice the output for subsequent calls, if necessary.</returns>
            <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination"/> is too small to hold the encoded output.</exception>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains an invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
            <remarks>
            As padding is optional for Base64Url the <paramref name="source"/> length not required to be a multiple of 4.
            If the <paramref name="source"/> length is not a multiple of 4 the remainders decoded accordingly:
            - Remainder of 3 bytes - decoded into 2 bytes data, decoding succeeds.
            - Remainder of 2 bytes - decoded into 1 byte data. decoding succeeds.
            - Remainder of 1 byte - is invalid input, causes FormatException.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.TryDecodeFromUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
            <summary>
            Decodes the span of UTF-8 encoded text represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains UTF-8 encoded text in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <returns><see langword="true"/> if bytes decoded successfully, otherwise <see langword="false"/>.</returns>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains an invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromUtf8(System.ReadOnlySpan{System.Byte})">
            <summary>
            Decodes the span of UTF-8 encoded text represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains UTF-8 encoded text in Base64Url that needs to be decoded.</param>
            <returns>>A byte array which contains the result of the decoding operation.</returns>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains an invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Decodes the span of unicode ASCII chars represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains unicode ASCII chars in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <param name="charsConsumed">When this method returns, contains the number of input chars consumed during the operation. This can be used to slice the input for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="isFinalBlock"><see langword="true"/> when the input span contains the entirety of data to encode; <see langword="false"/> when more data may follow,
            such as when calling in a loop. Calls with <see langword="false"/> should be followed up with another call where this parameter is <see langword="true"/> call. The default is <see langword="true" />.</param>
            <returns>One of the enumeration values that indicates the success or failure of the operation.</returns>
            <remarks>
            As padding is optional for Base64Url the <paramref name="source"/> length not required to be a multiple of 4 even if <paramref name="isFinalBlock"/> is <see langword="true"/>.
            If the <paramref name="source"/> length is not a multiple of 4 and <paramref name="isFinalBlock"/> is <see langword="true"/> the remainders decoded accordingly:
            - Remainder of 3 chars - decoded into 2 bytes data, decoding succeeds.
            - Remainder of 2 chars - decoded into 1 byte data. decoding succeeds.
            - Remainder of 1 char - will cause OperationStatus.InvalidData result.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte})">
            <summary>
            Decodes the span of unicode ASCII chars represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains ASCII chars in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <returns>The number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary.</returns>
            <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination"/> is too small to hold the encoded output.</exception>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains a invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.TryDecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@)">
            <summary>
            Decodes the span of unicode ASCII chars represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains ASCII chars in Base64Url that needs to be decoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the decoded binary data.</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <returns><see langword="true"/> if bytes decoded successfully, otherwise <see langword="false"/>.</returns>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains an invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.DecodeFromChars(System.ReadOnlySpan{System.Char})">
            <summary>
            Decodes the span of unicode ASCII chars represented as Base64Url into binary data.
            </summary>
            <param name="source">The input span which contains ASCII chars in Base64Url that needs to be decoded.</param>
            <returns>A byte array which contains the result of the decoding operation.</returns>
            <exception cref="T:System.FormatException"><paramref name="source"/> contains a invalid Base64Url character,
            more than two padding characters, or a non white space character among the padding characters.</exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Encodes the span of binary data into UTF-8 encoded text represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the UTF-8 encoded text in Base64Url.</param>
            <param name="bytesConsumed">When this method returns, contains the number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="isFinalBlock"><see langword="true"/> when the input span contains the entirety of data to encode; <see langword="false"/> when more data may follow,
            such as when calling in a loop, subsequent calls with <see langword="false"/> should end with <see langword="true"/> call. The default is <see langword="true" />.</param>
            <returns>One of the enumeration values that indicates the success or failure of the operation.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.GetEncodedLength(System.Int32)">
            <summary>
            Returns the length (in bytes) of the result if you were to encode binary data within a byte span of size <paramref name="bytesLength"/>.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="bytesLength"/> is less than 0 or greater than 1610612733.
            </exception>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
            <summary>
            Encodes the span of binary data into UTF-8 encoded text represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the UTF-8 encoded text in Base64Url.</param>
            <returns>The number of bytes written into the destination span. This can be used to slice the output for subsequent calls, if necessary.</returns>
            <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination"/> is too small to hold the encoded output.</exception>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToUtf8(System.ReadOnlySpan{System.Byte})">
            <summary>
            Encodes the span of binary data into UTF-8 encoded text represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <returns>The output byte array which contains the result of the operation, i.e. the UTF-8 encoded text in Base64Url.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToChars(System.ReadOnlySpan{System.Byte},System.Span{System.Char},System.Int32@,System.Int32@,System.Boolean)">
            <summary>
            Encodes the span of binary data into unicode ASCII chars represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the ASCII chars in Base64Url.</param>
            <param name="bytesConsumed">>When this method returns, contains the number of input bytes consumed during the operation. This can be used to slice the input for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="charsWritten">>When this method returns, contains the number of chars written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <param name="isFinalBlock"><see langword="true"/> when the input span contains the entirety of data to encode; <see langword="false"/> when more data may follow,
            such as when calling in a loop, subsequent calls with <see langword="false"/> should end with <see langword="true"/> call. The default is <see langword="true" />.</param>
            <returns>One of the enumeration values that indicates the success or failure of the operation.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToChars(System.ReadOnlySpan{System.Byte},System.Span{System.Char})">
            <summary>
            Encodes the span of binary data into unicode ASCII chars represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the ASCII chars in Base64Url.</param>
            <returns>The number of bytes written into the destination span. This can be used to slice the output for subsequent calls, if necessary.</returns>
            <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination"/> is too small to hold the encoded output.</exception>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToChars(System.ReadOnlySpan{System.Byte})">
            <summary>
            Encodes the span of binary data into unicode ASCII chars represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <returns>A char array which contains the result of the operation, i.e. the ASCII chars in Base64Url.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.EncodeToString(System.ReadOnlySpan{System.Byte})">
            <summary>
            Encodes the span of binary data into unicode string represented as Base64Url ASCII chars.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <returns>A string which contains the result of the operation, i.e. the ASCII string in Base64Url.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.TryEncodeToChars(System.ReadOnlySpan{System.Byte},System.Span{System.Char},System.Int32@)">
            <summary>
            Encodes the span of binary data into unicode ASCII chars represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the ASCII chars in Base64Url.</param>
            <param name="charsWritten">When this method returns, contains the number of chars written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <returns><see langword="true"/> if chars encoded successfully, otherwise <see langword="false"/>.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.TryEncodeToUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
            <summary>
            Encodes the span of binary data into UTF-8 encoded chars represented as Base64Url.
            </summary>
            <param name="source">The input span which contains binary data that needs to be encoded.</param>
            <param name="destination">The output span which contains the result of the operation, i.e. the UTF-8 encoded text in Base64Url.</param>
            <param name="bytesWritten">When this method returns, contains the number of chars written into the output span. This can be used to slice the output for subsequent calls, if necessary. This parameter is treated as uninitialized.</param>
            <returns><see langword="true"/> if bytes encoded successfully, otherwise <see langword="false"/>.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.TryEncodeToUtf8InPlace(System.Span{System.Byte},System.Int32,System.Int32@)">
            <summary>
            Encodes the span of binary data (in-place) into UTF-8 encoded text represented as base 64.
            The encoded text output is larger than the binary data contained in the input (the operation inflates the data).
            </summary>
            <param name="buffer">The input span which contains binary data that needs to be encoded.
            It needs to be large enough to fit the result of the operation.</param>
            <param name="dataLength">The amount of binary data contained within the buffer that needs to be encoded
            (and needs to be smaller than the buffer length).</param>
            <param name="bytesWritten">When this method returns, contains the number of bytes written into the buffer. This parameter is treated as uninitialized.</param>
            <returns><see langword="true"/> if bytes encoded successfully, otherwise <see langword="false"/>.</returns>
            <remarks>This implementation of the base64url encoding omits the optional padding characters.</remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.IsValid(System.ReadOnlySpan{System.Char})">
            <summary>Validates that the specified span of text is comprised of valid base-64 encoded data.</summary>
            <param name="base64UrlText">A span of text to validate.</param>
            <returns><see langword="true"/> if <paramref name="base64UrlText"/> contains a valid, decodable sequence of base-64 encoded data; otherwise, <see langword="false"/>.</returns>
            <remarks>
            If the method returns <see langword="true"/>, the same text passed to <see cref="M:System.Buffers.Text.Base64Url.DecodeFromChars(System.ReadOnlySpan{System.Char})"/> and
            <see cref="M:System.Buffers.Text.Base64Url.TryDecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@)"/> would successfully decode (in the case
            of <see cref="M:System.Buffers.Text.Base64Url.TryDecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@)"/> assuming sufficient output space).
            Any amount of whitespace is allowed anywhere in the input, where whitespace is defined as the characters ' ', '\t', '\r', or '\n'.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.IsValid(System.ReadOnlySpan{System.Char},System.Int32@)">
            <summary>Validates that the specified span of text is comprised of valid base-64 encoded data.</summary>
            <param name="base64UrlText">A span of text to validate.</param>
            <param name="decodedLength">If the method returns true, the number of decoded bytes that will result from decoding the input text.</param>
            <returns><see langword="true"/> if <paramref name="base64UrlText"/> contains a valid, decodable sequence of base-64 encoded data; otherwise, <see langword="false"/>.</returns>
            <remarks>
            If the method returns <see langword="true"/>, the same text passed to <see cref="M:System.Buffers.Text.Base64Url.DecodeFromChars(System.ReadOnlySpan{System.Char})"/> and
            <see cref="M:System.Buffers.Text.Base64Url.TryDecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@)"/> would successfully decode (in the case
            of <see cref="M:System.Buffers.Text.Base64Url.TryDecodeFromChars(System.ReadOnlySpan{System.Char},System.Span{System.Byte},System.Int32@)"/> assuming sufficient output space).
            Any amount of whitespace is allowed anywhere in the input, where whitespace is defined as the characters ' ', '\t', '\r', or '\n'.
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.IsValid(System.ReadOnlySpan{System.Byte})">
            <summary>Validates that the specified span of UTF-8 text is comprised of valid base-64 encoded data.</summary>
            <param name="utf8Base64UrlText">A span of UTF-8 text to validate.</param>
            <returns><see langword="true"/> if <paramref name="utf8Base64UrlText"/> contains a valid, decodable sequence of base-64 encoded data; otherwise, <see langword="false"/>.</returns>
            <remarks>
            where whitespace is defined as the characters ' ', '\t', '\r', or '\n' (as bytes).
            </remarks>
        </member>
        <member name="M:System.Buffers.Text.Base64Url.IsValid(System.ReadOnlySpan{System.Byte},System.Int32@)">
            <summary>Validates that the specified span of UTF-8 text is comprised of valid base-64 encoded data.</summary>
            <param name="utf8Base64UrlText">A span of UTF-8 text to validate.</param>
            <param name="decodedLength">If the method returns true, the number of decoded bytes that will result from decoding the input UTF-8 text.</param>
            <returns><see langword="true"/> if <paramref name="utf8Base64UrlText"/> contains a valid, decodable sequence of base-64 encoded data; otherwise, <see langword="false"/>.</returns>
            <remarks>
            where whitespace is defined as the characters ' ', '\t', '\r', or '\n' (as bytes).
            </remarks>
        </member>
        <member name="T:System.Index">
            <summary>Represent a type can be used to index a collection either from the start or the end.</summary>
            <remarks>
            Index is used by the C# compiler to support the new index syntax
            <code>
            int[] someArray = new int[5] { 1, 2, 3, 4, 5 } ;
            int lastElement = someArray[^1]; // lastElement = 5
            </code>
            </remarks>
        </member>
        <member name="M:System.Index.#ctor(System.Int32,System.Boolean)">
            <summary>Construct an Index using a value and indicating if the index is from the start or from the end.</summary>
            <param name="value">The index value. it has to be zero or positive number.</param>
            <param name="fromEnd">Indicating if the index is from the start or from the end.</param>
            <remarks>
            If the Index constructed from the end, index value 1 means pointing at the last element and index value 0 means pointing at beyond last element.
            </remarks>
        </member>
        <member name="P:System.Index.Start">
            <summary>Create an Index pointing at first element.</summary>
        </member>
        <member name="P:System.Index.End">
            <summary>Create an Index pointing at beyond last element.</summary>
        </member>
        <member name="M:System.Index.FromStart(System.Int32)">
            <summary>Create an Index from the start at the position indicated by the value.</summary>
            <param name="value">The index value from the start.</param>
        </member>
        <member name="M:System.Index.FromEnd(System.Int32)">
            <summary>Create an Index from the end at the position indicated by the value.</summary>
            <param name="value">The index value from the end.</param>
        </member>
        <member name="P:System.Index.Value">
            <summary>Returns the index value.</summary>
        </member>
        <member name="P:System.Index.IsFromEnd">
            <summary>Indicates whether the index is from the start or the end.</summary>
        </member>
        <member name="M:System.Index.GetOffset(System.Int32)">
            <summary>Calculate the offset from the start using the giving collection length.</summary>
            <param name="length">The length of the collection that the Index will be used with. length has to be a positive value</param>
            <remarks>
            For performance reason, we don't validate the input length parameter and the returned offset value against negative values.
            we don't validate either the returned offset is greater than the input length.
            It is expected Index will be used with collections which always have non negative length/count. If the returned offset is negative and
            then used to index a collection will get out of range exception which will be same affect as the validation.
            </remarks>
        </member>
        <member name="M:System.Index.Equals(System.Object)">
            <summary>Indicates whether the current Index object is equal to another object of the same type.</summary>
            <param name="value">An object to compare with this object</param>
        </member>
        <member name="M:System.Index.Equals(System.Index)">
            <summary>Indicates whether the current Index object is equal to another Index object.</summary>
            <param name="other">An object to compare with this object</param>
        </member>
        <member name="M:System.Index.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
        </member>
        <member name="M:System.Index.op_Implicit(System.Int32)~System.Index">
            <summary>Converts integer number to an Index.</summary>
        </member>
        <member name="M:System.Index.ToString">
            <summary>Converts the value of the current Index object to its equivalent string representation.</summary>
        </member>
        <member name="T:System.Range">
            <summary>Represent a range has start and end indexes.</summary>
            <remarks>
            Range is used by the C# compiler to support the range syntax.
            <code>
            int[] someArray = new int[5] { 1, 2, 3, 4, 5 };
            int[] subArray1 = someArray[0..2]; // { 1, 2 }
            int[] subArray2 = someArray[1..^0]; // { 2, 3, 4, 5 }
            </code>
            </remarks>
        </member>
        <member name="P:System.Range.Start">
            <summary>Represent the inclusive start index of the Range.</summary>
        </member>
        <member name="P:System.Range.End">
            <summary>Represent the exclusive end index of the Range.</summary>
        </member>
        <member name="M:System.Range.#ctor(System.Index,System.Index)">
            <summary>Construct a Range object using the start and end indexes.</summary>
            <param name="start">Represent the inclusive start index of the range.</param>
            <param name="end">Represent the exclusive end index of the range.</param>
        </member>
        <member name="M:System.Range.Equals(System.Object)">
            <summary>Indicates whether the current Range object is equal to another object of the same type.</summary>
            <param name="value">An object to compare with this object</param>
        </member>
        <member name="M:System.Range.Equals(System.Range)">
            <summary>Indicates whether the current Range object is equal to another Range object.</summary>
            <param name="other">An object to compare with this object</param>
        </member>
        <member name="M:System.Range.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
        </member>
        <member name="M:System.Range.ToString">
            <summary>Converts the value of the current Range object to its equivalent string representation.</summary>
        </member>
        <member name="M:System.Range.StartAt(System.Index)">
            <summary>Create a Range object starting from start index to the end of the collection.</summary>
        </member>
        <member name="M:System.Range.EndAt(System.Index)">
            <summary>Create a Range object starting from first element in the collection to the end Index.</summary>
        </member>
        <member name="P:System.Range.All">
            <summary>Create a Range object starting from first element to the end.</summary>
        </member>
        <member name="M:System.Range.GetOffsetAndLength(System.Int32)">
            <summary>Calculate the start offset and length of range object using a collection length.</summary>
            <param name="length">The length of the collection that the range will be used with. length has to be a positive value.</param>
            <remarks>
            For performance reason, we don't validate the input length parameter against negative values.
            It is expected Range will be used with collections which always have non negative length/count.
            We validate the range is inside the length scope though.
            </remarks>
        </member>
        <member name="P:System.SR.Argument_DestinationTooShort">
            <summary>Destination is too short.</summary>
        </member>
        <member name="P:System.SR.Format_BadBase64Char">
            <summary>The input is not a valid Base64Url string as it contains a non-Base64Url character, more than two padding characters, or an illegal character among the padding characters.</summary>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is supplying a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field or property member will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field and property members will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>

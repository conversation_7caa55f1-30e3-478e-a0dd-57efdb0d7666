﻿using EasyWork.Honor.Service.Print;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.PrintTool
{
    public partial class BoxNoPrint : Form
    {
        string ScriptText;
        public BoxNoPrint()
        {
            InitializeComponent();
            ScriptText = PrintService.Get_ZPLScript("中箱标", "ALL");
            tb_Date.Text = DateTime.Now.ToString("yyyy-MM-dd");
        }

        private void bt_Print_Click(object sender, EventArgs e)
        {
            if (tb_CARTON.Text.Trim() == "")
            {
                lb_show.Text = "请输入中箱号";
                tb_CARTON.Focus();
                return;
            }
            if (tb_ITEM.Text.Trim()=="")
            {
                lb_show.Text = "请输入编码";
                tb_ITEM.Focus();
                return;
            }
            if (tb_Qty.Text.Trim()=="")
            {
                lb_show.Text = "请先输入数量";
                tb_Qty.Focus();
                return;
            }
            if (tb_PSID.Text.Trim()=="")
            {
                lb_show.Text = "请先输入任务令";
                tb_PSID.Focus();
                return;
            }
            if (tb_Date.Text.Trim()=="")
            {
                lb_show.Text = "请先输入日期";
                tb_Date.Focus();
                return;
            }
            if (!int.TryParse(tb_Qty.Text.Trim(),out int _int))
            {
                lb_show.Text = "数量不规范";
                tb_Qty.Focus();
                return;
            }
            if (!DateTime.TryParse(tb_Date.Text.Trim(),out DateTime _datetime))
            {
                lb_show.Text = "日期不规范";
                tb_Date.Focus();
                return;
            }

            string text = ScriptText;
            for (int j = 0; j < 3; j++)
            {
                text = text.Replace("$PSID$", tb_PSID.Text.Trim().ToUpper());
                text = text.Replace("$ITEM$", tb_ITEM.Text.Trim().ToUpper());
                text = text.Replace("$QTY$", tb_Qty.Text.Trim().ToUpper());
                text = text.Replace("$CARTON$", tb_CARTON.Text.Trim().ToUpper());
                text = text.Replace("$DATE$", tb_Date.Text.Trim().ToUpper().Replace("-", "").Replace("-", ""));
            }

            ZPLPrint zb = new ZPLPrint();
            zb.ZPL_Print(text);
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {
            tb_CARTON.Text = "";
            tb_ITEM.Text = "";
            tb_PSID.Text = "";
            tb_Qty.Text = "";
        }

        private void cb_MD_print_SelectedIndexChanged(object sender, EventArgs e)
        {
            ScriptText = ScriptText.Replace("^MD10", "^MD" + cb_MD_print.Text.Trim());
        }

        private void tb_CARTON_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode==Keys.Enter)
            {
                bt_Print_Click(null,null);
            }
        }
    }
}

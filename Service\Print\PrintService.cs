﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EasyWork.Honor.Service.Print
{
    public static class PrintService
    {
        /// <summary>
        /// 查询打印ZPL的脚本
        /// </summary>
        /// <param name="ScriptType">脚本类型</param>
        /// <param name="PhoneModel">手机型号</param>
        /// <returns></returns>
        public static string Get_ZPLScript(string ScriptType,string PhoneModel)
        {
            string sql = "select ScriptText from ZPL_Scripts where ScriptType='"+ScriptType+ "' and PhoneModel='" + PhoneModel + "' and state='正常'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            if (dt.Rows.Count>0)
            {
                return dt.Rows[0]["ScriptText"].ToString();
            }
            return "";
        }
    }
}

﻿using EasyWork.bll.Login;
using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.WXMES
{
    public partial class WXFenPei : Form
    {
        string mac = "";
        string fenpeitotal = "";
        public WXFenPei()
        {
            InitializeComponent();
            Text = LoginService.GetMacAddress();
            start();
        }

        void start()
        {
            string sql = "select * from MES_Honor.dbo.CommonItems where itemname in('维修分配数','维修分配权限')";
            DataTable dt = SqlHelper.Query(sql).Tables[0];
            fenpeitotal = dt.Select("itemname='维修分配数'")[0]["text"].ToString();
            mac = dt.Select("itemname='维修分配权限'")[0]["text"].ToString();
        }

        private void btSave_Click(object sender, EventArgs e)
        {
            bool shop = false;
            string[] sting = mac.Split(',');
            foreach (object item in sting)
            {
                if (item.ToString() == Text.ToUpper())
                {
                    shop = true;
                    break;
                }
            }
            if (!shop)
            {
                MessageBox.Show("你没有权限打开");
                return;
            }
            //if (Text.ToUpper() != "E0:BE:03:1A:42:D9")
            //{
            //    //MessageBox.Show("你没有权限打开");
            //    //return;
            //}
            if (tbIMEI.Text.Trim() == "" || tbWXName.Text.Trim() == "")
            {
                MessageBox.Show("请选输入完整数据");
                return;
            }         

            string[] st = tbIMEI.Text.Trim().Split(new string[] { "\n" }, StringSplitOptions.None);
            bool b = st.GroupBy(c => c.ToString()).Where(d => d.Count() > 1).Count() > 0;
            if (b)
            {
                MessageBox.Show("在输入的物理号中有重复值");
                return;
            }

            if (!new[] { "FX", "CJ","待分配" }.Contains(tbWXName.Text.Trim().ToUpper()))
            {
                string sql1 = "select id from MES_Honor.dbo.sys_users where usercode='" + tbWXName.Text.Trim()+"' and userstate='正常'";
                DataTable dt1 = SqlHelper.Query(sql1).Tables[0];
                if (dt1.Rows.Count==0)
                {
                    MessageBox.Show("除FX/待分配/CJ外,系统没有该维修员信息");
                    return;
                }
            }

            string str = "";
            foreach (object item in st)
            {
                str += str == ""
                    ? "'" + item.ToString() + "'"
                    : ",'" + item.ToString() + "'";
            }

                string sql = "select * from MES_Honor.dbo.wxmes where old_imei in(" + str+ ") and isnull(location,'') not in('原件退回','Complete')";
            DataTable dt = SqlHelper.Query(sql).Tables[0];

            foreach (object item in st)
            {
                DataRow[] drr = dt.Select("old_imei='" + item + "'");
                if (drr.Length==0)
                {
                    MessageBox.Show($"物理号[{item}]系统不存在");
                    return;
                }                
            }

            foreach (DataRow item in dt.Rows)
            {
                if (item["Department"].ToString() == "")
                {
                    MessageBox.Show($"物理号[{item["old_imei"].ToString()}]还没有分配部门");
                    return;
                }
                if (item["state"].ToString() == "Receive")
                {
                    MessageBox.Show($"物理号[{item["old_imei"].ToString()}]还没有接收");
                    return;
                }
                if (item["Location"].ToString() != "FenPei" && item["Location"].ToString() != "WX" && item["Location"].ToString() != ""&&item["Location"].ToString()!= "待分配")
                {
                    MessageBox.Show($"物理号[{item["old_imei"].ToString()}]不在可分配站点,现在[{item["location"].ToString()}]上");
                    return;
                }
                if (new[] { "13366", "8823" }.Contains(tbWXName.Text.Trim()))
                {
                    if (item["Department"].ToString() == "翻新部门")
                    {
                        MessageBox.Show($"物理号[{item["old_imei"].ToString()}],翻新与拆机的主板维修不能为给工号13366与8823");
                        return;
                    }
                    if (item["InitialCode"].ToString().Substring(0, 1) == "5" && item["InitialCode"].ToString().Substring(0, 4) == "0303" && item["Department"].ToString() == "维修部门")
                    {
                        MessageBox.Show($"物理号[{item["old_imei"].ToString()}],翻新与拆机的主板维修不能为给工号13366与8823");
                        return;
                    }
                }

                if (!new[] { "FX", "CJ" }.Contains(item["Repairman"].ToString().Trim().ToUpper()) && item["Department"].ToString() != "PC主板")
                {
                    sql = "select count(1) from MES_Honor.dbo.wxmes where Repairman='" + item["Repairman"].ToString().Trim() + "' and location in('WX','ChaiJian')";
                    string total = SqlHelper.Query(sql).Tables[0].Rows[0][0].ToString();
            
                    //if (total > 8)
                    //{
                    //    //return $"维修员[{param.Repairman}]滞留在手上的主板不能超过8片,不能分配";
                    //}
                    if (int.Parse(total) + dt.Rows.Count > int.Parse(fenpeitotal))
                    {
                        MessageBox.Show($"维修员[{item["Repairman"].ToString()}]分配数量[{dt.Rows.Count}]+滞留[{total}]的主板不能超过{fenpeitotal}片,不能分配");
                        return;
                    }
                }

                //if (item["Repairman"].ToString().Trim().ToUpper() == "FX" && item["Department"].ToString() != "翻新部门")
                //{
                //    MessageBox.Show($"物理号[{item["old_imei"].ToString()}],不是翻新机器,不能分配到FX");
                //    return;
                //}
                //else if (item["Repairman"].ToString().Trim().ToUpper() == "CJ" && item["Department"].ToString() != "维修部门")
                //{
                //    MessageBox.Show($"物理号[{item["old_imei"].ToString()}],不是出主板机器,不能分配到CJ");
                //    return;
                //}

            }

            foreach (DataRow item in dt.Rows)
            {
                item["Location"] = item["Location"].ToString() != "WX"
                    ? "FenPei" : "WX";

                sql = "update MES_Honor.dbo.wxmes set Repairman='" + tbWXName.Text.Trim() + "',state='make',Location='" + item["Location"].ToString() + "'  where psid='" + item["psid"].ToString() + "' and old_imei ='" + item["old_imei"].ToString() + "'";
                SqlHelper.ExecuteSql(sql);

                sql = "insert into MES_Honor.dbo.wxmes_log (psid,phonemodel,initialcode,targetcode,item_sales,remarks,outtype,old_imei,sn,veneercode,originalfault,type,department,repairman,color,operatetype,location,state,createTime,createById) select psid,phonemodel,initialcode,targetcode,item_sales,remarks,outtype,old_imei,sn,veneercode,originalfault,type,department,repairman,color,'Job','FenPei','make',getdate(),'"+Login.usercode+"' from MES_Honor.dbo.wxmes where psid='" + item["psid"].ToString() + "' and old_imei= '" + item["old_imei"].ToString() + "'";
                SqlHelper.ExecuteSql(sql); 

            }


            MessageBox.Show("保存成功");
            tbIMEI.Text = tbWXName.Text = "";

        }


    }
}

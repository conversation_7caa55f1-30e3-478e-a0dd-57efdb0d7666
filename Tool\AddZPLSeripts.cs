﻿using EasyWork.Honor.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.Tool
{
    public partial class AddZPLSeripts : Form
    {
        public AddZPLSeripts()
        {
            InitializeComponent();
        }

        private void btSave_Click(object sender, EventArgs e)
        {
            string sql = "insert into ZPL_Scripts(scripttype,phonemodel,scripttext,createbyid,createtime,state) values('"+cbType.Text.Trim()+ "','"+tbCode.Text.Trim()+ "','"+tbZPLScriptText.Text.Trim()+ "',getdate(),'"+Login.usercode+"','正常')";
            SqlHelper.ExecuteSql(sql);
            tbZPLScriptText.Text = "";
            tbZPLScriptText.Focus();
            tbZPLScriptText.SelectAll();
        }

        private void btUpdate_Click(object sender, EventArgs e)
        {
            string sql = "update ZPL_Scripts set scripttext='" + tbZPLScriptText.Text.Trim()+ "' where phonemodel='"+tbCode.Text.Trim()+ "' and scripttype='"+cbType.Text.Trim()+"'";
            SqlHelper.ExecuteSql(sql);
            tbZPLScriptText.Text = "";
            tbZPLScriptText.Focus();
            tbZPLScriptText.SelectAll();
        }

        private void button1_Click(object sender, EventArgs e)
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Title = "txt文件";
            ofd.FileName = "";
            ofd.Filter = "文本文件(*.txt)|*.txt";
            ofd.ValidateNames = true;
            ofd.CheckFileExists = true;
            ofd.CheckPathExists = true;
            string strName = string.Empty;
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                strName = ofd.FileName;
            }
            if (strName == "")
            {
                MessageBox.Show("没有选择脚本文件");
                return;
            }
            string name = ofd.SafeFileName.Split('.')[0];
            string Stext = System.IO.File.ReadAllText(strName);

            string sql = "select * from ZPL_Scripts where phonemodel='" + name.Trim() + "' and scripttype='" + cbType.Text.Trim() + "'";
            DataTable dt = SqlHelper.Query(sql).Tables[0];

            if (dt.Rows.Count==0)
            {
                sql = "insert into ZPL_Scripts(scripttype,phonemodel,scripttext,intime,inname) values('" + cbType.Text.Trim() + "','" + name.Trim() + "','" + Stext.Trim() + "',getdate(),'" + Login.usercode + "')";
                SqlHelper.ExecuteSql(sql);
            }
            else
            {
                 sql = "update ZPL_Scripts set scripttext='" + Stext.Trim() + "' where phonemodel='" + name.Trim() + "' and scripttype='" + cbType.Text.Trim() + "'";
                SqlHelper.ExecuteSql(sql);
            }
            
        }
    }
}

﻿using DevComponents.DotNetBar;
using EasyWork.bll;
using EasyWork.bll.Login;
using EasyWork.bll.Public;
using EasyWork.Honor.Service;
using EasyWork.Honor.Service.WXMES;
using Newtonsoft.Json;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using System;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace EasyWork.Honor
{
    public partial class MDI : Form
    {      
        public static string HPDShow = "0";
        public MDI()
        {
            InitializeComponent();   
            Permission();
            //Thread ckd = new Thread(CheckDomain);
            //ckd.Start();

            //Thread ht = new Thread(heartbeat);
            //ht.Start();
            Text = Login.usercode+"-"+Login.username;    

            tabControl1.SelectedIndex = 1;
            //JokeGenerator();
        }

        //void CheckDomain()
        //{
        //    LoginService.ChangePCName(Login.pcname);
        //    PublicService.GetRegistryWinUpdate();
        //    try
        //    {
        //        int.Parse(Login.usercode);
        //    }
        //    catch 
        //    {
        //        return;
        //    } 
        //}

        //void readMainText()
        //{
        //    //MeBrie_label = "";
        //}

        //void heartbeat()
        //{
        //    while (true)
        //    {
        //        PublicService.SaveLog(Login.usercode, "", Login.ver, "在线检测", "heartbeat", "Main", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        //        Thread.Sleep(3 * 60 * 1000);
        //    }
        //}

        private void Permission()
        {
            // Login.usercode = "admin";

            foreach (SideBarPanelItem ctl in Bar_HW.Panels)
            {
                //if (new[] { "btPrintTool" }.Contains(ctl.Name.ToString()))
                //{
                //    continue;
                //}
                if (!new[] { "HonorApp" }.Contains(ctl.Name.ToString()))
                {
                    ctl.Visible = false;
                }

                foreach (ButtonItem ct in ctl.SubItems)
                {
                    if (!new[] { "bt_KnowledgeLibrary111", "bt_Compartner111" }.Contains(ct.Name.ToString()))
                    {
                        ct.Visible = false;
                    }
                }
            }

            //2025-02-21 添加：隐藏百沃应用面板的菜单
            foreach (SideBarPanelItem ctl in Bar_BW.Panels)
            {
                ctl.Visible = false;
                foreach (ButtonItem ct in ctl.SubItems)
                {
                    ct.Visible = false;
                }
            }

            DataTable dt = PublicService.Permission(Login.usercode);
            DataTable pardt = PublicService.ParentPer(dt);

            foreach (DataRow pdr in pardt.Rows)
            {
                foreach (SideBarPanelItem ctl in Bar_HW.Panels)
                {
                    if (ctl.Name.ToLower() == pdr["qxname"].ToString().ToLower())
                    {
                        ctl.Visible = true;
                        continue;
                    }
                }

                foreach (SideBarPanelItem ctl in Bar_BW.Panels)
                {
                    if (ctl.Name.ToLower() == pdr["qxname"].ToString().ToLower())
                    {
                        ctl.Visible = true;
                        continue;
                    }
                }
            }



            foreach (DataRow dr in dt.Rows)
            {
                foreach (SideBarPanelItem ctl in Bar_HW.Panels)
                {
                    foreach (ButtonItem ct in ctl.SubItems)
                    {
                        if (ct.Name.ToLower() == dr["qxname"].ToString().ToLower())
                        {
                            ct.Visible = true;
                            continue;
                        }
                    }
                }

                foreach (SideBarPanelItem ctl in Bar_BW.Panels)
                {
                    foreach (ButtonItem ct in ctl.SubItems)
                    {
                        if (ct.Name.ToLower() == dr["qxname"].ToString().ToLower())
                        {
                            ct.Visible = true;
                            continue;
                        }
                    }
                }
            }

        }










        #region 荣耀应用
        private void KnowledgeLibrary1_Click(object sender, EventArgs e)
        {
            IWebDriver selenium = new ChromeDriver();
            //if (ts_oldweknow.Checked)
            //{
            //    selenium.Navigate().GoToUrl("http://consumer-tkb.huawei.com/tkb");
            //    PublicService.SaveLog(Login.usercode, "huawei", Login.ver, "正常", "ComPartner-W", "旧知识库", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
            //}
            //else
            //{
                selenium.Navigate().GoToUrl("http://consumer-tkb.huawei.com/ctkb/home.html#/index");
                PublicService.SaveLog(Login.usercode, "huawei", Login.ver, "正常", "ComPartner-W", "新知识库", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
          //  }
            DataTable dt = PublicService.QueryAccount("ComPartner-W");
            if (dt.Rows.Count < 1)
            {
                return;
            }
            string uid = dt.Rows[0]["uid"].ToString();
            string pwd = dt.Rows[0]["pwd"].ToString();

            selenium.FindElement(By.Id("uid")).SendKeys(uid);
            selenium.FindElement(By.Id("password")).SendKeys(pwd);
            Thread.Sleep(10);
            selenium.FindElement(By.XPath("//*[@value='登录']")).Click();
        }


        //*[@id="app"]/div/div[1]/div[3]/div[1]/div/ul/div[5]/li/div
        public void authex_login(string code)
        {
            DataTable dt = PublicService.QueryAccount(code);
            if (dt.Rows.Count < 1)
            {
                return;
            }
            string uid = dt.Rows[0]["uid"].ToString();
            string pwd = dt.Rows[0]["pwd"].ToString();
            string url = dt.Rows[0]["url"].ToString();
            string remarks = dt.Rows[0]["remarks"].ToString();
            ChromeOptions options = new ChromeOptions();
            options.AddArgument("--no-sandbox");


            ChromeDriver selenium = new ChromeDriver();
           
            selenium.Navigate().GoToUrl(url);
            while (true)
            {
                try
                {
                    selenium.FindElement(By.Id("username")).SendKeys(uid);
                    selenium.FindElement(By.XPath("//*[@type='password']")).SendKeys(pwd);
                    break;
                }
                catch
                {
                    Thread.Sleep(3000);                    
                }
            }
            Thread.Sleep(5);
            try
            {
                selenium.FindElement(By.XPath("//*[@value='登录']")).Click();
            }
            catch
            {
                selenium.FindElement(By.XPath("//*[@type='submit']")).Click();
            }
            PublicService.SaveLog(Login.usercode, "huawei", Login.ver, "正常", uid, remarks + code, Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }  

        public void openComPartner(string type)
        {
            IntPtr maindHwnd = WinAPI.FindWindow(null, "登录");
            if (maindHwnd == IntPtr.Zero)
            {
                string str = "C:\\Program Files\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&C:\\Program Files (x86)\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&C:\\Program Files\\HonorComPartner\\HonorComPartner.exe";
                str += "&C:\\Program Files (x86)\\HonorComPartner\\HonorComPartner.exe";
                str += "&D:\\Program Files\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&D:\\Program Files (x86)\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&D:\\Program Files\\HonorComPartner\\HonorComPartner.exe";
                str += "&D:\\Program Files (x86)\\HonorComPartner\\HonorComPartner.exe";
                str += "&E:\\Program Files\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&E:\\Program Files (x86)\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&E:\\Program Files\\HonorComPartner\\HonorComPartner.exe";
                str += "&E:\\Program Files (x86)\\HonorComPartner\\HonorComPartner.exe";
                str += "&F:\\Program Files\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&F:\\Program Files (x86)\\Honor\\HonorComPartner\\HonorComPartner.exe";
                str += "&F:\\Program Files\\HonorComPartner\\HonorComPartner.exe";
                str += "&F:\\Program Files (x86)\\HonorComPartner\\HonorComPartner.exe";

                string[] filename = str.Split('&');

                foreach (object o in filename)
                {
                    if (File.Exists(o.ToString()))
                    {
                        Process.Start(o.ToString());
                        break;
                    }
                }
            }
            Thread.Sleep(1500);
            maindHwnd = WinAPI.FindWindow(null, "登录");
            if (maindHwnd == IntPtr.Zero)
            {
                maindHwnd = WinAPI.FindWindow(null, "Login");
                if (maindHwnd == IntPtr.Zero)
                {
                    MessageBox.Show("请启动程序后再进行尝试");
                    return;
                }
            }
            IntPtr childHwnd = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "edit", null);
            IntPtr childHwnd1 = WinAPI.FindWindowEx(maindHwnd, IntPtr.Zero, "AfxWnd140", null);//42

            DataTable dt = PublicService.QueryAccount(type);
            if (dt.Rows.Count > 0)
            {
                string uid = dt.Rows[0]["uid"].ToString();
                string pwd = dt.Rows[0]["pwd"].ToString();
                WinAPI.SendMessage(childHwnd, WinAPI.WM_SETTEXT, (int)IntPtr.Zero, uid);

                for (int i = 0; i < 50; i++)
                {
                    WinAPI.SendMessage(childHwnd1, WinAPI.WM_CHAR, 8, 0);
                    Thread.Sleep(5);
                }
                string st = "";
                for (int i = 0; i < pwd.Length; i++)
                {
                    st += pwd.Substring(i, 1).ToString() + ",";
                }

                string[] str = st.Split(',');
                foreach (object o in str)
                {
                    if (o.ToString() != "")
                    {
                        WinAPI.SendMessage(childHwnd1, WinAPI.WM_CHAR, PublicService.StringToASCII(o.ToString()), 0);
                    }
                }

                PublicService.SaveLog(Login.usercode, "Honor", Login.ver, "正常", type, "ComPartner", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
            }
        }

        private void ts_new_weknow_Click(object sender, EventArgs e)
        {
            if (ts_new_weknow.Checked)
            {
                ts_oldweknow.Checked = true;
                ts_new_weknow.Checked = false;
                PublicService.WriteConfigData("weknow", "old_weknow");

            }
            else
            {
                ts_oldweknow.Checked = false;
                ts_new_weknow.Checked = true;
                PublicService.WriteConfigData("weknow", "new_weknow");
            }
        }

        private void HonorPublicLogin_Click(object sender, EventArgs e)
        {
          //  MessageBox.Show(sender.ToString().Split('-')[0]);
             authex_login(sender.ToString().Split('-')[0]);
        }

        private void bt_web_Compartner_Click(object sender, EventArgs e)
        {
            IWebDriver selenium = new ChromeDriver();
      
            selenium.Navigate().GoToUrl("https://authex.hihonor.com/uniportal1/?redirect=https%3A%2F%2Fcompartner.service.hihonor.com%2Fnmsp%2Ffinaltest");
           
        }

        #endregion

        private void MDI_FormClosing(object sender, FormClosingEventArgs e)
        {
            PublicService.SaveLog(Login.usercode, "", Login.ver, "登出", "LogOut", "MDI", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
            Application.ExitThread();
            Environment.Exit(0);
        }

        private void bt_MingPaiPrint_Click(object sender, EventArgs e)
        {
            PrintTool.MingPaiPrint newform = new PrintTool.MingPaiPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "MingPaiPrint", "铭牌标签打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_ZPLPublicPrint_Click(object sender, EventArgs e)
        {
            PrintTool.ZPLPublicPrint newform = new PrintTool.ZPLPublicPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "ZPLPublicPrint", "斑马打印机通用打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_ChuHuoPrint_Click(object sender, EventArgs e)
        {
            PrintTool.ChuHuoPrint newform = new PrintTool.ChuHuoPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "ChuHuoPrint", "维修出货盒标", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_GZChuHuoPrint_Click(object sender, EventArgs e)
        {
            PrintTool.GZChuHuoPrint newform = new PrintTool.GZChuHuoPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "GZChuHuoPrint", "改制出货盒标", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_BoxNoPrint_Click(object sender, EventArgs e)
        {
            PrintTool.BoxNoPrint newform = new PrintTool.BoxNoPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "BoxNoPrint", "中箱号打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_WXPsidWorkPrint_Click(object sender, EventArgs e)
        {
            PrintTool.WXPsidWorkPrint newform = new PrintTool.WXPsidWorkPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "WXPsidWorkPrint", "维修工单打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_CMESWorkPrint_Click(object sender, EventArgs e)
        {
            PrintTool.CMESWorkPrint newform = new PrintTool.CMESWorkPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "CMESWorkPrint", "客户机工单打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_CheckReportPrint_Click(object sender, EventArgs e)
        {
            PrintTool.CheckReportPrint newform = new PrintTool.CheckReportPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "CheckReportPrint", "改制检测报告打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btHHTPlugins_Click(object sender, EventArgs e)
        {
            ClientPlugins.HHTPlugins newform = new ClientPlugins.HHTPlugins();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "btClientPlugins", "火火兔", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void bt_Compartner1_Click(object sender, EventArgs e)
        {
            openComPartner("ComPartner1");
        }

        private void bt_Compartner2_Click(object sender, EventArgs e)
        {
            openComPartner("ComPartner2");
        }

        private void bt_KnowledgeLibrary1_Click(object sender, EventArgs e)
        {
            authex_login("KnowledgeLibrary1");
        }

        private void bt_KnowledgeLibrary2_Click(object sender, EventArgs e)
        {
            authex_login("KnowledgeLibrary2");
        }

        private void buttonItem2_Click(object sender, EventArgs e)
        {
            //PublicTool.DistinguishBetween newform = new PublicTool.DistinguishBetween();
            Form1 newform = new Form1();
            newform.Show();
        }

        private async void btDataVal_Click(object sender, EventArgs e)
        {
            //增加停线检查机制
            var (isStop, reason) = await WXMESPublic.QuickCheckStationAsync("DataVal", "翻新部门");
            if (isStop)
            {
                System.Windows.MessageBox.Show($"站位已停线！\n原因：{reason}", "停线异常", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                return;
            }
            WXMES.DataValJob newform = new WXMES.DataValJob();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "DataValJob", "维修数据验证", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private async void PCDataVal_Click(object sender, EventArgs e)
        {
            //增加停线检查机制
            var (isStop, reason) = await WXMESPublic.QuickCheckStationAsync("DataVal", "PC翻新");
            if (isStop)
            {
                System.Windows.MessageBox.Show($"站位已停线！\n原因：{reason}", "停线异常", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                return;
            }
            WXMES.PCDataValJob newform = new WXMES.PCDataValJob();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "PCDataValJob", "PC数据验证", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btColorBoxPrint_Click(object sender, EventArgs e)
        {
            WXMES.ColorBoxPrint newform = new WXMES.ColorBoxPrint();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "ColorBoxPrint", "彩盒标打印", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btWXFenPei_Click(object sender, EventArgs e)
        {
            WXMES.WXFenPei newform = new WXMES.WXFenPei();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "WXFenPei", "维修分配", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btWXMESDataMerge_Click(object sender, EventArgs e)
        {
            WXMES.WXMESDataMerge newform = new WXMES.WXMESDataMerge();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "WXMESDataMerge", "WXMES数据合并", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btAddZPLSeripts_Click(object sender, EventArgs e)
        {
            Tool.AddZPLSeripts newform = new Tool.AddZPLSeripts();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "WXMESDataMerge", "添加ZPL脚本", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btAutoReadILookLog_Click(object sender, EventArgs e)
        {
            WXMES.AutoReadILookLog newform = new WXMES.AutoReadILookLog();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "AutoReadILookLog", "读取ILOOK串口日志", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }

        private void btAutoReadWXReport_Click(object sender, EventArgs e)
        {
            WXMES.AutoReadWXReport newform = new WXMES.AutoReadWXReport();
            newform.Show();
            PublicService.SaveLog(Login.usercode, Login.username, Login.ver, "正常", "0", "读取TGMES高维维修报表", Login.mac, Login.pcname, Login.ip, Login.batch, Login.winver, Login.cpu, Login.hdd, Login.memory);
        }


        /// <summary>
        /// 段子生成器
        /// </summary>
        private void JokeGenerator()
        {
            // 先查询当天是否有段子
            string story = PublicService.ReadStory();
            if (story == null)
            {
                try
                {
                    // 读取 Json 文件
                    string json = File.ReadAllText("Joker.json");
                    // 反序列化
                    var jokers = Newtonsoft.Json.Linq.JObject.Parse(json)["jokes"];
                    // 随机生成一个索引
                    Random random = new Random();
                    // 写入故事
                    string joke = jokers[random.Next(jokers.Count())]["text"].ToString();
                    PublicService.WritetJokerStory(joke);
                    story = joke;
                }
                catch (Exception ex)
                {
                    // 处理异常，记录日志
                    Debug.WriteLine($"Error reading or parsing Joker.json: {ex.Message}");
                }
            }
            // 更新标签文本
            this.MeBrie_label.Text = story ?? this.MeBrie_label.Text;
        }
    }
}

@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ===== Configuration =====
set "VERSION=1.2.2"
set "PROJECT_NAME=EasyWork.Honor"
set "RELEASE_DIR=bin\Release"
REM ===========================

echo [INFO] Starting build process for v%VERSION%

echo [STEP1] Clean and build project...
dotnet build "%PROJECT_NAME%.csproj" -c Release
if errorlevel 1 (
    echo [ERROR] Build failed, trying with MSBuild...
    msbuild "%PROJECT_NAME%.csproj" /p:Configuration=Release /p:Platform="Any CPU" /t:Clean,Build
    if errorlevel 1 (
        echo [ERROR] Build failed
        goto :end
    )
)

echo [STEP2] Create NuGet package...
REM 确保 nuget.exe 可用
where nuget >nul 2>&1
if errorlevel 1 (
    echo [ERROR] nuget.exe not found in PATH. Please install NuGet CLI or add it to PATH
    goto :end
)

nuget pack "%PROJECT_NAME%.nuspec" -Version %VERSION% -OutputDirectory "%RELEASE_DIR%"
if errorlevel 1 (
    echo [ERROR] NuGet pack failed
    goto :end
)

echo [STEP3] Generate Squirrel installer...
pushd "%RELEASE_DIR%"
if exist "Setup.exe" del "Setup.exe"
if exist "RELEASES" del "RELEASES"
if exist "%PROJECT_NAME%.%VERSION%.nupkg" (
    REM 使用项目中的 Squirrel 工具
    "..\packages\squirrel.windows.2.0.1\tools\Squirrel.exe" --releasify "%PROJECT_NAME%.%VERSION%.nupkg" --releaseDir=.
    if errorlevel 1 (
        echo [ERROR] Squirrel releasify failed
        popd
        goto :end
    )
) else (
    echo [ERROR] NuGet package not found: %PROJECT_NAME%.%VERSION%.nupkg
    popd
    goto :end
)
popd

echo [SUCCESS] Build completed! Files ready for publish:
echo   - %RELEASE_DIR%\%PROJECT_NAME%.%VERSION%.nupkg
echo   - %RELEASE_DIR%\Setup.exe
echo   - %RELEASE_DIR%\RELEASES

:end
echo Press any key to exit...
pause >nul

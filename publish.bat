@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ===== Configuration =====
set "VERSION=1.2.2"
set "PROJECT_NAME=EasyWork.Honor"
set "NUSPEC_FILE=EasyWork.Honor.nuspec"
set "DEBUG_DIR=bin\Debug"
set "SOURCE=E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Release"
set "BASE_PATH=\\***********\Backup\publish\dog"
set "TARGET=%BASE_PATH%\versions\v%VERSION%"
set "CURRENT=%BASE_PATH%\current"
set "USERNAME=it"
set "PASSWORD=123456"
set "PACKAGE_NAME=EasyWork.Honor.%VERSION%.nupkg"
set "NUGET_PATH=C:\Program Files\NuGet\nuget.exe"
set "SQUIRREL_TOOLS=..\packages\squirrel.windows.2.0.1\tools"
REM ===========================

echo [INFO] Starting one-click build and publish process for v%VERSION%
echo ========================================================
echo.

echo [PHASE1] BUILD PACKAGE
echo [STEP1] Prepare Release directory...
if not exist "%DEBUG_DIR%\%PROJECT_NAME%.exe" (
    echo [ERROR] Debug build not found!
    echo [INFO] Please build the project in Visual Studio first:
    echo [INFO] 1. Open Visual Studio
    echo [INFO] 2. Set configuration to Debug
    echo [INFO] 3. Build the project
    echo [INFO] 4. Then run this script again
    goto :end
)

echo [INFO] Found Debug build, creating Release directory...
if exist "%SOURCE%" rmdir /s /q "%SOURCE%" >nul 2>&1
mkdir "%SOURCE%" >nul 2>&1

echo [INFO] Copying files from Debug to Release...
xcopy "%DEBUG_DIR%\*.*" "%SOURCE%\" /E /Y >nul

echo [STEP2] Create NuGet package...
if not exist "%NUGET_PATH%" (
    echo [ERROR] NuGet not found at: %NUGET_PATH%
    echo [INFO] Please install NuGet or update the path in this script
    goto :end
)

echo [INFO] Creating NuGet package...
"%NUGET_PATH%" pack "%NUSPEC_FILE%" -Version %VERSION% -OutputDirectory "%SOURCE%"
if errorlevel 1 (
    echo [ERROR] NuGet pack failed
    goto :end
)

echo [INFO] NuGet package created successfully: %PACKAGE_NAME%
echo.

echo [PHASE2] PUBLISH TO NAS
echo [STEP3] Validate source directory...
if not exist "%SOURCE%" (
    echo [ERROR] Source directory not found: %SOURCE%
    goto :end
)

if not exist "%SOURCE%\%PACKAGE_NAME%" (
    echo [ERROR] NuGet package not found: %PACKAGE_NAME%
    echo [INFO] Package creation may have failed
    goto :end
)

echo [STEP4] Connect to NAS...
REM 断开所有到该服务器的现有连接，防止多重连接错误
net use \\***********\IPC$ /delete /y >nul 2>&1
net use \\***********\Backup /delete /y >nul 2>&1
net use "%BASE_PATH%" /delete /y >nul 2>&1
net use * /delete /y >nul 2>&1

REM 等待网络重置
ping 127.0.0.1 -n 2 >nul

REM 重新连接到NAS
net use "%BASE_PATH%" /user:%USERNAME% %PASSWORD% /persistent:no
if errorlevel 1 (
    echo [ERROR] NAS connection failed
    echo [INFO] Trying alternative connection method...
    REM 尝试连接到根共享
    net use \\***********\Backup /user:%USERNAME% %PASSWORD% /persistent:no >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Alternative connection also failed
        goto :end
    )
)

echo [STEP5] Create version directory...
mkdir "%TARGET%" >nul 2>&1
if not exist "%TARGET%" (
    echo [ERROR] Failed to create version directory
    goto :cleanup
)

echo [STEP6] Copy essential files...
REM 检查源文件是否存在
if not exist "%SOURCE%\*.nupkg" (
    echo [WARNING] No .nupkg files found in source directory
    echo [INFO] Available files in source:
    dir "%SOURCE%" /b 2>nul | findstr /i "\.nupkg \.exe RELEASES" 2>nul
    if errorlevel 1 echo [INFO] No matching files found
)

REM 复制文件，允许部分失败
robocopy "%SOURCE%" "%TARGET%" "*.nupkg" "Setup.exe" "RELEASES" /NFL /NDL /NJH /NJS /R:3 /W:1
set COPY_RESULT=%errorlevel%
echo [INFO] Copy result code: %COPY_RESULT%
if %COPY_RESULT% geq 8 (
    echo [ERROR] Critical file copy error
    goto :cleanup
)

echo [STEP7] Generate RELEASES file using Squirrel...
pushd "%TARGET%"
del RELEASES 2>nul

REM 检查是否有任何 .nupkg 文件
dir *.nupkg /b >nul 2>&1
if errorlevel 1 (
    echo [ERROR] No .nupkg files found in target directory
    echo [INFO] Cannot generate RELEASES file without package files
    goto :cleanup_nas
)

REM 如果指定的包文件存在，使用它
if exist "%PACKAGE_NAME%" (
    echo [INFO] Using expected package: %PACKAGE_NAME%
    goto :found_package
)

REM 否则使用第一个找到的 .nupkg 文件
echo [WARNING] Expected package file missing: %PACKAGE_NAME%
echo [INFO] Looking for alternative .nupkg files...
for %%f in (*.nupkg) do (
    set "PACKAGE_NAME=%%f"
    echo [INFO] Using alternative package: %%f
    goto :found_package
)

:found_package
echo [INFO] Generating RELEASES file using Squirrel for: %PACKAGE_NAME%

REM 使用 Squirrel 工具生成 RELEASES 文件
echo [INFO] Running Squirrel releasify to generate proper RELEASES file...
"..\..\%SQUIRREL_TOOLS%\Squirrel.exe" --releasify "%PACKAGE_NAME%" --releaseDir=. --no-msi
if errorlevel 1 (
    echo [WARNING] Squirrel releasify failed, falling back to manual generation...
    goto :manual_releases
)

REM 检查 Squirrel 是否生成了 RELEASES 文件
if exist "RELEASES" (
    echo [INFO] RELEASES file generated successfully by Squirrel
    goto :releases_done
)

:manual_releases
echo [INFO] Generating RELEASES file manually...
certutil -hashfile "%PACKAGE_NAME%" SHA1 > tmp.hash
if errorlevel 1 (
    echo [ERROR] Failed to generate hash for %PACKAGE_NAME%
    goto :cleanup_nas
)

REM 提取 SHA1 哈希值
set "SHA1="
for /f "skip=1 tokens=1" %%a in (tmp.hash) do (
    if "!SHA1!"=="" (
        set "SHA1=%%a"
    )
)

REM 去除 SHA1 中的空格
set "SHA1=!SHA1: =!"

call :get_file_size "%PACKAGE_NAME%"

echo [INFO] SHA1: !SHA1!
echo [INFO] File: %PACKAGE_NAME%
echo [INFO] Size: !FILE_SIZE!

REM 生成 RELEASES 文件
echo !SHA1! %PACKAGE_NAME% !FILE_SIZE!> RELEASES
del tmp.hash >nul 2>&1
echo [INFO] RELEASES file generated manually

:releases_done

echo [STEP8] Update current version links...
REM 确保 current 目录存在
if not exist "%CURRENT%" mkdir "%CURRENT%" >nul 2>&1

REM 删除旧的符号链接
del "%CURRENT%\*.nupkg" >nul 2>&1
del "%CURRENT%\Setup.exe" >nul 2>&1
del "%CURRENT%\RELEASES" >nul 2>&1

REM 创建新的符号链接
if exist "%TARGET%\%PACKAGE_NAME%" (
    mklink "%CURRENT%\%PACKAGE_NAME%" "%TARGET%\%PACKAGE_NAME%" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for %PACKAGE_NAME%, copying file instead
        copy "%TARGET%\%PACKAGE_NAME%" "%CURRENT%\" >nul 2>&1
    )
)



if exist "%TARGET%\Setup.exe" (
    mklink "%CURRENT%\Setup.exe" "%TARGET%\Setup.exe" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for Setup.exe, copying file instead
        copy "%TARGET%\Setup.exe" "%CURRENT%\" >nul 2>&1
    )
)

if exist "%TARGET%\RELEASES" (
    mklink "%CURRENT%\RELEASES" "%TARGET%\RELEASES" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for RELEASES, copying file instead
        copy "%TARGET%\RELEASES" "%CURRENT%\" >nul 2>&1
    )
)

echo [INFO] Current version links updated to v%VERSION%

:cleanup_nas
popd
echo.
echo ========================================================
echo [SUCCESS] One-click build and publish completed!
echo [INFO] Version %VERSION% is now available for updates
echo [INFO] Update URL: file://***********/Backup/publish/dog/current
echo ========================================================

:cleanup
net use "%BASE_PATH%" /delete /y >nul 2>&1

:get_file_size
set "FILE_SIZE=0"
for %%A in ("%~1") do set "FILE_SIZE=%%~zA"
goto :eof

:end
echo Press any key to exit...
pause >nul

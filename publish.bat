@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ===== Configuration =====
set "VERSION=1.2.2"
set "SOURCE=E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Release"
set "BASE_PATH=\\172.20.0.20\Backup\publish\dog"
set "TARGET=%BASE_PATH%\versions\v%VERSION%"
set "CURRENT=%BASE_PATH%\current"
set "USERNAME=it"
set "PASSWORD=123456"
set "PACKAGE_NAME=EasyWork.Honor.%VERSION%.nupkg"
REM ===========================

echo [INFO] Starting publish process for v%VERSION%

echo [STEP1] Validate source directory...
if not exist "%SOURCE%" (
    echo [ERROR] Source directory not found: %SOURCE%
    goto :end
)

echo [STEP2] Connect to NAS...
net use "%BASE_PATH%" /delete /y >nul 2>&1
net use "%BASE_PATH%" /user:%USERNAME% %PASSWORD% /persistent:no
if errorlevel 1 (
    echo [ERROR] NAS connection failed
    goto :end
)

echo [STEP3] Create version directory...
mkdir "%TARGET%" >nul 2>&1
if not exist "%TARGET%" (
    echo [ERROR] Failed to create version directory
    goto :cleanup
)

echo [STEP4] Copy essential files...
robocopy "%SOURCE%" "%TARGET%" "*.nupkg" "Setup.exe" "RELEASES" /NFL /NDL /NJH /NJS /R:3 /W:1
if errorlevel 8 (
    echo [ERROR] File copy failed
    goto :cleanup
)

echo [STEP5] Generate RELEASES file...
pushd "%TARGET%"
del RELEASES 2>nul
if not exist "%PACKAGE_NAME%" (
    echo [ERROR] Package file missing: %PACKAGE_NAME%
    goto :cleanup_nas
)

certutil -hashfile "%PACKAGE_NAME%" SHA1 > tmp.hash
for /f "tokens=1,2 delims=:" %%a in ('findstr /v ":" tmp.hash ^| findstr /v "CertUtil"') do (
    set "SHA1=%%b"
)
set "SIZE=%~z%PACKAGE_NAME%"
echo !SHA1! %PACKAGE_NAME% !SIZE! > RELEASES
del tmp.hash >nul 2>&1
echo RELEASES file generated

echo [STEP6] Update current version links...
del "%CURRENT%\*" >nul 2>&1
mklink "%CURRENT%\%PACKAGE_NAME%" "%TARGET%\%PACKAGE_NAME%" >nul
mklink "%CURRENT%\Setup.exe" "%TARGET%\Setup.exe" >nul
mklink "%CURRENT%\RELEASES" "%TARGET%\RELEASES" >nul
echo Current version links updated to v%VERSION%

:cleanup_nas
popd
echo [SUCCESS] Publish completed!

:cleanup
net use "%BASE_PATH%" /delete /y >nul 2>&1

:end
echo Press any key to exit...
pause >nul
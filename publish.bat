@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ===== Configuration =====
set "VERSION=1.2.2"
set "SOURCE=E:\Wangxianqi\Project\Project_4\EasyWork.Honor\EasyWork\bin\Release"
set "BASE_PATH=\\***********\Backup\publish\dog"
set "TARGET=%BASE_PATH%\versions\v%VERSION%"
set "CURRENT=%BASE_PATH%\current"
set "USERNAME=it"
set "PASSWORD=123456"
set "PACKAGE_NAME=EasyWork.Honor.%VERSION%.nupkg"
REM ===========================

echo [INFO] Starting publish process for v%VERSION%

echo [STEP1] Validate source directory...
if not exist "%SOURCE%" (
    echo [ERROR] Source directory not found: %SOURCE%
    goto :end
)

echo [STEP2] Connect to NAS...
REM 断开所有到该服务器的现有连接，防止多重连接错误
net use \\***********\IPC$ /delete /y >nul 2>&1
net use \\***********\Backup /delete /y >nul 2>&1
net use "%BASE_PATH%" /delete /y >nul 2>&1
net use * /delete /y >nul 2>&1

REM 等待网络重置
ping 127.0.0.1 -n 2 >nul

REM 重新连接到NAS
net use "%BASE_PATH%" /user:%USERNAME% %PASSWORD% /persistent:no
if errorlevel 1 (
    echo [ERROR] NAS connection failed
    echo [INFO] Trying alternative connection method...
    REM 尝试连接到根共享
    net use \\***********\Backup /user:%USERNAME% %PASSWORD% /persistent:no >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Alternative connection also failed
        goto :end
    )
)

echo [STEP3] Create version directory...
mkdir "%TARGET%" >nul 2>&1
if not exist "%TARGET%" (
    echo [ERROR] Failed to create version directory
    goto :cleanup
)

echo [STEP4] Copy essential files...
REM 检查源文件是否存在
if not exist "%SOURCE%\*.nupkg" (
    echo [WARNING] No .nupkg files found in source directory
    echo [INFO] Available files in source:
    dir "%SOURCE%" /b 2>nul | findstr /i "\.nupkg \.exe RELEASES" 2>nul
    if errorlevel 1 echo [INFO] No matching files found
)

REM 复制文件，允许部分失败
robocopy "%SOURCE%" "%TARGET%" "*.nupkg" "Setup.exe" "RELEASES" /NFL /NDL /NJH /NJS /R:3 /W:1
set COPY_RESULT=%errorlevel%
echo [INFO] Copy result code: %COPY_RESULT%
if %COPY_RESULT% geq 8 (
    echo [ERROR] Critical file copy error
    goto :cleanup
)

echo [STEP5] Generate RELEASES file...
pushd "%TARGET%"
del RELEASES 2>nul

REM 检查是否有任何 .nupkg 文件
dir *.nupkg /b >nul 2>&1
if errorlevel 1 (
    echo [ERROR] No .nupkg files found in target directory
    echo [INFO] Cannot generate RELEASES file without package files
    goto :cleanup_nas
)

REM 如果指定的包文件存在，使用它
if exist "%PACKAGE_NAME%" (
    echo [INFO] Using expected package: %PACKAGE_NAME%
    goto :found_package
)

REM 否则使用第一个找到的 .nupkg 文件
echo [WARNING] Expected package file missing: %PACKAGE_NAME%
echo [INFO] Looking for alternative .nupkg files...
for %%f in (*.nupkg) do (
    set "PACKAGE_NAME=%%f"
    echo [INFO] Using alternative package: %%f
    goto :found_package
)

:found_package
echo [INFO] Generating RELEASES file for: %PACKAGE_NAME%
certutil -hashfile "%PACKAGE_NAME%" SHA1 > tmp.hash
if errorlevel 1 (
    echo [ERROR] Failed to generate hash for %PACKAGE_NAME%
    goto :cleanup_nas
)

for /f "tokens=2 delims= " %%a in ('findstr /v ":" tmp.hash ^| findstr /v "CertUtil"') do (
    set "SHA1=%%a"
)
call :get_file_size "%PACKAGE_NAME%"
echo !SHA1! %PACKAGE_NAME% !FILE_SIZE! > RELEASES
del tmp.hash >nul 2>&1
echo [INFO] RELEASES file generated successfully

echo [STEP6] Update current version links...
REM 确保 current 目录存在
if not exist "%CURRENT%" mkdir "%CURRENT%" >nul 2>&1

REM 删除旧的符号链接
del "%CURRENT%\*.nupkg" >nul 2>&1
del "%CURRENT%\Setup.exe" >nul 2>&1
del "%CURRENT%\RELEASES" >nul 2>&1

REM 创建新的符号链接
if exist "%TARGET%\%PACKAGE_NAME%" (
    mklink "%CURRENT%\%PACKAGE_NAME%" "%TARGET%\%PACKAGE_NAME%" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for %PACKAGE_NAME%, copying file instead
        copy "%TARGET%\%PACKAGE_NAME%" "%CURRENT%\" >nul 2>&1
    )
)

if exist "%TARGET%\Setup.exe" (
    mklink "%CURRENT%\Setup.exe" "%TARGET%\Setup.exe" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for Setup.exe, copying file instead
        copy "%TARGET%\Setup.exe" "%CURRENT%\" >nul 2>&1
    )
)

if exist "%TARGET%\RELEASES" (
    mklink "%CURRENT%\RELEASES" "%TARGET%\RELEASES" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to create symlink for RELEASES, copying file instead
        copy "%TARGET%\RELEASES" "%CURRENT%\" >nul 2>&1
    )
)

echo [INFO] Current version links updated to v%VERSION%

:cleanup_nas
popd
echo [SUCCESS] Publish completed!

:cleanup
net use "%BASE_PATH%" /delete /y >nul 2>&1

:get_file_size
set "FILE_SIZE=0"
for %%A in ("%~1") do set "FILE_SIZE=%%~zA"
goto :eof

:end
echo Press any key to exit...
pause >nul

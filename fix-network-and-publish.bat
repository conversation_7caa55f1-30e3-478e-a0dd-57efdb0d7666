@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo [INFO] Fix network and publish...

echo [STEP1] Disconnect all connections to ***********...
net use * /delete /y >nul 2>&1
net use \\***********\IPC$ /delete /y >nul 2>&1
net use \\***********\Backup /delete /y >nul 2>&1

echo [STEP2] Wait for network reset...
ping 127.0.0.1 -n 3 >nul

echo [STEP3] Run publish script...
call publish.bat

echo [DONE] Fix and publish completed
pause

﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EasyWork.Honor.ShowMsg
{
    public partial class HHT : Form
    {
        public HHT()
        {
            InitializeComponent();
            TopMost = true;
            Thread th = new Thread(Go);
            th.Start();
        }

        void Go()
        {
            Thread.Sleep(2000);
            for (int i = 0; i <= this.Height; i++)
            {
                Point p = new Point(this.Location.X, this.Location.Y + i);//弹出框向下移动消失
                PointToScreen(p);//即时转换成屏幕坐标
                Location = p;// new Point(this.Location.X, this.Location.Y + 1);
                Thread.Sleep(10);//下降速度调节，数字越小消失的速度越快，建议不大于10
            }
            Close();
            Dispose();
        }

        private void HHT_Load(object sender, EventArgs e)
        {

        }
    }
}
